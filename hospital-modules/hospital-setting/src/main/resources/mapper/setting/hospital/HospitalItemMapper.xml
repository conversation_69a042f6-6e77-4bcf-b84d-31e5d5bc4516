<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.puree.hospital.setting.mapper.hospital.HospitalItemMapper">

    <resultMap id="BaseResultMap" type="com.puree.hospital.setting.domain.entity.hospital.HospitalItem">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="templateId" column="template_id" jdbcType="INTEGER"/>
            <result property="hospitalId" column="hospital_id" jdbcType="BIGINT"/>
            <result property="itemValue" column="item_value" jdbcType="VARCHAR"/>
            <result property="revision" column="revision" jdbcType="SMALLINT"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="isDelete" column="is_delete" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,template_id,hospital_id,
        item_value,revision,create_by,
        create_time,update_by,update_time,
        is_delete
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from cfg_hospital_item
        where  id = #{id,jdbcType=BIGINT}
    </select>
    <select id="selectByTemplateIdAndHospitalId" resultType="com.puree.hospital.setting.domain.entity.hospital.HospitalItem">
        select
        <include refid="Base_Column_List" /> from cfg_hospital_item
        where  template_id = #{templateId,jdbcType=INTEGER}
        <if test="hospitalId != null">
            and hospital_id = #{hospitalId,jdbcType=BIGINT}
        </if>

    </select>

    <select id="selectByTemplateId" resultType="com.puree.hospital.setting.domain.entity.hospital.HospitalItem">
        select
        <include refid="Base_Column_List" /> from cfg_hospital_item
        where  template_id = #{templateId,jdbcType=INTEGER}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from cfg_hospital_item
        where  id = #{id,jdbcType=BIGINT} 
    </delete>
    <delete id="deleteByTemplateId">
        delete from cfg_hospital_item where template_id = #{templateId,jdbcType=INTEGER}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.puree.hospital.setting.domain.entity.hospital.HospitalItem" useGeneratedKeys="true">
        insert into cfg_hospital_item
        ( id,template_id,hospital_id
        ,item_value,revision,create_by
        ,create_time,update_by,update_time
        ,is_delete)
        values (#{id,jdbcType=BIGINT},#{templateId,jdbcType=INTEGER},#{hospitalId,jdbcType=BIGINT}
        ,#{itemValue,jdbcType=VARCHAR},#{revision,jdbcType=SMALLINT},#{createBy,jdbcType=VARCHAR}
        ,#{createTime,jdbcType=TIMESTAMP},#{updateBy,jdbcType=VARCHAR},#{updateTime,jdbcType=TIMESTAMP}
        ,#{isDelete,jdbcType=TINYINT})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.puree.hospital.setting.domain.entity.hospital.HospitalItem" useGeneratedKeys="true">
        insert into cfg_hospital_item
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">id,</if>
                <if test="templateId != null">template_id,</if>
                <if test="hospitalId != null">hospital_id,</if>
                <if test="itemValue != null">item_value,</if>
                <if test="revision != null">revision,</if>
                <if test="createBy != null">create_by,</if>
                <if test="createTime != null">create_time,</if>
                <if test="updateBy != null">update_by,</if>
                <if test="updateTime != null">update_time,</if>
                <if test="isDelete != null">is_delete,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id,jdbcType=BIGINT},</if>
                <if test="templateId != null">#{templateId,jdbcType=INTEGER},</if>
                <if test="hospitalId != null">#{hospitalId,jdbcType=BIGINT},</if>
                <if test="itemValue != null">#{itemValue,jdbcType=VARCHAR},</if>
                <if test="revision != null">#{revision,jdbcType=SMALLINT},</if>
                <if test="createBy != null">#{createBy,jdbcType=VARCHAR},</if>
                <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
                <if test="updateBy != null">#{updateBy,jdbcType=VARCHAR},</if>
                <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
                <if test="isDelete != null">#{isDelete,jdbcType=TINYINT},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.puree.hospital.setting.domain.entity.hospital.HospitalItem">
        update cfg_hospital_item
        <set>
                <if test="templateId != null">
                    template_id = #{templateId,jdbcType=INTEGER},
                </if>
                <if test="hospitalId != null">
                    hospital_id = #{hospitalId,jdbcType=BIGINT},
                </if>
                <if test="itemValue != null">
                    item_value = #{itemValue,jdbcType=VARCHAR},
                </if>
                <if test="revision != null">
                    revision = #{revision,jdbcType=SMALLINT},
                </if>
                <if test="createBy != null">
                    create_by = #{createBy,jdbcType=VARCHAR},
                </if>
                <if test="createTime != null">
                    create_time = #{createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="updateBy != null">
                    update_by = #{updateBy,jdbcType=VARCHAR},
                </if>
                <if test="updateTime != null">
                    update_time = #{updateTime,jdbcType=TIMESTAMP},
                </if>
                <if test="isDelete != null">
                    is_delete = #{isDelete,jdbcType=TINYINT},
                </if>
        </set>
        where   id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.puree.hospital.setting.domain.entity.hospital.HospitalItem">
        update cfg_hospital_item
        set
            template_id =  #{templateId,jdbcType=INTEGER},
            hospital_id =  #{hospitalId,jdbcType=BIGINT},
            item_value =  #{itemValue,jdbcType=VARCHAR},
            revision =  #{revision,jdbcType=SMALLINT},
            create_by =  #{createBy,jdbcType=VARCHAR},
            create_time =  #{createTime,jdbcType=TIMESTAMP},
            update_by =  #{updateBy,jdbcType=VARCHAR},
            update_time =  #{updateTime,jdbcType=TIMESTAMP},
            is_delete =  #{isDelete,jdbcType=TINYINT}
        where   id = #{id,jdbcType=BIGINT}
    </update>

    <select id="selectByTemplateIds" resultMap="BaseResultMap">
        select * from cfg_hospital_item
        where template_id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>
