<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.puree.hospital.setting.mapper.hospital.HospitalHistoryMapper">
    <resultMap type="com.puree.hospital.setting.domain.entity.hospital.HospitalHistory" id="CfgHospitalHistoryMap">
        <result property="id" column="id" jdbcType="BIGINT"/>
        <result property="hospitalId" column="hospital_id" jdbcType="VARCHAR"/>
        <result property="templateId" column="template_id" jdbcType="VARCHAR"/>
        <result property="itemKey" column="item_key" jdbcType="BIGINT"/>
        <result property="previousValue" column="previous_value" jdbcType="INTEGER"/>
        <result property="updatedValue" column="updated_value" jdbcType="VARCHAR"/>
        <result property="version" column="version" jdbcType="INTEGER"/>
        <result property="createBy" column="create_by" jdbcType="BIGINT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>
    <insert id="insert">
        insert into cfg_hospital_history
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="hospitalHistory.hospitalId != null">hospital_id,</if>
            <if test="hospitalHistory.templateId != null">template_id,</if>
            <if test="hospitalHistory.itemKey != null">item_key,</if>
            <if test="hospitalHistory.previousValue != null">previous_value,</if>
            <if test="hospitalHistory.updatedValue != null">updated_value,</if>
            <if test="hospitalHistory.version != null">version,</if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="hospitalHistory.hospitalId != null">#{hospitalHistory.hospitalId},</if>
            <if test="hospitalHistory.templateId != null">#{hospitalHistory.templateId},</if>
            <if test="hospitalHistory.itemKey != null">#{hospitalHistory.itemKey},</if>
            <if test="hospitalHistory.previousValue != null">#{hospitalHistory.previousValue},</if>
            <if test="hospitalHistory.updatedValue != null">#{hospitalHistory.updatedValue},</if>
            <if test="hospitalHistory.version != null">#{hospitalHistory.version},</if>
        </trim>
    </insert>

    <select id="queryByItemIdAndHospitalId" resultType="com.puree.hospital.setting.domain.entity.hospital.HospitalHistory">
        select
        *
        from cfg_hospital_history
        where template_id = #{templateId} and hospital_id = #{hospitalId}
    </select>
    <select id="queryByKeyCount" resultType="java.lang.Long">
        select
        count(*)
        from cfg_hospital_history
        <where>
            `hospital_id` = #{hospitalId}
            <if test="itemsKey != null and itemsKey != ''">
                and
                (`item_key` like concat('%',#{itemsKey},'%'))
            </if>
        </where>
    </select>
    <select id="queryByKey" resultType="com.puree.hospital.setting.domain.entity.hospital.HospitalHistory">
        select
        id,hospital_id,template_id,item_key,previous_value,updated_value,version,create_by,create_time,update_by,update_time
        from cfg_hospital_history
        <where>
            `hospital_id` = #{hospitalId}
            <if test="itemsKey != null and itemsKey != ''">
                and
                (`item_key` like concat('%',#{itemsKey},'%'))
            </if>
        </where>
        ORDER BY id DESC
        limit #{pageNum},#{pageSize}
    </select>

</mapper>