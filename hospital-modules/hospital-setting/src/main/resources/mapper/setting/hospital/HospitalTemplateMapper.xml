<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.puree.hospital.setting.mapper.hospital.HospitalTemplateMapper">
    <resultMap type="com.puree.hospital.setting.domain.entity.hospital.HospitalTemplate" id="CfgHospitalTemplateMap">
        <result property="id" column="id" jdbcType="BIGINT"/>
        <result property="key" column="key" jdbcType="VARCHAR"/>
        <result property="menuId" column="menu_id" jdbcType="BIGINT"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="sort" column="sort" jdbcType="INTEGER"/>
        <result property="type" column="type" jdbcType="INTEGER"/>
        <result property="restraint" column="restraint" jdbcType="LONGVARCHAR"/>
        <result property="enableHttp" column="enable_http" jdbcType="TINYINT"/>
        <result property="enableCache" column="enable_cache" jdbcType="TINYINT"/>
        <result property="cacheMinutes" column="cache_minutes" jdbcType="INTEGER"/>
        <result property="allowAnonymous" column="allow_anonymous" jdbcType="TINYINT"/>
        <result property="contentType" column="content_type" jdbcType="VARCHAR"/>
        <result property="allowedRoles" column="allowed_roles" jdbcType="VARCHAR"/>
        <result property="whiteList" column="white_list" jdbcType="VARCHAR"/>
        <result property="defaultValue" column="default_value" jdbcType="LONGVARCHAR"/>
        <result property="revision" column="revision" jdbcType="INTEGER"/>
        <result property="createBy" column="create_by" jdbcType="BIGINT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="isDelete" column="is_delete" jdbcType="TINYINT"/>
        <result property="isWrap" column="is_wrap" jdbcType="TINYINT"/>
        <result property="filterMode" column="filter_mode" jdbcType="VARCHAR"/>
        <result property="filterList" column="filter_list" jdbcType="LONGVARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,`key`,menu_id,`name`,remark,sort,`type`,restraint,
        enable_http,enable_cache,cache_minutes,allow_anonymous,content_type,
        allowed_roles,white_list,default_value,
        revision,create_by,create_time,update_by,update_time,is_delete,is_wrap,
        filter_mode,filter_list
    </sql>

    <sql id="Base_Select_Column_List">
        id,`key`,menu_id,`name`,remark,sort,`type`+0 as `type`,restraint,
        enable_http,enable_cache,cache_minutes,allow_anonymous,content_type,
        allowed_roles,white_list,default_value,
        revision,create_by,create_time,update_by,update_time,is_delete,is_wrap,
        filter_mode,filter_list
    </sql>

    <insert id="save" keyProperty="id" useGeneratedKeys="true">
        insert into cfg_hospital_template(
        <include refid="Base_Column_List"/>
        )
        values (#{id}, #{key}, #{menuId}, #{name}, #{remark}, #{sort}, #{type}, #{restraint},
        #{enableHttp},#{enableCache},#{cacheMinutes},#{allowAnonymous},#{contentType},
        #{allowedRoles},#{whiteList},#{defaultValue},
        #{revision},#{createBy},#{createTime},#{updateBy},#{updateTime},0,#{isWrap},#{filterMode},#{filterList})
    </insert>
    <insert id="insertItems" useGeneratedKeys="true" keyProperty="hospitalItem.id" >
        insert into cfg_hospital_item(
        template_id,hospital_id,item_value,revision,is_delete,create_by,update_by)
        values (#{hospitalItem.id},#{hospitalId},#{hospitalItem.itemValue},#{hospitalItem.itemRevision},0,#{hospitalItem.createBy},#{hospitalItem.updateBy})
    </insert>
    <update id="update">
        update cfg_hospital_template
        <set>
            <if test="hospitalItem.key != null">`key` = #{hospitalItem.key},</if>
            <if test="hospitalItem.menuId != null">menu_id = #{hospitalItem.menuId},</if>
            <if test="hospitalItem.name != null">`name` = #{hospitalItem.name},</if>
            <if test="hospitalItem.remark != null">remark = #{hospitalItem.remark},</if>
            <if test="hospitalItem.sort != null">sort = #{hospitalItem.sort},</if>
            <if test="hospitalItem.type != null">`type` = #{hospitalItem.type},</if>
            <if test="hospitalItem.restraint != null">restraint = #{hospitalItem.restraint},</if>
            <if test="hospitalItem.enableHttp != null">enable_http = #{hospitalItem.enableHttp},</if>
            <if test="hospitalItem.enableCache != null">enable_cache = #{hospitalItem.enableCache},</if>
            <if test="hospitalItem.cacheMinutes != null">cache_minutes = #{hospitalItem.cacheMinutes},</if>
            <if test="hospitalItem.allowAnonymous != null">allow_anonymous = #{hospitalItem.allowAnonymous},</if>
            <if test="hospitalItem.contentType != null">content_type = #{hospitalItem.contentType},</if>
            <if test="hospitalItem.allowedRoles != null">allowed_roles = #{hospitalItem.allowedRoles},</if>
            <if test="hospitalItem.whiteList != null">white_list = #{hospitalItem.whiteList},</if>
            <if test="hospitalItem.defaultValue != null">default_value = #{hospitalItem.defaultValue},</if>
            <if test="hospitalItem.revision != null">revision = revision + 1,</if>
            <if test="hospitalItem.isWrap != null">is_wrap = #{hospitalItem.isWrap},</if>
            <if test="hospitalItem.filterMode != null">filter_mode = #{hospitalItem.filterMode},</if>
            <if test="hospitalItem.filterList != null">filter_list = #{hospitalItem.filterList},</if>
        </set>
        where id = #{hospitalItem.id} and revision = #{revision} and is_delete = 0
    </update>
    <update id="updateItems">
        update cfg_hospital_item
        set item_value = #{item.itemValue},
        revision = revision + 1,
        update_by = #{item.updateBy}
        where template_id = #{item.id} and hospital_id = #{hospitalId} and revision = #{item.itemRevision}
    </update>
    <update id="physicalDeleteByIdAndItemsName">
        delete from cfg_hospital_template where id = #{id} and `name` = #{itemsName}
    </update>
    <update id="deleteByIdAndItemsName">
        update cfg_hospital_template set is_delete=1,update_by = #{username} where id = #{id} and `name` = #{itemsName} and is_delete = 0
    </update>
    <update id="updateSortForward">
        UPDATE cfg_hospital_template
        SET sort = sort - 1
        WHERE menu_id = #{menuId}
          and sort &lt;= #{sort}
          and is_delete = 0
    </update>
    <update id="updateSortMoveBack">
        UPDATE cfg_hospital_template
        SET sort = sort + 1
        WHERE menu_id = #{menuId}
          and sort &gt;= #{sort}
          and is_delete = 0
    </update>
    <update id="updateSort">
        UPDATE cfg_hospital_template
        SET sort = #{sort}
        WHERE id = #{id} and is_delete = 0
    </update>
    <update id="resetSort">
        UPDATE cfg_hospital_template AS eci
            INNER JOIN (
                SELECT id AS bid,
                       row_number() over ( ORDER BY sort ASC,update_time DESC) `rank`
                FROM cfg_hospital_template
                WHERE menu_id = #{menuId} and is_delete = 0
            ) AS seq
            ON eci.id = seq.bid
        SET eci.sort = seq.`rank`
    </update>
    <update id="updateDefaultValue">
        update   cfg_hospital_template
        set     default_value = #{defaultValue},
                update_by = #{updateBy},
                update_time = #{updateTime}
        where   id = #{id}
    </update>


    <select id="childItemsCount" resultType="java.lang.Long">
        select count(1)
        from cfg_hospital_template
        where menu_id = #{menuId}
        and is_delete = 0
    </select>
    <select id="count" resultType="java.lang.Long">
        select count(1)
        from cfg_hospital_template
        <where>
            <if test="key != null and key != '' and name != null and name != ''">
                and (`key` = #{key} or `name` = #{name})
            </if>
            <if test="key == null and name != null and name != ''">
                and `name` = #{name}
            </if>
            <if test="id != null">
                and `id` != #{id}
            </if>
            and is_delete = 0
        </where>
    </select>
    <select id="countItems" resultType="java.lang.Integer">
        select count(1)
        from cfg_hospital_item
        where  hospital_id = #{template.hospitalId} and is_delete = 0
        <if test="template.id != null">
            and template_id = #{template.id}
        </if>
    </select>
    <select id="query" resultType="com.puree.hospital.setting.domain.entity.hospital.HospitalTemplate">
        select
        <include refid="Base_Select_Column_List" />
        from cfg_hospital_template
        <where>
            is_delete = 0
            <if test="template.id != null">
                and id = #{template.id}
                and revision = #{template.revision}
            </if>
            <if test="template.key != null and template.key != ''">
                and `key` = #{template.key}
            </if>
        </where>
    </select>
    <select id="list" resultType="com.puree.hospital.setting.domain.entity.hospital.HospitalTemplate">
        select
        <include refid="Base_Select_Column_List" />
        from cfg_hospital_template
        where
        1=1
        <if test="blurSearch != null and blurSearch != ''">
            and
            (`key` like concat('%',#{blurSearch},'%') or
            `remark` like concat('%',#{blurSearch},'%') or
            `name` like concat('%',#{blurSearch},'%'))
        </if>
        and is_delete = 0
        ORDER BY menu_id,sort ASC
    </select>
    <select id="getById" resultType="com.puree.hospital.setting.domain.entity.hospital.HospitalTemplate">
        select
        <include refid="Base_Select_Column_List" />
        from cfg_hospital_template
        where id = #{id} and is_delete = #{isDelete}
    </select>
    <select id="itemsList" resultType="com.puree.hospital.setting.domain.entity.hospital.HospitalTemplate">
        select ct.id,`key`,menu_id,`name`,remark,sort,`type`+0 as `type`,restraint,ct.revision,
        ct.cache_minutes,
               enable_http,enable_cache,allow_anonymous,content_type,
               allowed_roles,white_list,default_value,
               ci.revision as itemRevision,ci.create_by,IF(ci.create_time IS NULL,ct.create_time,ci.create_time) as create_time,ci.update_by,
               ci.update_time,ct.is_delete,ci.item_value as itemValue,ci.hospital_id as hospitalId,ci.is_delete,
               ct.filter_mode,ct.filter_list
        from cfg_hospital_template ct
                 LEFT JOIN cfg_hospital_item ci
                           on ct.id=ci.template_id and ci.hospital_id = #{hospitalId}
        where ct.is_delete = '0'
        <if test="blurSearch != null and blurSearch != ''">
        and
        (`key` like concat('%',#{blurSearch},'%') or
        `remark` like concat('%',#{blurSearch},'%') or
        `name` like concat('%',#{blurSearch},'%'))
        </if>
        ORDER BY menu_id,sort ASC
    </select>
    <select id="queryItem" resultType="com.puree.hospital.setting.domain.entity.hospital.HospitalTemplate">
        select
        ct.id,ct.`key`,ct.menu_id,ct.`name`,ct.remark,ct.sort,ct.`type`+0 as `type`,ct.restraint,ct.`cache_minutes`,
        ct.enable_http,ct.enable_cache,ct.allow_anonymous,ct.content_type,
        ct.allowed_roles,ct.white_list,ct.default_value,
        ct.revision,ct.create_by,ci.create_time,ct.update_by,ct.update_time,ct.is_delete,ci.revision as itemRevision,ci.item_value as itemValue
        from cfg_hospital_template ct left join cfg_hospital_item ci on ct.id = ci.template_id
        <where>
        ct.is_delete = 0
        and ci.is_delete = 0
        and ci.hospital_id = #{template.hospitalId}
        <if test="template.id != null">
             and ct.id = #{template.id} and ci.revision = #{template.itemRevision}
         </if>
        <if test="template.key != null and template.key != ''" >
             and ct.`key` = #{template.key}
        </if>
        </where>
    </select>
    <select id="getTemplate"
            resultType="com.puree.hospital.setting.domain.entity.hospital.HospitalTemplate">
            SELECT
        <include refid="Base_Select_Column_List" />
        FROM `cfg_hospital_template` where `key`=#{key} and is_delete = 0
    </select>
    <select id="selectTemplateList"
            resultType="com.puree.hospital.setting.domain.entity.hospital.HospitalTemplate">
        select ct.id,ct.`key`, ct.`name`, ct.`type`+0 as `type`, ct.remark, ct.white_list,ct.allow_anonymous,ct.allowed_roles,ct.enable_http,ci.item_value,ct.default_value,ct.content_type,
        ct.revision,ct.create_by,ct.create_time,ct.update_by,ct.update_time,ct.is_delete,ct.default_value
        from cfg_hospital_template ct left join cfg_hospital_item ci on ct.id = ci.template_id and ci.hospital_id = #{hospitalId}
        where 1=1
          <if test="menuId != null and menuId != ''">
              and ct.menu_id = #{menuId}
          </if>
        <if test="isDeleted != null and isDeleted != ''">
            and ct.is_delete = #{isDeleted}
        </if>
        order by ct.create_time desc
    </select>

    <select id="getCacheMinutesTemplateList" resultMap="CfgHospitalTemplateMap">
        select
        <include refid="Base_Select_Column_List" />
        from cfg_hospital_template
        where is_delete = 0
        and cache_minutes = #{cacheMinutes}
    </select>
    <select id="queryTemplateMaxSortById" resultType="java.lang.Integer">
        select IFNULL(MAX(sort),0) + 1 from cfg_hospital_template where menu_id = #{menuId} and is_delete = 0
    </select>

</mapper>