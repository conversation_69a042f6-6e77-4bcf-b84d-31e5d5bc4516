<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.puree.hospital.setting.mapper.platform.PlatformItemsMapper">
    <resultMap type="com.puree.hospital.setting.domain.entity.platform.PlatformItems" id="EasyConfigItemsMap">
        <result property="id" column="id" jdbcType="BIGINT"/>
        <result property="key" column="key" jdbcType="VARCHAR"/>
        <result property="menuId" column="menu_id" jdbcType="BIGINT"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="sort" column="sort" jdbcType="INTEGER"/>
        <result property="type" column="type" jdbcType="INTEGER"/>
        <result property="restraint" column="restraint" jdbcType="LONGVARCHAR"/>
        <result property="whetherOpenHttpApi" column="whether_open_http_api" jdbcType="TINYINT"/>
        <result property="anonymousAccess" column="anonymous_access" jdbcType="TINYINT"/>
        <result property="responseContentType" column="response_content_type" jdbcType="VARCHAR"/>
        <result property="accessibleRoles" column="accessible_roles" jdbcType="VARCHAR"/>
        <result property="pushConfigurationCenter" column="push_configuration_center" jdbcType="VARCHAR"/>
        <result property="defaultValue" column="default_value" jdbcType="LONGVARCHAR"/>
        <result property="value" column="value" jdbcType="LONGVARCHAR"/>
        <result property="revision" column="revision" jdbcType="INTEGER"/>
        <result property="createBy" column="create_by" jdbcType="BIGINT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
    </resultMap>
    <resultMap type="com.puree.hospital.setting.domain.entity.hospital.HospitalTemplate" id="CfgHospitalTemplateMap">
        <result property="id" column="id" jdbcType="BIGINT"/>
        <result property="key" column="key" jdbcType="VARCHAR"/>
        <result property="menuId" column="menu_id" jdbcType="BIGINT"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="sort" column="sort" jdbcType="INTEGER"/>
        <result property="type" column="type" jdbcType="VARCHAR"/>
        <result property="restraint" column="restraint" jdbcType="LONGVARCHAR"/>
        <result property="enableHttp" column="enable_http" jdbcType="TINYINT"/>
        <result property="enableCache" column="enable_cache" jdbcType="TINYINT"/>
        <result property="allowAnonymous" column="allow_anonymous" jdbcType="TINYINT"/>
        <result property="contentType" column="content_type" jdbcType="VARCHAR"/>
        <result property="allowedRoles" column="allowed_roles" jdbcType="VARCHAR"/>
        <result property="whiteList" column="white_list" jdbcType="VARCHAR"/>
        <result property="defaultValue" column="default_value" jdbcType="LONGVARCHAR"/>
        <result property="revision" column="revision" jdbcType="INTEGER"/>
        <result property="createBy" column="create_by" jdbcType="BIGINT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="isDelete" column="is_delete" jdbcType="TINYINT"/>
        <result property="isWrap" column="is_wrap" jdbcType="TINYINT"/>
    </resultMap>
    <sql id="Base_Column_List">
        id,`key`,menu_id,`name`,remark,sort,`type`,restraint,
        whether_open_http_api,anonymous_access,response_content_type,
        accessible_roles,push_configuration_center,default_value,`value`,
        revision,create_by,create_time,update_by,update_time,del_flag,is_wrap
    </sql>
    <sql id="Template_Column_List">
        id,`key`,menu_id,`name`,remark,sort,`type`,restraint,
        enable_http,enable_cache,allow_anonymous,content_type,
        allowed_roles,white_list,default_value,
        revision,create_by,create_time,update_by,update_time,is_delete
    </sql>
    <delete id="physicalDeleteByIdAndItemsName">
        delete from easy_config_items where id = #{id} and `name` = #{itemsName}
    </delete>
    <select id="queryItem" resultMap="EasyConfigItemsMap">
        select
        <include refid="Base_Column_List"/>
        from easy_config_items
        where
        del_flag = 0
        <if test="item.id != null and item.revision != null">
            and id = #{item.id}
            and revision = #{item.revision}
        </if>
        <if test="item.key != null and item.key != ''">
            and `key` = #{item.key}
        </if>
    </select>
    <!-- 通过 ID  查询单条数据 -->
    <select id="queryById" resultMap="EasyConfigItemsMap">
        select
        <include refid="Base_Column_List" />
        from easy_config_items
        where id = #{id}
        and del_flag = #{isDelete}
    </select>

    <!-- 通过 key  查询单条数据 -->
    <select id="getByKey" resultMap="EasyConfigItemsMap">
        select
        <include refid="Base_Column_List" />
        from easy_config_items
        where `key` = #{key}
        and del_flag = 0
    </select>

    <!-- 模糊查询数据 -->
    <select id="blurSearch" resultMap="EasyConfigItemsMap">
        select
        <include refid="Base_Column_List" />
        from easy_config_items
        where
        1=1
        <if test="blurSearch != null and blurSearch != ''">
            and
            (`key` like concat('%',#{blurSearch},'%') or
            `remark` like concat('%',#{blurSearch},'%') or
            `name` like concat('%',#{blurSearch},'%'))
        </if>

        and del_flag = 0
        ORDER BY menu_id,sort ASC
    </select>



    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        select count(1)
        from easy_config_items
        <where>
            <if test="key != null and key != '' and name != null and name != ''">
                and (`key` = #{key} or `name` = #{name})
            </if>
            <if test="key == null and name != null and name != ''">
                and `name` = #{name}
            </if>
            <if test="id != null">
                and `id` != #{id}
            </if>
            and del_flag = 0
        </where>
    </select>

    <!--新增数据-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into easy_config_items(
        <include refid="Base_Column_List"/>
        )
        values (#{id}, #{key}, #{menuId}, #{name}, #{remark}, #{sort}, #{type}, #{restraint}, #{whetherOpenHttpApi},
        #{anonymousAccess}, #{responseContentType}, #{accessibleRoles}, #{pushConfigurationCenter},
        #{defaultValue}, #{value}, #{revision}, #{createBy}, #{createTime}, #{updateBy}, #{updateTime},0,#{isWrap})
    </insert>
    <!-- 更新模版数据 -->
    <update id="updateTemplate">
        update easy_config_items
        <set>
            <if test="t.name != null and t.name != ''">
                `name` = #{t.name},
            </if>
            <if test="t.remark != null ">
                remark = #{t.remark},
            </if>
            <if test="t.type != null">
                `type` = #{t.type},
            </if>
            <if test="t.restraint != null and t.restraint != ''">
                restraint = #{t.restraint},
            </if>
            <if test="t.whetherOpenHttpApi != null">
                whether_open_http_api = #{t.whetherOpenHttpApi},
            </if>
            <if test="t.anonymousAccess != null">
                anonymous_access = #{t.anonymousAccess},
            </if>
            <if test="t.responseContentType != null">
                response_content_type = #{t.responseContentType},
            </if>
            <if test="t.accessibleRoles != null ">
                accessible_roles = #{t.accessibleRoles},
            </if>
            <if test="t.pushConfigurationCenter != null ">
                push_configuration_center = #{t.pushConfigurationCenter},
            </if>
            <if test="t.defaultValue != null ">
                default_value = #{t.defaultValue},
            </if>
            <if test="t.revision != null">
                revision = #{t.revision} + 1,
            </if>
            <if test="t.updateBy != null and t.updateBy != ''">
                update_by = #{t.updateBy},
            </if>
            <if test="t.updateTime != null">
                update_time = #{t.updateTime},
            </if>
            <if test="t.isWrap != null">
                is_wrap = #{t.isWrap},
            </if>
        </set>
        where
        del_flag = 0
        <if test="t.id != null and sourceRevision != null">
           and id = #{t.id} and revision = #{sourceRevision}
        </if>
        <if test="t.key != null and t.key != ''">
            and `key` = #{t.key}
        </if>

    </update>

    <!-- 更新数据 -->
    <update id="updateValue">
        update easy_config_items
        <set>
            `value` = #{t.value},
            update_time = #{t.updateTime},
        </set>
        where
        del_flag = 0
        <if test="t.id != null and sourceRevision != null">
            and id = #{t.id} and revision = #{sourceRevision}
        </if>
        <if test="t.key != null and t.key != ''">
            and `key` = #{t.key}
        </if>

    </update>

    <!--通过主键删除-->
    <update id="deleteByIdAndItemsName">
        update easy_config_items set del_flag=1  where id = #{id} and `name` = #{itemsName} and del_flag = 0
    </update>

    <!-- 根据 sort 将值后移 -->
    <update id="updateSortMoveBack">
        UPDATE easy_config_items
        SET sort = sort + 1
        WHERE menu_id = #{menuId}
          and sort &gt;= #{sort}
          and del_flag = 0
    </update>

    <!-- 根据 sort 将值前移 -->
    <update id="updateSortForward">
        UPDATE easy_config_items
        SET sort = sort - 1
        WHERE menu_id = #{menuId}
          and sort &lt;= #{sort}
          and del_flag = 0
    </update>
    <!-- 根据 主键 修改 sort -->
    <update id="updateSort">
        UPDATE easy_config_items
        SET sort = #{sort}
        WHERE id = #{id} and del_flag = 0
    </update>
    <!-- 修改菜单ID -->
    <update id="updateMenuId">
        UPDATE easy_config_items
        SET sort = #{sort} , menu_id = #{menuId}
        WHERE id = #{id} and del_flag = 0
    </update>

    <!-- 通过 ID  查询菜单最大排序 -->
    <select id="queryMenuMaxSortById" resultType="int">
        select MAX(sort) from easy_config_items where menu_id = #{menuId} and del_flag = 0
    </select>

    <!--获取菜单下的配置项数-->
    <select id="getItems" resultMap="EasyConfigItemsMap">
        select ite.`key`,ite.`name`,ite.`remark`,ite.restraint,ite.id,ite.type,ite.revision,ite.create_by,ite.create_time,ite.update_by,ite.update_time,ite.default_value
        from easy_config_items as ite LEFT JOIN
        easy_config_menu as men ON ite.menu_id = men.id AND men.del_flag = 0
        where
        1=1
        <if test="menuKey != null and menuKey != ''">
            and menu_key = #{menuKey}
        </if>
        <if test="isDeleted != null and isDeleted != ''">
            and ite.del_flag = #{isDeleted}
        </if>
        order by ite.create_time desc
    </select>

    <!-- 重置排序 -->
    <select id="childItemsCount" resultType="java.lang.Long">
        select count(1)
        from easy_config_items
        where menu_id = #{menuId}
          and del_flag = 0
    </select>

    <update id="resetSort">
        UPDATE easy_config_items AS eci
            INNER JOIN (
            SELECT id AS bid,
            row_number() over ( ORDER BY sort ASC,update_time DESC) `rank`
            FROM easy_config_items
            WHERE menu_id = #{menuId} and del_flag = 0
            ) AS seq
        ON eci.id = seq.bid
            SET eci.sort = seq.`rank`
    </update>
    <update id="updateTemplateDefaultValue">
        update easy_config_items set default_value = #{defaultValue} , update_time = #{updateTime}
        where `id` = #{id} and del_flag = 0 and revision = #{revision}
    </update>

    <select id="getItemsByMenuId" resultMap="EasyConfigItemsMap">
        select `key`,`name`
        from easy_config_items
        where menu_id = #{menuId}
          and del_flag = 0
    </select>
    <select id="selectItemList"
            resultType="com.puree.hospital.setting.domain.entity.platform.PlatformItems">
        select `key`, `name`, `type`, remark, accessible_roles, anonymous_access, whether_open_http_api,response_content_type,value,default_value
        from easy_config_items
        where menu_id = #{menuId}
          and del_flag = 0
    </select>



</mapper>