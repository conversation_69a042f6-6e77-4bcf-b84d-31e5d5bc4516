<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.puree.hospital.setting.mapper.platform.PlatformPushBindMapper">
    <resultMap type="com.puree.hospital.setting.domain.entity.platform.PlatformPushBind" id="EasyConfigPushBindMap">
        <result property="id" column="id" jdbcType="BIGINT"/>
        <result property="itemId" column="item_id" jdbcType="BIGINT"/>
        <result property="pushName" column="push_name" jdbcType="VARCHAR"/>
        <result property="createBy" column="create_by" jdbcType="BIGINT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
    </resultMap>
    <!-- 获取绑定关系 -->
    <select id="getBindingRelationship" resultType="com.puree.hospital.setting.domain.entity.platform.PlatformItemsPushDo"
            parameterType="java.util.Collection">
        SELECT
        push_name,hospitalItem.*
        FROM
        easy_config_items hospitalItem
        LEFT JOIN easy_config_push_bind bind ON hospitalItem.id = bind.item_id
        WHERE
        bind.push_name IN
        <foreach collection="platformItems" item="hospitalItem" separator="," open="(" close=")">
            #{hospitalItem}
        </foreach>
        AND hospitalItem.del_flag = 0 AND bind.del_flag = 0
    </select>
    <select id="getBindingList"
            resultType="com.puree.hospital.setting.domain.entity.platform.PlatformPushBind">
        SELECT
        *
        FROM
        easy_config_push_bind
        WHERE
        item_id = #{id} AND del_flag = 0
    </select>

    <!--新增数据-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into easy_config_push_bind(item_id, push_name, create_by, create_time, update_by,
                                          update_time, del_flag)
        values (#{itemId}, #{pushName}, #{createBy}, #{createTime}, #{updateBy}, #{updateTime}, 0)
    </insert>



    <delete id="deleteBindingRelationship">
        delete from easy_config_push_bind where item_id = #{itemId}
    </delete>
</mapper>