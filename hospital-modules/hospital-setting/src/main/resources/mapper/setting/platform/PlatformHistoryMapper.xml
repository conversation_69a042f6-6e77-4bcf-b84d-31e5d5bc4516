<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.puree.hospital.setting.mapper.platform.PlatformHistoryMapper">
    <resultMap type="com.puree.hospital.setting.domain.entity.platform.PlatformHistory" id="EasyConfigHistoryMap">
        <result property="id" column="id" jdbcType="BIGINT"/>
        <result property="itemsId" column="items_id" jdbcType="BIGINT"/>
        <result property="menuId" column="menu_id" jdbcType="BIGINT"/>
        <result property="itemsKey" column="items_key" jdbcType="VARCHAR"/>
        <result property="type" column="type" jdbcType="INTEGER"/>
        <result property="sourceData" column="source_data" jdbcType="LONGVARCHAR"/>
        <result property="targetData" column="target_data" jdbcType="LONGVARCHAR"/>
        <result property="version" column="version" jdbcType="INTEGER"/>
        <result property="createBy" column="create_by" jdbcType="BIGINT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 通过数据项ID 查询历史数据 -->
    <select id="queryByItemId" resultMap="EasyConfigHistoryMap">
        select
            id,items_id,menu_id,items_key,type,source_data,target_data,version,create_by,create_time,update_by,update_time
        from easy_config_history
        where items_id = #{itemsId}
        ORDER BY id DESC
    </select>

    <!--新增数据-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into easy_config_history(items_id,menu_id,items_key,type,source_data,target_data,version,create_by,create_time,update_by,update_time)
        values (#{itemsId},#{menuId},#{itemsKey},#{type},#{sourceData},#{targetData},#{version},#{createBy},#{createTime},#{updateBy},#{updateTime})
    </insert>

    <!-- 重置版本号防止溢出 -->
    <insert id="resetVersion" keyProperty="id" useGeneratedKeys="true">
        insert into easy_config_history(id,version)
        values
        <foreach collection="histories" item="hospitalItem" separator=",">
            (#{hospitalItem.id},#{hospitalItem.version})
        </foreach>
        on duplicate key update
        id=values(id),
        version=values(version)
    </insert>


    <!-- 刷新历史表 -->
    <delete id="refresh">
        delete from easy_config_history where version &lt; #{version} and items_id = #{itemsId}
    </delete>


    <!-- 查询历史数据 -->
    <select id="list" resultMap="EasyConfigHistoryMap">
        select
            id,items_id,menu_id,items_key,type,source_data,target_data,version,create_by,create_time,update_by,update_time
        from easy_config_history
        ORDER BY id DESC
    </select>

    <select id="queryByKey" resultMap="EasyConfigHistoryMap">
        select
        id,items_id,menu_id,items_key,type,source_data,target_data,version,create_by,create_time,update_by,update_time
        from easy_config_history
        <where>
            <if test="blurSearch != null and blurSearch != ''">
                and
                (`items_key` like concat('%',#{blurSearch},'%'))
            </if>
            <if test="type != null">
                and
                (`type` = #{type})
            </if>
        </where>
        ORDER BY id DESC
        limit #{pageNum},#{pageSize}
    </select>

    <select id="queryByKeyCount" resultType="java.lang.Long">
        select
        count(*)
        from easy_config_history
        <where>
            <if test="blurSearch != null and blurSearch != ''">
                and
                (`items_key` like concat('%',#{blurSearch},'%'))
            </if>
            <if test="type != null">
                and
                (`type`  = #{type})
            </if>
        </where>
        ORDER BY id DESC
    </select>
</mapper>