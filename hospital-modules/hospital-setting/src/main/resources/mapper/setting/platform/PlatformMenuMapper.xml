<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.puree.hospital.setting.mapper.platform.PlatformMenuMapper">
    <resultMap type="com.puree.hospital.setting.domain.entity.Menu" id="EasyConfigMenuMap">
        <result property="id" column="id" jdbcType="BIGINT"/>
        <result property="menuName" column="menu_name" jdbcType="VARCHAR"/>
        <result property="menuKey" column="menu_key" jdbcType="VARCHAR"/>
        <result property="parentId" column="parent_id" jdbcType="BIGINT"/>
        <result property="sort" column="sort" jdbcType="INTEGER"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="revision" column="revision" jdbcType="INTEGER"/>
        <result property="createBy" column="create_by" jdbcType="BIGINT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="isDelete" column="del_flag" jdbcType="TINYINT"/>
        <result property="enableHttp" column="enable_http" jdbcType="TINYINT"/>
        <result property="allowAnonymous" column="allow_anonymous" jdbcType="TINYINT"/>
        <result property="allowedRoles" column="allowed_roles" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 通过ID查询单条数据 -->
    <select id="queryById" resultMap="EasyConfigMenuMap">
        select id,
               menu_name,
               menu_key,
               parent_id,
               sort,
               remark,
               revision,
               create_by,
               create_time,
               update_by,
               update_time,
               del_flag,
               enable_http,
               allow_anonymous,
               allowed_roles
        from easy_config_menu
        where id = #{id}
          and del_flag = 0
    </select>

    <!-- 获取所有菜单 -->
    <select id="list" resultMap="EasyConfigMenuMap">
        select id,
               menu_name,
               menu_key,
               parent_id,
               sort,
               remark,
               revision,
               create_by,
               create_time,
               update_by,
               update_time,
               del_flag,
               enable_http,
               allow_anonymous,
               allowed_roles
        from easy_config_menu
        where del_flag = 0
    </select>

    <!-- 统计行数 -->
    <select id="count" resultType="java.lang.Long">
        select count(1)
        from easy_config_menu
        <where>
            <if test="menuKey != null and menuKey != ''">
                and `menu_key` = #{menuKey}
            </if>
            <if test="menuName != null and menuName != ''">
                or `menu_name` = #{menuName}
            </if>
            and del_flag = 0
        </where>
    </select>

    <!-- 统计行数 -->
    <select id="queryByMenuNameAndKey" resultMap="EasyConfigMenuMap">
        select id,
        menu_name,
        menu_key,
        parent_id,
        sort,
        remark,
        revision,
        create_by,
        create_time,
        update_by,
        update_time,
        del_flag,
        enable_http,
        allow_anonymous,
        allowed_roles
        from easy_config_menu
        <where>
            <if test="menuKey != null and menuKey != ''">
                and `menu_key` = #{menuKey}
            </if>
            <if test="menuName != null and menuName != ''">
                or `menu_name` = #{menuName}
            </if>
            and del_flag = 0
        </where>
    </select>

    <select id="queryMenuMaxSortById" resultType="java.lang.Integer" parameterType="java.lang.Long">
        select max(sort)
        from easy_config_menu
        where parent_id = #{menuId}
          and del_flag = 0
    </select>

    <!-- 查询父 ID 下的所有节点 -->
    <select id="queryMenuSortByParentId" resultMap="EasyConfigMenuMap" parameterType="java.lang.Long">
        select *
        from easy_config_menu
        where parent_id = #{parentId}
          and del_flag = 0
    </select>


    <!--新增数据-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into easy_config_menu(id, menu_name, menu_key, parent_id, sort, remark, revision, create_by, create_time,
                                     update_by, update_time, del_flag, enable_http, allow_anonymous, allowed_roles)
        values (#{id}, #{menuName}, #{menuKey}, #{parentId}, #{sort}, #{remark}, #{revision}, #{createBy},
                #{createTime}, #{updateBy}, #{updateTime}, 0, #{enableHttp}, #{allowAnonymous}, #{allowedRoles})
    </insert>

    <!-- 更新数据 -->
    <update id="update">
        update easy_config_menu
        <set>
            <if test="menuName != null and menuName != ''">
                menu_name = #{menuName},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
            <if test="revision != null">
                revision = #{revision},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="enableHttp != null">
                enable_http = #{enableHttp},
            </if>
            <if test="allowAnonymous != null">
                allow_anonymous = #{allowAnonymous},
            </if>
            <if test="allowedRoles != null">
                allowed_roles = #{allowedRoles},
            </if>
        </set>
        where id = #{id} and del_flag = 0
    </update>

    <!--通过主键删除-->
    <update id="deleteByIdAndItemsName">
        update easy_config_menu
        set del_flag=1
        where id = #{id}
          and `menu_name` = #{menuName}
          and del_flag = 0
    </update>
    <!-- 根据 sort 将值后移 -->
    <update id="updateSortMoveBack">
        UPDATE easy_config_menu
        SET sort = sort + 1
        WHERE parent_id = #{id}
          and sort &gt;= #{sort}
          and del_flag = 0
    </update>

    <!-- 根据 sort 将值前移 -->
    <update id="updateSortForward">
        UPDATE easy_config_menu
        SET sort = sort - 1
        WHERE parent_id = #{id}
          and sort &lt;= #{sort}
          and del_flag = 0
    </update>

    <!-- 根据 主键 修改 sort -->
    <update id="updateSort">
        UPDATE easy_config_menu
        SET sort = #{sort},update_time = now()
        WHERE id = #{id}
          and del_flag = 0
    </update>
    <!-- 修改菜单ID -->
    <update id="updateMenuId">
        UPDATE easy_config_menu
        SET sort      = #{sort},
            parent_id = #{parentId}
        WHERE id = #{id}
          and del_flag = 0
    </update>

    <!-- 重置排序 -->
    <update id="resetSort">
        UPDATE easy_config_menu AS ecm
            INNER JOIN (
                SELECT id AS bid,
                    row_number() over ( ORDER BY sort ASC,update_time DESC) `rank`
                FROM easy_config_menu
                WHERE parent_id = #{parentId} and del_flag = 0
            ) AS seq
        ON ecm.id = seq.bid
            SET ecm.sort = seq.`rank`
    </update>


    <!-- 统计子节点个数 -->
    <select id="childNodeCount" resultType="java.lang.Long">
        select count(1)
        from easy_config_menu
        where `parent_id` = #{parentId}
          and del_flag = 0
    </select>
    <select id="queryMenu" resultMap="EasyConfigMenuMap">
        select id,
               menu_name,
               menu_key,
               parent_id,
               sort,
               remark,
               revision,
               create_by,
               create_time,
               update_by,
               update_time,
               del_flag,
               enable_http,
               allow_anonymous,
               allowed_roles
        from easy_config_menu
        where `menu_key` = #{menuKey}
          and del_flag = 0
    </select>


</mapper>