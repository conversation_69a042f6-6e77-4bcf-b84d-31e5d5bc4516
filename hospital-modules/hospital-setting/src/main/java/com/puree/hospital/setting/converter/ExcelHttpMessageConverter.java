package com.puree.hospital.setting.converter;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.fasterxml.jackson.databind.JsonNode;
import org.springframework.http.HttpInputMessage;
import org.springframework.http.HttpOutputMessage;
import org.springframework.http.MediaType;
import org.springframework.http.converter.AbstractHttpMessageConverter;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.http.converter.HttpMessageNotWritableException;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.List;

/**
 * 将JsonArray对象转换成Excel文件
 *
 * <AUTHOR>
 * @date 2024/12/31 16:56
 */
@Component
public class ExcelHttpMessageConverter extends AbstractHttpMessageConverter<JsonNode> {

    public static final MediaType EXCEL_TYPE = MediaType.valueOf("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");

    public ExcelHttpMessageConverter() {
        super(EXCEL_TYPE);
    }


    @Override
    public boolean canRead(@NonNull Class<?> clazz, MediaType mediaType) {
        return false;
    }

    @Override
    public boolean canWrite(@NonNull Class<?> clazz, MediaType mediaType) {
        //禁止未声明content-type的请求头，防止扩大转换范围
        return mediaType != null && super.canWrite(clazz, mediaType);
    }

    @Override
    protected boolean supports(@NonNull Class<?> clazz) {
        return JsonNode.class.isAssignableFrom(clazz);
    }

    @Override
    @NonNull
    protected JsonNode readInternal(@NonNull Class<? extends JsonNode> clazz, @NonNull HttpInputMessage inputMessage)
            throws HttpMessageNotReadableException {
        throw new HttpMessageNotReadableException("Reading JSON array from excel is not supported", inputMessage);
    }

    @Override
    protected void writeInternal(JsonNode jsonNode, HttpOutputMessage outputMessage)
            throws IOException, HttpMessageNotWritableException {
        OutputStream outputStream = outputMessage.getBody();
        ExcelWriterBuilder writerBuilder = EasyExcel.write(outputStream);

        // Create a list of lists to hold the data
        List<List<String>> data = new ArrayList<>();

        // Write header
        JsonNode firstObject = jsonNode.elements().next();
        List<String> header = new ArrayList<>();
        firstObject.fieldNames().forEachRemaining(header::add);
        data.add(header);

        // Write data rows
        for (JsonNode node : jsonNode) {
            List<String> row = new ArrayList<>();
            firstObject.fieldNames().forEachRemaining(fieldName -> row.add(node.get(fieldName).asText()));
            data.add(row);
        }

        // Write to Excel
        writerBuilder.sheet("Sheet1").doWrite(data);
    }
}