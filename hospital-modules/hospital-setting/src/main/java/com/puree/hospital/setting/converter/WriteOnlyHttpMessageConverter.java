package com.puree.hospital.setting.converter;


import org.springframework.http.HttpInputMessage;
import org.springframework.http.MediaType;
import org.springframework.http.converter.AbstractHttpMessageConverter;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;

import java.lang.reflect.ParameterizedType;

/**
 * 将JsonArray对象转换成CSV格式
 *
 * <AUTHOR>
 * @date 2024/12/30 19:42
 */
@SuppressWarnings("unused")
@Component
public abstract class WriteOnlyHttpMessageConverter<T> extends AbstractHttpMessageConverter<T> {

    @SuppressWarnings("unchecked")
    private final Class<T> type = (Class<T>) ((ParameterizedType) getClass()
            .getGenericSuperclass()).getActualTypeArguments()[0];

    protected WriteOnlyHttpMessageConverter(MediaType supportedMediaType) {
        super(supportedMediaType);
    }


    @Override
    public boolean canRead(@NonNull Class<?> clazz, MediaType mediaType) {
        return false;
    }

    @Override
    public boolean canWrite(@NonNull Class<?> clazz, MediaType mediaType) {
        //禁止未声明content-type的请求头，防止扩大转换范围
        return mediaType != null && super.canWrite(clazz, mediaType);
    }

    @Override
    protected boolean supports(@NonNull Class<?> clazz) {
        return type.isAssignableFrom(clazz);
    }

    @Override
    @NonNull
    protected T readInternal(@NonNull Class<? extends T> clazz, @NonNull HttpInputMessage inputMessage)
            throws HttpMessageNotReadableException {
        String msg = String.format("Reading %s from %s is not supported", type.getSimpleName(), inputMessage.getHeaders().getContentType());
        throw new HttpMessageNotReadableException(msg, inputMessage);
    }
}