package com.puree.hospital.setting.domain.entity.platform;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.puree.hospital.setting.domain.entity.Template;
import com.puree.hospital.setting.domain.entity.TemplateAuth;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 平台配置项元数据
 * <AUTHOR>
 * @date 2023/12/4 18:47
 */
@Data
public class PlatformItems implements TemplateAuth, Template, Serializable {
    /**
     * 主键
     */
    private Long id;
    /**
     * key
     */
    private String key;
    /**
     * 菜单类型ID
     */
    private Long menuId;
    /**
     * 名称
     */
    private String name;
    /**
     * 描述
     */
    private String remark;
    /**
     * 排序字段
     */
    private Integer sort;
    /**
     * 类型;前端定义值是多少
     * 0 单行文本
     * 1 多行文本
     * 2 富文本
     * 3 数字
     * 4 是/否
     * 5 选择项
     * 6 日期时间
     * 7 表格
     * 8 动态字典
     * 9 静态字典
     */
    private Integer type;
    /**
     * 约束
     */
    private String restraint;
    /**
     * 是否开启 http api 访问;0 为关闭，1为开启
     */
    private Boolean whetherOpenHttpApi;
    /**
     * 是否为匿名访问;0 为关闭，1为开启
     */
    private Boolean anonymousAccess;
    /**
     * 响应内容类型
     */
    private String responseContentType;
    /**
     * 可访问角色;角色与角色之间用,号隔开
     */
    private String accessibleRoles;
    /**
     * 推送的配置中心;文件名与文件名之间用,号隔开
     */
    private String pushConfigurationCenter;
    /**
     * 默认值
     */
    private String defaultValue;
    /**
     * 值;初始创建时为默认值
     */
    private String value;
    /**
     * 乐观锁
     */
    private Integer revision;
    /**
     * 创建人
     */
    private String createBy;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
     * 更新时间
     */
    private String updateBy;
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    /**
     * 删除标记;0未删除1已删除
     */
    private Boolean delFlag;

    /**
     * 是否对返回值进行 json包装
     */
    private Boolean isWrap;

    @Override
    public String getItemsValue() {
        return value;
    }

    @Override
    public String getAllowedRoles() {
        return accessibleRoles;
    }

    @Override
    public boolean isHttpEnabled() {
        return Boolean.TRUE.equals(whetherOpenHttpApi);
    }

    @Override
    public boolean isAnonymousEnabled() {
        return Boolean.TRUE.equals(anonymousAccess);
    }

    @Override
    public String getContentType() {
        return responseContentType;
    }

    @Override
    public Long getHospitalId() {
        return 0L;
    }


}