package com.puree.hospital.setting.service.hospital;
import com.puree.hospital.setting.domain.entity.hospital.HospitalItem;
import java.util.List;


/**
 * <p>
 * 医院配置项 service
 * </p>
 *
 * <AUTHOR>
 * @date 2024/10/22 15:37
 */
public interface HospitalItemsService {
    /**
     * @param templateId 模板id
     * @param hospitalId 医院id
     * @return {@link HospitalItem }
     */
    HospitalItem getHospitalItemByTemplateId(Long templateId, Long hospitalId);

    /**
     * @param templateId 模板 ID
     * @return List<HospitalItem>
     * @description 按模板 ID 搜索
     * <AUTHOR>
     * @date 2024/11/27 12:08
     **/
    List<HospitalItem> getHospitalItemByTemplateId(Long templateId);
    /**
     * @param templateId
     * @return HospitalItem
     * @description 根据模板 id 获取列表
     * <AUTHOR>
     * @date 2024/12/12 12:05
     **/
    List<HospitalItem> getItemListByTemplateId(Long templateId);
    /**
     * @param templateIds
     * @return List<HospitalItem>
     * @description 根据ids批量查询配置项
     * <AUTHOR>
     * @date 2024/12/12 12:15
     **/
    List<HospitalItem> getItemsByTemplateIds(List<Long> templateIds);

    /**
     *  通过模版id 删除配置项
     * @param templateId 模板id
     */
    void deleteByTemplateId(Long templateId);

    /**
     * 模版修改默认值时，批量更新配置项值
     * @param templateId 模版Id
     * @param defaultValue 模版默认值
     */
    void batchUpdateItemValueForDefaultValue(Long templateId, String defaultValue);
}
