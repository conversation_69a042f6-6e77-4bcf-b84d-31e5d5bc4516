package com.puree.hospital.setting.domain.dto.platform;

import com.puree.hospital.setting.constants.ItemTypeEnum;
import com.puree.hospital.setting.domain.dto.SettingChangeDTO;
import lombok.Data;

import javax.validation.constraints.*;
import java.io.Serializable;

/**
 * @ClassName EasyConfigItemsDevelopUpdateDto
 * <AUTHOR>
 * @Description 配置项 开发修改 dto
 * @Date 2023/12/7 12:32
 * @Version 1.0
 */
@Data
public class ItemsDevelopUpdateDto extends SettingChangeDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @NotNull(message = "主键 不能为空")
    private Long id;

    /**
     * key
     */
    private String key;
    /**
     * 名称
     * 修改时，未删除的数据中 名称 不能重复
     */
    @NotBlank(message = "名称 不能为空")
    @Size(max = 64, message = "参数 名称 不能超过 64 个字符")
    private String name;
    /**
     * 描述
     */
    @Size(max = 255, message = "参数 描述 不能超过 255 个字符")
    private String remark;
    /**
     * 类型;前端定义值是多少
     */
    @NotNull(message = "类型 不能为空")
    private Integer type;
    /**
     * 约束
     */
    @NotBlank(message = "约束 不能为空")
    private String restraint;
    /**
     * 是否开启 http api 访问;0 为关闭，1为开启
     */
    private Boolean whetherOpenHttpApi;
    /**
     * 是否为匿名访问;0 为关闭，1为开启
     */
    private Boolean anonymousAccess;
    /**
     * 响应内容类型
     */
    private String responseContentType;
    /**
     * 可访问角色;角色与角色之间用,号隔开
     */
    @Size(max = 1024, message = "参数 可访问角色 不能超过 1024 个字符")
    private String accessibleRoles;
    /**
     * 推送的配置中心;文件名与文件名之间用,号隔开
     */
    @Size(max = 1024, message = "参数 推送的配置中心 不能超过 1024 个字符")
    private String pushConfigurationCenter;


    /**
     * 乐观锁
     */
    @NotNull(message = "版本号不能为空")
    private Integer revision;
    /**
     * 返回值是否包装成ajax类型 只在返回类型为json时生效 1 包装 0 不包装
     */
    private Boolean isWrap;

    @Override
    public Long getHospitalId() {
        return null;
    }



    @Override
    public String getItemsValue() {
        return null;
    }

    @Override
    public String getDefaultValue() {
        return null;
    }
}
