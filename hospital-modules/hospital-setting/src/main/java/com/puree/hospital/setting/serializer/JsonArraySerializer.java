package com.puree.hospital.setting.serializer;


import com.puree.hospital.setting.utils.JsonUtil;

import java.util.function.Function;

/**
 * <p>
 * 模版默认值  数组序列化器
 * </p>
 *
 * <AUTHOR>
 * @date 2025/7/9 15:14
 */
public class JsonArraySerializer implements Function<String, String> {


    @Override
    public String apply(String s) {
        if (s == null || s.isEmpty()) {
            return "[]";
        }
        if (!JsonUtil.isJsonArray(s)) {
            throw new IllegalArgumentException("json格式错误");
        }
        return s;
    }
}
