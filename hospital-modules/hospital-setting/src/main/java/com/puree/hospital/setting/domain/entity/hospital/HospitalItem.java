package com.puree.hospital.setting.domain.entity.hospital;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 医院参数值表
 *
 * <AUTHOR>
 * @date 2024/10/22
 */
@Data
public class HospitalItem implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 外键，模板ID
     */
    private Long templateId;

    /**
     * 医院ID
     */
    private Long hospitalId;

    /**
     * 参数值
     */
    private String itemValue;

    /**
     * 乐观锁
     */
    private Integer revision;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 删除标记；0未删除，1已删除
     */
    private Boolean isDelete;

    private static final long serialVersionUID = 1L;

}