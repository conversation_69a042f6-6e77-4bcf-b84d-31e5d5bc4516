package com.puree.hospital.setting.annotation;

import com.puree.hospital.setting.model.event.SettingChangeEvent;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <p>
 * 业务配置 模版、配置项，变更通知
 * </p>
 *
 * <AUTHOR>
 * @date 2024/12/4 11:22
 */
@Target({ ElementType.METHOD })
@Retention(RetentionPolicy.RUNTIME)
public @interface SettingChangeNotify {

    /**
     * 操作 （新增，修改，删除）
     *
     * @return {@link String }
     */
    SettingChangeEvent.Operate operate();

    /**
     * 范围 （医院，平台）
     *
     * @return {@link String }
     */
    SettingChangeEvent.Classify classify();

    /**
     * 类型 （模版，配置项）
     *
     * @return {@link String }
     */
    SettingChangeEvent.Type type();

}
