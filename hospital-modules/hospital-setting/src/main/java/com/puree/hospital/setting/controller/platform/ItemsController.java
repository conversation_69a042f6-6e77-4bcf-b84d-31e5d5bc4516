package com.puree.hospital.setting.controller.platform;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.puree.hospital.common.core.exception.ServiceException;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.common.security.annotation.PreAuthorize;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.setting.config.TemplatePhysicalConfig;
import com.puree.hospital.setting.domain.dto.TemplateDefaultValueDTO;
import com.puree.hospital.setting.annotation.SettingChangeNotify;
import com.puree.hospital.setting.domain.dto.platform.ItemsDevelopSaveDto;
import com.puree.hospital.setting.domain.dto.platform.ItemsDevelopUpdateDto;
import com.puree.hospital.setting.domain.dto.platform.ItemsOperationsUpdateDto;
import com.puree.hospital.setting.domain.entity.platform.PlatformItems;
import com.puree.hospital.setting.domain.entity.hospital.HospitalTemplate;
import com.puree.hospital.setting.domain.query.TemplateQuery;
import com.puree.hospital.setting.domain.entity.platform.PlatformItems;
import com.puree.hospital.setting.domain.query.TemplateQuery;
import com.puree.hospital.setting.model.event.SettingChangeEvent;
import com.puree.hospital.setting.service.platform.PlatformItemsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import javax.servlet.http.HttpServletRequest;
import java.util.Collection;
import java.util.List;
import java.util.Map;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * @ClassName EasyConfigItemsController
 * <AUTHOR>
 * @Description 配置项数据
 * @Date 2023/12/6 12:11
 * @Version 1.0
 */
@RefreshScope
@RestController
@RequestMapping("/easy/config/easy-config-items")
public class ItemsController {

    @Autowired
    private TemplatePhysicalConfig templatePhysicalConfig;

    @Autowired
    private PlatformItemsService platformItemsService;

    /**
     * @Param blurSearch 模糊查询字段，支持配置项名称、描述、key
     * @Return com.puree.easycfg.domain.R
     * @Description 模糊查询 运营 配置项
     * <AUTHOR>
     * @Date 2023/12/6 14:49
     **/
    @GetMapping("/blur-search/operations")
    @PreAuthorize(hasPermi = "business:setting:platform:items:select")
    public R<Map<String, Object>> blurSearchConfigItems(@RequestParam(required = false, name = "blurSearch") String blurSearch) {
        return R.ok(platformItemsService.blurSearchConfigItems(blurSearch));
    }

    /**
     *  获取菜单下所有配置项
     * @param query 查询参数
     * @return 配置项
     */
    @GetMapping("/template/items-list")
    @PreAuthorize(hasPermi = "business:setting:platform:template:select")
    public R itemList(TemplateQuery query) {
        PageHelper.startPage(query.getPageNum(), query.getPageSize());
        List<PlatformItems> items = platformItemsService.getItems(query);
        PageInfo<PlatformItems> pageInfo = new PageInfo<>(items);
        return R.ok(pageInfo);
    }

    /**
     * @Param  查询模版信息
     * @Return com.puree.easycfg.domain.R
     * @Description 开发通过 ID 获取
     * <AUTHOR>
     * @Date 2023/12/6 17:12
     **/
    @GetMapping("template")
    @PreAuthorize(hasPermi = "business:setting:platform:template:update")
    public R<PlatformItems> getConfigItems(@RequestParam(required = false, name = "id") Long id) {
        return R.ok(platformItemsService.getById(id,false));
    }

    /**
     * 新增模版
     * @param dto 模版信息
     * @return
     */
    @PostMapping("/template")
    @PreAuthorize(hasPermi = "business:setting:platform:template:insert")
    @SettingChangeNotify(operate = SettingChangeEvent.Operate.ADD, classify = SettingChangeEvent.Classify.PLATFORM, type = SettingChangeEvent.Type.TEMPLATE)
    public R<Boolean> insertTemplate(@RequestBody @Validated ItemsDevelopSaveDto dto) {
        return R.ok(platformItemsService.insertTemplate(dto));
    }

    /**
     *  修改模版
     * @param dto 模版信息
     * @return 是否成功
     * @throws Exception 异常信息
     */
    @PutMapping("/template")
    @PreAuthorize(hasPermi = "business:setting:platform:template:update")
    public R<Boolean> updateTemplate(@RequestBody @Validated ItemsDevelopUpdateDto dto) throws Exception {
        return R.ok(platformItemsService.updateTemplate(dto));
    }

    /**
     * 修改模版默认值
     * @param dto 默认值
     * @return 是否成功
     * @throws Exception 异常信息
     */
    @PutMapping("/template/default-value")
    @PreAuthorize(hasPermi = "business:setting:platform:template:update")
    public R<Boolean> updateTemplateDefaultValue(@RequestBody @Validated TemplateDefaultValueDTO dto) throws Exception {
        return R.ok(platformItemsService.updateTemplateDefaultValue(dto));
    }


    /**
     * 修改 配置项
     * @param dto 配置项信息
     * @return 是否成功
     */
    @PutMapping("/items")
    @PreAuthorize(hasPermi = "business:setting:platform:items:update")
    @SettingChangeNotify(operate = SettingChangeEvent.Operate.UPDATE, classify = SettingChangeEvent.Classify.PLATFORM, type = SettingChangeEvent.Type.ITEMS)
    public R<Boolean> updateItems(@RequestBody ItemsOperationsUpdateDto dto) {
        if (dto.getId() == null && StringUtils.isEmpty(dto.getKey())){
            throw new IllegalArgumentException("ID 和 Key 不能同时为空");
        }
        if (dto.getRevision() == null && dto.getId() != null) {
            throw new IllegalArgumentException("Revision不能为空");
        }
        return R.ok(platformItemsService.updateItems(dto));
    }

    /**
     * 删除模版
     * @param id 模版ID
     * @param itemsName 模版名称
     * @return
     */
    @DeleteMapping("/template/{id}")
    @PreAuthorize(hasPermi = "business:setting:platform:template:delete")
    public R<Boolean> deleteTemplate(@PathVariable(value = "id", required = true) Long id,
                            @RequestParam("itemsName") String itemsName) {
        return R.ok(platformItemsService.deleteTemplate(id, itemsName));
    }

    /**
     * @Param id
     * @Param itemsName 需要 ID 和 名称同时匹配才能删除
     * @Return com.puree.easycfg.domain.R
     * @Description 开发删除配置项
     * <AUTHOR>
     * @Date 2023/12/7 18:05
     **/
    @DeleteMapping("/template/physical-delete/{id}")
    @PreAuthorize(hasPermi = "business:setting:platform:template:delete")
    public R developPhysicalDelete(@PathVariable(value = "id") Long id,@RequestParam("itemsName") String itemsName) {
        if (!templatePhysicalConfig.isPhysicalDeletion()) {
            throw new IllegalArgumentException("当前环境禁止物理删除，请与系统管理员联系！");
        }
        return R.ok(platformItemsService.developPhysicalDelete(id,itemsName));
    }

    /**
     * @Param id
     * @Param sort 排序
     * @Param menuId 菜单ID
     * @Return com.puree.easycfg.domain.R
     * @Description 修改菜单或排序
     * <AUTHOR>
     * @Date 2023/12/7 18:46
     **/
    @PutMapping("/template/sort-or-menuId")
    @PreAuthorize(hasPermi = "business:setting:platform:template:move")
    public R<Boolean> updateTemplateSortOrMenuId(@RequestParam(value = "id", required = true) Long id,
                                        @RequestParam(value = "sort", required = false) Integer sort,
                                        @RequestParam(value = "menuId", required = false) Long menuId) {
        return R.ok(platformItemsService.updateTemplateSortOrMenuId(id, sort, menuId));
    }

    /**
     * @Param
     * @Return com.puree.easycfg.domain.R
     * @Description 获取别名列表
     * <AUTHOR>
     * @Date 2023/12/18 15:04
     **/
    @GetMapping("/get-alias-map")
    public R<Collection<Map<String, String>>> getAliasMap() {
        return R.ok(platformItemsService.getAliasMapStringKeyList());
    }

}
