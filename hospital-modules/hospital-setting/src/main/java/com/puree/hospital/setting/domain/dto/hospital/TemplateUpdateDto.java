package com.puree.hospital.setting.domain.dto.hospital;


import com.puree.hospital.setting.domain.dto.SettingChangeDTO;
import com.puree.hospital.setting.constants.ItemTypeEnum;
import lombok.Data;

import javax.validation.constraints.*;
import java.io.Serializable;

/**
 * 医院配置模板 修改dto
 * <AUTHOR>
 * @date 2024/09/24
 */
@Data
public class TemplateUpdateDto extends SettingChangeDTO implements Serializable {

    /**
     * 主键
     */
    @NotNull(message = "主键 不能为空")
    private Long id;

    /**
     * key
     */
    private String key;
    /**
     * 名称
     * 修改时，未删除的数据中 名称 不能重复
     */
    @NotBlank(message = "名称 不能为空")
    @Size(max = 64, message = "参数 名称 不能超过 64 个字符")
    private String name;

    /**
     * 菜单 ID
     */
    private Long menuId;
    /**
     * 描述
     */
    @Size(max = 255, message = "参数 描述 不能超过 255 个字符")
    private String remark;
    /**
     * 类型;前端定义值是多少
     */
    @NotNull(message = "类型 不能为空")
    private String type;
    /**
     * 约束
     */
    @NotBlank(message = "约束 不能为空")
    private String restraint;
    /**
     * 是否开启 http api 访问;0 为关闭，1为开启
     */
    private Boolean enableHttp;
    /**
     * 是否开启Redis缓存;0 为关闭，1为开启
     */
    private Boolean enableCache;
    /**
     * 缓存分钟数
     */
    @Min(value = 0, message = "参数不正确")
    private Integer cacheMinutes;
    /**
     * 是否为匿名访问;0 为关闭，1为开启
     */
    private Boolean allowAnonymous;
    /**
     * 响应内容类型
     */
    private String contentType;
    /**
     * 可访问角色;角色与角色之间用,号隔开
     */
    @Size(max = 1024, message = "参数 可访问角色 不能超过 1024 个字符")
    private String allowedRoles;
    /**
     * 医院白名单，当开启时仅名单中的医院后天可见该配置项
     */
    @Size(max = 1024, message = "参数 医院启用范围 不能超过 1024 个字符")
    private String whiteList;

    /**
     * 乐观锁
     */
    @NotNull(message = "版本号不能为空")
    private Integer revision;
    /**
     * 返回值是否包装成ajax类型 只在返回类型为json时生效 1 包装 0 不包装
     */
    private Boolean isWrap;

    @Override
    public Long getHospitalId() {
        return null;
    }


    @Override
    public String getItemsValue() {
        return "";
    }

    @Override
    public String getDefaultValue() {
        return "";
    }

    /**
     * 过滤模式;0 无 1 白名单 2 黑名单
     * @see com.puree.hospital.setting.constants.FilterModeEnum
     */
    private String filterMode;

    /**
     * 过滤列表;用,号隔开
     */
    private String filterList;


}
