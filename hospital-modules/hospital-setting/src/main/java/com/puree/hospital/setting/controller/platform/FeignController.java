package com.puree.hospital.setting.controller.platform;

import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.setting.domain.entity.platform.PlatformItems;
import com.puree.hospital.setting.service.platform.PlatformItemsService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;

/**
 * <p>
 * 内部远程方法调用
 * </p>
 *
 * <AUTHOR>
 * @date 2024/11/21 14:54
 */
@RestController
@AllArgsConstructor
@RequestMapping("/feign/easy-config/platform")
@Slf4j
public class FeignController {

    private final PlatformItemsService platformItemsService;

    /**
     * 获取配置值
     *
     * @param key 目标访问key
     * @return 响应的配置值
     */
    @GetMapping("/getSettingValue")
    public R<String> getSettingValue(@RequestParam(value = "key") String key) {
        if (!StringUtils.hasText(key)) {
            throw new IllegalArgumentException("key is empty");
        }
        PlatformItems config = platformItemsService.getByKey(key);
        if (Objects.isNull(config)) {
            return R.fail(String.format("key：%s is not exist", key));
        }

        String value = config.getValue();
        return R.ok(Objects.nonNull(value) ? value : config.getDefaultValue());

    }

}
