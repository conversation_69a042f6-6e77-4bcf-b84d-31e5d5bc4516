package com.puree.hospital.setting.mapper.platform;


import com.puree.hospital.setting.domain.entity.platform.PlatformHistory;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @ClassName EasyConfigHistoryMapper
 * <AUTHOR>
 * @Description 历史数据 mapper
 * @Date 2023/12/6 11:37
 * @Version 1.0
 */
@Mapper
public interface PlatformHistoryMapper {

    /**
     * @Param itemsId
     * @Return java.util.List<com.puree.easycfg.domain.easy.EasyConfigHistory>
     * @Description 通过数据项ID 查询历史数据
     * <AUTHOR>
     * @Date 2023/12/8 16:40
     **/
    List<PlatformHistory> queryByItemId(@Param("itemsId") Long itemsId);

    /**
     * @Param
     * @Return java.util.List<com.puree.easycfg.domain.easy.EasyConfigHistory>
     * @Description 获取全部数据
     * <AUTHOR>
     * @Date 2024/1/16 17:42
     **/
    List<PlatformHistory> list();

    /**
     * @Param version
     * @Param itemsId
     * @Return java.lang.Integer
     * @Description 刷新历史表
     * <AUTHOR>
     * @Date 2023/12/8 16:40
     **/
    Integer refresh(@Param("version") Integer version, @Param("itemsId") Long itemsId);

    /**
     * @Param easyConfigHistories
     * @Return java.lang.Integer
     * @Description 重置版本号防止溢出
     * <AUTHOR>
     * @Date 2023/12/8 16:41
     **/
    Integer resetVersion(@Param("histories") List<PlatformHistory> easyConfigHistories);

    /**
     * @Param history
     * @Return java.lang.Integer
     * @Description 新增数据
     * <AUTHOR>
     * @Date 2023/12/8 16:41
     **/
    Integer insert(PlatformHistory platformHistory);

    /**
     * @Param blurSearch
     * @Param pageSize 每页几条
     * @Param pageNo 第几页
     * @Return java.util.List<com.puree.easycfg.domain.easy.EasyConfigHistory>
     * @Description 通过 key 模糊搜索
     * <AUTHOR>
     * @Date 2024/1/16 17:48
     **/
    List<PlatformHistory> queryByKey(@Param("blurSearch")String blurSearch, @Param("pageSize")Integer pageSize, @Param("pageNum")Integer pageNum, @Param("type") Integer type);


    Long queryByKeyCount(@Param("blurSearch")String blurSearch, @Param("type") Integer type);
}
