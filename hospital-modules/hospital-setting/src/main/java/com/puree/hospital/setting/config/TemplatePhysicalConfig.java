package com.puree.hospital.setting.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

/**
 * <p>
 * 业务配置 模版删除配置类
 * </p>
 *
 * <AUTHOR>
 * @date 2025/7/10 14:35
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "easy-config.template")
public class TemplatePhysicalConfig {
    /**
     * 是否允许物理删除模板;默认false
     */
    private boolean physicalDeletion = false;
}
