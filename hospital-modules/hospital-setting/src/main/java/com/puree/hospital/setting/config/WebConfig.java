package com.puree.hospital.setting.config;


import com.puree.hospital.setting.converter.CsvHttpMessageConverter;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.List;

/**
 * SpringBoot http message converter config类
 *
 * <AUTHOR>
 * @date 2024/12/30 19:48
 */
@Configuration
public class WebConfig implements WebMvcConfigurer {

    private final CsvHttpMessageConverter csvHttpMessageConverter;

    public WebConfig(CsvHttpMessageConverter csvHttpMessageConverter) {
        this.csvHttpMessageConverter = csvHttpMessageConverter;
    }

    @Override
    public void extendMessageConverters(List<HttpMessageConverter<?>> converters) {
        converters.add(csvHttpMessageConverter);
    }
}