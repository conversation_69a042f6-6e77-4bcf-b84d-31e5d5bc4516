package com.puree.hospital.setting.service.platform;



import com.puree.hospital.setting.domain.nacos.NacosConfigAll;
import com.puree.hospital.setting.domain.nacos.NacosLogin;
import com.puree.hospital.setting.domain.nacos.NacosPage;

import java.util.Properties;

/**
 * @ClassName EasyNacosService
 * <AUTHOR>
 * @Description nacos 的接口
 * @Date 2023/12/4 15:25
 * @Version 1.0
 */
public interface NacosService {

    /**
     * @Param
     * @Return com.puree.easycfg.domain.nacos.NacosLogin
     * @Description Nacos 登录
     * <AUTHOR>
     * @Date 2023/12/4 15:54
     **/
    NacosLogin nacosLogin();

    /**
     * @Param dataId
     * @Return com.puree.easycfg.domain.nacos.NacosPage
     * @Description 模糊查询分页, 第一页前十条，需要手动带上 * 号，如：*test*、*.yaml、application.* 等
     * <AUTHOR>
     * @Date 2023/12/4 16:04
     **/
    NacosPage blurPage(String dataId);


    /**
     * 获取访问令牌
     *
     * @return {@link String }
     */
    String getAccessToken();
    /**
     * @Param dataId
     * @Param pageNo 第几页
     * @Param pageSize 一页多少条
     * @Param search blur为模糊查询，accurate 为精确查询
     * @Return com.puree.easycfg.domain.nacos.NacosPage
     * @Description 查询分页
     * <AUTHOR>
     * @Date 2023/12/4 16:11
     **/
    NacosPage page(String dataId, Integer pageNo, Integer pageSize, String search);

    /**
     * @Param dataId
     * @Return com.puree.easycfg.domain.nacos.NacosPage
     * @Description 精确查询分页, 第一页前十条
     * <AUTHOR>
     * @Date 2023/12/4 16:16
     **/
    NacosPage accuratePage(String dataId);

    /**
     * @Param dataId
     * @Param show all 为全部，不填写为只读取 content，默认是空
     * @Return com.puree.easycfg.domain.nacos.NacosConfigAll
     * @Description 精确查询配置文件信息
     * <AUTHOR>
     * @Date 2023/12/4 16:23
     **/
    NacosConfigAll accurateNacosConfig(String dataId, String show);

    /**
     * @Param dataId
     * @Return java.lang.String
     * @Description 精确查询配置文件内容，只返回 content 的数据
     * <AUTHOR>
     * @Date 2023/12/4 17:05
     **/
    String accurateNacosConfigContent(String dataId);

    /**
     * @Param dataId
     * @Param key
     * @Param value
     * @Return java.lang.Boolean
     * @Description 修改配置文件
     * <AUTHOR>
     * @Date 2023/12/4 17:22
     **/
    Boolean updateNacosConfigItem(String dataId,String key,String value);

    /**
     * @Param dataId
     * @Param properties
     * @Return java.lang.Boolean
     * @Description 全量替换配置文件
     * <AUTHOR>
     * @Date 2023/12/12 17:53
     **/
    Boolean FullReplacementNacosConfig(String dataId, Properties properties);

    /**
     * @Param dataId
     * @Param key
     * @Return java.lang.Boolean
     * @Description 删除配置文件 key
     * <AUTHOR>
     * @Date 2023/12/8 19:00
     **/
    Boolean deleteNacosConfigItem(String dataId, String key);
}
