package com.puree.hospital.setting.domain.dto;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 开发新增医院模板配置
 * <AUTHOR>
 * @date 2024/09/23
 */
@Data
public class MenuSaveDto implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;
    /**
     * 类型名称
     */
    @NotBlank(message = "类型名称 不能为空")
    @Size(min = 0, max = 255, message = "参数 类型名称 不能超过 255 个字符")
    private String menuName;
    /**
     * 类型 key
     */
    @NotBlank(message = "类型名称 不能为空")
    @Size(min = 0, max = 64, message = "参数 类型 key 不能超过 64 个字符")
    private String menuKey;
    /**
     * 父级 ID;一级为 -1
     */
    @Min(value = 0, message = "参数不正确")
    private Long parentId;
    /**
     * 说明
     */
    @Size(min = 0, max = 255, message = "参数 说明 不能超过 255 个字符")
    private String remark;

    /**
     * 是否开启 http api 访问;0 为关闭，1为开启
     */
    private Boolean enableHttp;
    /**
     * 是否为匿名访问;0 为关闭，1为开启
     */
    private Boolean allowAnonymous;
    /**
     * 可访问角色;角色与角色之间用,号隔开
     */
    @Size( max = 1024, message = "参数 可访问角色 不能超过 1024 个字符")
    private String allowedRoles;

}
