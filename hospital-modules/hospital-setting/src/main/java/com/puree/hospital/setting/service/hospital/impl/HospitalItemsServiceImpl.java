package com.puree.hospital.setting.service.hospital.impl;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.setting.domain.entity.hospital.HospitalItem;
import com.puree.hospital.setting.mapper.hospital.HospitalItemMapper;
import com.puree.hospital.setting.service.hospital.HospitalItemsService;
import com.puree.hospital.setting.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.ExecutorType;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;

/**
 * <p>
 * 医院端业务配置 业务
 * </p>
 *
 * <AUTHOR>
 * @date 2024/10/22 15:37
 */
@Service
@Slf4j
public class HospitalItemsServiceImpl implements HospitalItemsService {

    @Autowired
    private HospitalItemMapper hospitalItemMapper;

    @Autowired
    private SqlSessionFactory sqlSessionFactory;


    @Override
    public HospitalItem getHospitalItemByTemplateId(Long templateId, Long hospitalId) {
        List<HospitalItem> hospitalItems = hospitalItemMapper.selectByTemplateIdAndHospitalId(templateId, hospitalId);
        if (CollectionUtil.isNotEmpty(hospitalItems)) {
            return hospitalItems.get(0);
        }
        return null;
    }


    /**
     * @param templateId
     * @return HospitalItem
     * @description 根据模板id 获取列表
     * <AUTHOR>
     * @date 2024/12/12 12:04
     **/
    @Override
    public List<HospitalItem> getItemListByTemplateId(Long templateId) {
        return hospitalItemMapper.selectByTemplateIdAndHospitalId(templateId, null);
    }

    @Override
    public List<HospitalItem> getItemsByTemplateIds(List<Long> templateIds) {
        return hospitalItemMapper.selectByTemplateIds(templateIds);
    }

    @Override
    public void deleteByTemplateId(Long templateId) {
        hospitalItemMapper.deleteByTemplateId(templateId);
    }

    @Override
    public void batchUpdateItemValueForDefaultValue(Long templateId, String defaultValue) {
        List<HospitalItem> hospitalItems = hospitalItemMapper.selectByTemplateId(templateId);
        try(SqlSession sqlSession = sqlSessionFactory.openSession(ExecutorType.BATCH)) {
            HospitalItemMapper mapper = sqlSession.getMapper(HospitalItemMapper.class);
            for (HospitalItem hospitalItem : hospitalItems) {
                hospitalItem.setItemValue(JsonUtil.mergeJson(defaultValue, hospitalItem.getItemValue()));
                mapper.updateByPrimaryKey(hospitalItem);
            }
            sqlSession.commit();
        } catch (Exception e) {
            log.error("更新模版失败", e);
            throw new ServiceException("更新模版失败:" + e.getMessage());
        }
    }

    /**
     * @param templateId 模板 ID
     * @return List<HospitalItem>
     * @description 按模板 ID 搜索
     * <AUTHOR>
     * @date 2024/11/27 12:08
     **/
    @Override
    public List<HospitalItem> getHospitalItemByTemplateId(Long templateId) {
       return hospitalItemMapper.selectByTemplateId(templateId);
    }
}
