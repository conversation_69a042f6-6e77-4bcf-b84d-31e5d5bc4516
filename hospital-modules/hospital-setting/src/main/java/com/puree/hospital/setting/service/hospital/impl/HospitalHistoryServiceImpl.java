package com.puree.hospital.setting.service.hospital.impl;

import com.puree.hospital.common.api.domain.Paging;

import com.puree.hospital.setting.domain.entity.hospital.HospitalHistory;
import com.puree.hospital.setting.domain.entity.hospital.HospitalTemplate;
import com.puree.hospital.setting.domain.entity.platform.PlatformHistory;
import com.puree.hospital.setting.domain.vo.hospital.HistoryVo;
import com.puree.hospital.setting.mapper.hospital.HospitalHistoryMapper;
import com.puree.hospital.setting.mapper.platform.PlatformHistoryMapper;
import com.puree.hospital.setting.service.hospital.HospitalHistoryService;
import com.puree.hospital.setting.utils.JsonUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/09/24
 */
@Slf4j
@Service
@AllArgsConstructor
public class HospitalHistoryServiceImpl implements HospitalHistoryService {

    @Autowired
    private final HospitalHistoryMapper hospitalHistoryMapper;
    @Autowired
    private final PlatformHistoryMapper platformHistoryMapper;

    @Override
    public Boolean insertHistory(HospitalTemplate newData, HospitalTemplate oldData) {
        Long itemsId = newData.getId();
        if (itemsId == null){
            throw new IllegalArgumentException("itemsId 不能为空");
        }
        if (newData.getKey() == null){
            throw new IllegalArgumentException("itemKey 不能为空");
        }
        if (newData.getMenuId() == null){
            throw new IllegalArgumentException("MenuId 不能为空");
        }
        if (newData.getType() == null){
            throw new IllegalArgumentException("Type 不能为空");
        }
        //查询有无历史数据
        List<PlatformHistory> easyConfigHistories = platformHistoryMapper.queryByItemId(itemsId);

        PlatformHistory platformHistory = new PlatformHistory();
        platformHistory.setItemsId(newData.getId());
        platformHistory.setItemsKey(newData.getKey());
        platformHistory.setMenuId(newData.getMenuId());
        platformHistory.setType(2);
        platformHistory.setCreateTime(oldData == null ? new Date() : oldData.getCreateTime());
        platformHistory.setUpdateTime(new Date());
        platformHistory.setCreateBy(oldData == null ? newData.getCreateBy() : oldData.getCreateBy());
        platformHistory.setUpdateBy(newData.getUpdateBy());
        //开发修改只需要记录元数据
        HistoryVo sourceVo = new HistoryVo();
        HistoryVo targetVo = new HistoryVo();
        BeanUtils.copyProperties(newData, targetVo);
        //对应修改情况；新增情况下，sourceVo为空
        if (oldData != null){
            BeanUtils.copyProperties(oldData, sourceVo);
        }
        platformHistory.setSourceData(JsonUtil.toJson(sourceVo));
        platformHistory.setTargetData(JsonUtil.toJson(targetVo));
        //写入版本号
        platformHistory.setVersion(newData.getRevision() + 1);
        if (easyConfigHistories == null || easyConfigHistories.isEmpty()) {
            //无历史数据则版本号为 1
            platformHistory.setVersion(1);
        }
        log.debug("insertHistory:{}", platformHistory);
        return platformHistoryMapper.insert(platformHistory) >= 1;
    }

    @Override
    public Boolean insertHospitalHistory(HospitalTemplate newData, HospitalTemplate oldData, Long hospitalId) {
        log.debug("insertHospitalHistory--newData:{}",newData);
        log.debug("insertHospitalHistory--oldData:{}",oldData);
        Long templateId = newData.getId();
        if (templateId == null){
            throw new IllegalArgumentException("templateId 不能为空");
        }
        if (hospitalId == null){
            throw new IllegalArgumentException("HospitalId 不能为空");
        }
        if (newData.getKey() == null){
            throw new IllegalArgumentException("itemKey 不能为空");
        }
        Long itemId = newData.getId();
        //查询有无历史数据
        List<HospitalHistory> easyConfigHistories = hospitalHistoryMapper.queryByItemIdAndHospitalId(itemId, hospitalId);

        HospitalHistory hospitalHistory = getHospitalHistory(newData, oldData, easyConfigHistories,hospitalId);
        return hospitalHistoryMapper.insert(hospitalHistory) >= 1;
    }

    private static HospitalHistory getHospitalHistory(HospitalTemplate newData, HospitalTemplate oldData, List<HospitalHistory> easyConfigHistories, Long hospitalId) {
        HospitalHistory hospitalHistory = new HospitalHistory();
        hospitalHistory.setHospitalId(hospitalId);
        hospitalHistory.setVersion(newData.getItemRevision());
        hospitalHistory.setTemplateId(newData.getId());
        hospitalHistory.setItemKey(newData.getKey());
        hospitalHistory.setCreateTime(oldData == null ? new Date() : oldData.getCreateTime());
        hospitalHistory.setUpdateTime(new Date());
        hospitalHistory.setCreateBy(oldData == null ? newData.getCreateBy() : oldData.getCreateBy());
        hospitalHistory.setUpdateBy(newData.getUpdateBy());
        //对应新增情况
        if(oldData == null){
            hospitalHistory.setPreviousValue(null);
        }else {
            //对应修改情况
            if (oldData.getItemValue() == null){
                hospitalHistory.setPreviousValue(oldData.getDefaultValue());
            }else {
                hospitalHistory.setPreviousValue(oldData.getItemValue());
            }
        }

        hospitalHistory.setUpdatedValue(newData.getItemValue());
        if (easyConfigHistories == null || easyConfigHistories.isEmpty()) {
            //无历史数据则版本号为 1
            hospitalHistory.setVersion(1);
        }
        return hospitalHistory;
    }

    @Override
    public Paging<List<HospitalHistory>> list(String itemsKey, Integer hospitalId, Integer pageSize, Integer pageNum) {
        if (pageNum == null || pageNum < 1) {
            pageNum = 1;
        }
        if (pageSize == null || pageSize < 1) {
            pageSize = 10;
        }
        log.debug("list--itemsKey:{},hospitalId:{},pageSize:{},pageNum:{}", itemsKey, hospitalId, pageSize, pageNum);
        Long count = hospitalHistoryMapper.queryByKeyCount(hospitalId, itemsKey);

        if (count == null || count == 0) {
            return Paging.success(null, 0L, pageNum, pageSize);
        }
        List<HospitalHistory> easyConfigHistories = queryByKey(itemsKey, hospitalId, pageSize, pageNum);

        return Paging.success(easyConfigHistories, count, pageNum, pageSize);
    }

    private List<HospitalHistory> queryByKey(String itemsKey, Integer hospitalId, Integer pageSize, Integer pageNum) {
        if (pageSize == null) {
            pageSize = 10;
        }
        if (pageNum == null) {
            pageNum = 1;
        }
        //分页
        pageNum = (pageNum - 1) * pageSize;
        return hospitalHistoryMapper.queryByKey(itemsKey, hospitalId, pageSize, pageNum);
    }

}
