package com.puree.hospital.setting.controller.platform;

import com.puree.hospital.setting.domain.nacos.NacosConfigAll;
import com.puree.hospital.setting.domain.nacos.NacosPage;
import com.puree.hospital.setting.service.platform.NacosService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @ClassName EasyNacosController
 * <AUTHOR>
 * @Description nacos Controller
 * 用于测试nacos是否通过，已从 spring.factories 中删除注入，需要测试则手动开启
 * @Date 2023/12/4 16:59
 * @Version 1.0
 */
@RestController
@AllArgsConstructor
@RequestMapping("/easy/nacos")
public class NacosController {

    private final NacosService nacosService;

    /**
     * @Param dataId
     * @Return com.puree.easycfg.domain.nacos.NacosPage
     * @Description 模糊查询分页, 第一页前十条，需要手动带上 * 号，如：*test*、*.yaml、application.* 等
     * <AUTHOR>
     * @Date 2023/12/4 16:04
     **/
    @GetMapping("/blurPage")
    public NacosPage blurPage(String dataId) {
        return nacosService.blurPage(dataId);
    }

    /**
     * @Param dataId
     * @Param pageNo 第几页
     * @Param pageSize 一页多少条
     * @Param search blur为模糊查询，accurate 为精确查询
     * @Return com.puree.easycfg.domain.nacos.NacosPage
     * @Description 查询分页
     * <AUTHOR>
     * @Date 2023/12/4 16:11
     **/
    @GetMapping("/page")
    public NacosPage page(String dataId, Integer pageNo, Integer pageSize, String search) {
        return nacosService.page(dataId, pageNo, pageSize, search);
    }

    /**
     * @Param dataId
     * @Return com.puree.easycfg.domain.nacos.NacosPage
     * @Description 精确查询分页, 第一页前十条
     * <AUTHOR>
     * @Date 2023/12/4 16:16
     **/
    @GetMapping("/accurate")
    public NacosPage accuratePage(String dataId) {
        return nacosService.accuratePage(dataId);
    }

    /**
     * @Param dataId
     * @Param show all 为全部，不填写为只读取 content，默认是空
     * @Return com.puree.easycfg.domain.nacos.NacosConfigAll
     * @Description 精确查询配置文件信息
     * <AUTHOR>
     * @Date 2023/12/4 16:23
     **/
    @GetMapping("/accurateNacosConfig")
    public NacosConfigAll accurateNacosConfig(String dataId, String show) {
        return nacosService.accurateNacosConfig(dataId, show);
    }

    /**
     * @Param dataId
     * @Return java.lang.String
     * @Description 精确查询配置文件内容，只返回 content 的数据
     * <AUTHOR>
     * @Date 2023/12/4 17:07
     **/
    @GetMapping("/accurateNacosConfigContent")
    public String accurateNacosConfig(String dataId) {
        return nacosService.accurateNacosConfigContent(dataId);
    }

    @PostMapping("/updateNacosConfig")
    public Object updateNacosConfig(String dataId, String key, String value) {
        return nacosService.updateNacosConfigItem(dataId, key, value);
    }
    @GetMapping("/access-token")
    public Object getAccessToken() {
        return nacosService.getAccessToken();
    }
}
