package com.puree.hospital.setting.domain.nacos;

import lombok.Data;

/**
 * @ClassName NacosConfig
 * <AUTHOR>
 * @Description 配置文件全部信息
 * @Date 2023/11/7 12:17
 * @Version 1.0
 */
@Data
public class NacosConfigAll {
    /**
     * 应用名称
     */
    private String appName;
    /**
     * config标签
     */
    private String configTags;
    /**
     * 内容
     */
    private String content;
    /**
     * 创建IP
     */
    private String createIp;
    /**
     * 创建时间
     */
    private Long createTime;
    /**
     * 创建用户
     */
    private String createUser;
    /**
     * 文件名称，对应 nacos 控制台的 dataId
     */
    private String dataId;
    /**
     * 描述
     */
    private String desc;
    /**
     * 生效
     */
    private String effect;
    /**
     * 分组
     */
    private String group;
    /**
     * ID
     */
    private String id;
    /**
     * MD5
     */
    private String md5;
    /**
     * 修改时间
     */
    private String modifyTime;
    /**
     * 模式
     */
    private String schema;
    /**
     * 租户 ID，对应 nacos 控制台的 命名空间
     */
    private String tenant;
    /**
     * 类型 text、json、xml、yaml、html、properties
     */
    private String type;
    /**
     * 用户
     */
    private String use;
}
