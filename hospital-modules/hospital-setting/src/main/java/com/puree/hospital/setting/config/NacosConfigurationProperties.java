package com.puree.hospital.setting.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * @ClassName NaocsConfigProperties
 * <AUTHOR>
 * @Description Nacos 配置属性
 * @Date 2023/11/28 17:33
 * @Version 1.0
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "spring.cloud.nacos.config")
public class NacosConfigurationProperties {

    //todo 这里也应该为抽象的配置属性类，指定多个配置注册中心，为了方便直接这样写的
    /**
     * nacos 账号
     */
    private String username;
    /**
     * nacos 密码
     */
    private String password;
    /**
     * nacos 地址
     */
    private String serverAddr = "127.0.0.1:8848";
    /**
     * 命名空间
     */
    private String namespace = "public";
    /**
     * 分组
     */
    private String group = "DEFAULT_GROUP";

}
