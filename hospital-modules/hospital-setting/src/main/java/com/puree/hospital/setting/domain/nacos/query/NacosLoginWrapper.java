package com.puree.hospital.setting.domain.nacos.query;


import lombok.Builder;
import lombok.Data;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

/**
 * @ClassName NacosLoginWrapper
 * <AUTHOR>
 * @Description Nacos 登录包装器
 * @Date 2023/11/28 14:16
 * @Version 1.0
 */
@Data
@Builder
public class NacosLoginWrapper {

    private static final String URL = "http://%s/nacos/v1/auth/users/login";

    /**
     * 账号
     */
    private String username;
    /**
     * 密码
     */
    private String password;


    private String address;

    private RestTemplate restTemplate;


    public String buildUrl() {
        return String.format(URL, address);
    }

    /**
     * @Param
     * @Return cn.hutool.http.HttpRequest
     * @Description 生成 Http 请求
     * <AUTHOR>
     * @Date 2023/11/28 14:24
     **/
    public ResponseEntity<String> sendPostRequest() {
        String url = buildUrl();

        HttpHeaders headers = new HttpHeaders();
        headers.set("Content-Type", "application/x-www-form-urlencoded");

        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("username", username);
        params.add("password", password);

        HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(params, headers);

        return restTemplate.exchange(url, HttpMethod.POST, request, String.class);
    }
}
