package com.puree.hospital.setting.domain.nacos.query;

import com.puree.hospital.setting.config.NacosConfigurationProperties;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

/**
 * @ClassName NacosConfigUpdataWrapper
 * <AUTHOR>
 * @Description Nacos 配置更新包装器
 * @Date 2023/11/28 14:35
 * @Version 1.0
 */
@Data
@Builder
@Slf4j
public class NacosConfigUpdateWrapper {

    private static final String URL = "http://%s/nacos/v1/cs/configs";

    private static final String HINT = "#请不要直接在Nacos中修改该文件，请在运营平台操作\n%s";
    /**
     * 文件名称，对应 nacos 控制台的 dataId
     */
    private String dataId;
    /**
     * 分组，对应 nacos 控制台的 group
     */
    private String group;
    /**
     * 内容
     */
    private String content;
    /**
     * 应用名称
     */
    private String appName;
    /**
     * 描述
     */
    private String desc;
    /**
     * 类型 text、json、xml、yaml、html、properties
     */
    private String type;
    /**
     * id
     */
    private Integer id;
    /**
     * 校验值
     */
    private String md5;
    /**
     * 租户，也就是命名空间
     */
    private String tenant;
    /**
     * 创建时间
     */
    private Long createTime;
    /**
     * 修改时间
     */
    private Long modifyTime;
    /**
     * 创建用户
     */
    private String createUser;
    /**
     * 创建 IP
     */
    private String createIp;
    /**
     * config标签
     */
    private String configTags;
    /**
     * Nacos 访问令牌
     */
    private String accessToken;
    /**
     * 用户名称
     */
    private String username;

    private NacosConfigurationProperties nacosConfigurationProperties;
    private RestTemplate restTemplate;

    public String buildUrl() {
//        String UrlFormat = CharSequenceUtil.format(URL, nacosConfigurationProperties.getServerAddr());
//        UrlBuilder urlBuilder = UrlBuilder.of(UrlFormat);
//        urlBuilder.addQuery("accessToken", accessToken);
//        urlBuilder.addQuery("username", nacosConfigurationProperties.getUsername());
//        return urlBuilder;
        String urlFormat = String.format(URL, nacosConfigurationProperties.getServerAddr());
        return urlFormat + "?accessToken=" + accessToken + "&username=" + nacosConfigurationProperties.getUsername();

    }

    /**
     * @Param
     * @Return cn.hutool.http.HttpRequest
     * @Description 生成 Http 请求
     * <AUTHOR>
     * @Date 2023/11/28 14:24
     **/
    public ResponseEntity<String> sendPostRequest() {
        String url = buildUrl();

        HttpHeaders headers = new HttpHeaders();
        headers.set("Content-Type", "application/x-www-form-urlencoded");

        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("dataId", dataId);
        params.add("group", StringUtils.isEmpty(group) ? nacosConfigurationProperties.getGroup() : group);
        params.add("content", String.format(HINT, content));
        params.add("appName", appName);
        params.add("desc", desc);
        params.add("type", StringUtils.isEmpty(type) ? "properties" : type);
        params.add("id", id + "");
        params.add("md5", md5);
        params.add("tenant", StringUtils.isEmpty(tenant) ? nacosConfigurationProperties.getNamespace() : tenant);
        params.add("createTime", createTime + "");
        params.add("modifyTime", modifyTime + "");
        params.add("createUser", createUser);
        params.add("createIp", createIp);
        params.add("configTags", configTags);

        HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(params, headers);

        return restTemplate.exchange(url, HttpMethod.POST, request, String.class);
    }
}
