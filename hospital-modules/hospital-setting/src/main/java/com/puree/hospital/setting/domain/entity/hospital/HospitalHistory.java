package com.puree.hospital.setting.domain.entity.hospital;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 医院历史记录
 * <AUTHOR>
 * @date 2024/09/23
 */
@Data
public class HospitalHistory {
    /**
     * 主键
     */
    private Long id;

    /**
     * 医院ID
     */
    private Long hospitalId;
    /**
     * 模板ID
     */
    private Long templateId;

    /**
     * 元数据key
     */
    private String itemKey;
    /**
     * 源数据
     */
    private String previousValue;
    /**
     * 目标数据
     */
    private String updatedValue;
    /**
     * 版本号;用于记录历史版本，版本号最大的为最新记录
     */
    private Integer version;
    /**
     * 创建人
     */
    private String createBy;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
     * 更新人
     */
    private String updateBy;
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}
