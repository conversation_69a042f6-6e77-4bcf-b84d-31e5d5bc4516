package com.puree.hospital.setting.domain.entity;

/**
 * <p>
 * 模版接口
 * </p>
 *
 * <AUTHOR>
 * @date 2024/12/12 11:49
 */
public interface ITemplate{


    /**
     * 配置项/模版 key
     *
     * @return {@link String }
     */
    String getKey();

    /**
     * 获取模板名称
     *
     * @return {@link String }
     */
    String getName();


    /**
     * 获取 ID
     *
     * @return {@link Long }
     */
    Long getId();


    /**
     * 获取 配置项的值
     *
     * @return {@link String }
     */
    String getItemsValue();

    /**
     * 获取默认值
     *
     * @return {@link String }
     */
    String getDefaultValue();

}
