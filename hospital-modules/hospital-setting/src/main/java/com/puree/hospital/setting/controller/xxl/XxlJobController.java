package com.puree.hospital.setting.controller.xxl;

import com.puree.hospital.setting.service.hospital.HospitalTemplateService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @ClassName XxlJobController
 * <AUTHOR>
 * @Description 定时任务调用
 * @Date 2024/12/9 18:07
 * @Version 1.0
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/xxl-job/easy-config")
public class XxlJobController {

    private final HospitalTemplateService hospitalTemplateService;

    /**
     * @param
     * @return Boolean
     * @description 定时任务续期
     * <AUTHOR>
     * @date 2024/12/12 14:09
     **/
    @XxlJob("renewPermanent")
    public Boolean renewPermanentItem() {
        return hospitalTemplateService.renewPermanentItem();
    }
}
