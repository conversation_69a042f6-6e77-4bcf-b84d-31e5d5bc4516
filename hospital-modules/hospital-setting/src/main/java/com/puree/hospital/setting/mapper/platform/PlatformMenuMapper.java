package com.puree.hospital.setting.mapper.platform;


import com.puree.hospital.setting.domain.entity.Menu;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @ClassName EasyConfigMenuMapper
 * <AUTHOR>
 * @Description 菜单 mapper
 * @Date 2023/12/6 11:18
 * @Version 1.0
 */
@Mapper
public interface PlatformMenuMapper {

    /**
     * @Param
     * @Return java.util.List<com.puree.easycfg.domain.easy.EasyConfigMenu>
     * @Description 获取所有菜单
     * <AUTHOR>
     * @Date 2023/12/8 15:07
     **/
    List<Menu> list();

    /**
     * @Param
     * @Return List<EasyConfigMenu>
     * @Description 通过菜单名称和菜单 key 查询
     * <AUTHOR>
     * @Date 2024/6/24 10:57
     **/
    List<Menu> queryByMenuNameAndKey(@Param("menuKey") String menuKey, @Param("menuName") String menuName);

    /**
     * @Param menuKey
     * @Param menuName
     * @Return java.lang.Long
     * @Description 统计是否重复
     * <AUTHOR>
     * @Date 2023/12/8 15:30
     **/
    Long count(@Param("menuKey") String menuKey, @Param("menuName") String menuName);

    /**
     * @Param parentId
     * @Return java.lang.Long
     * @Description 统计子节点个数
     * <AUTHOR>
     * @Date 2023/12/18 16:19
     **/
    Long childNodeCount(@Param("parentId")Long parentId);

    /**
     * @Param menuId
     * @Return java.lang.Integer
     * @Description 通过 ID 查询该菜单下最大排序
     * <AUTHOR>
     * @Date 2023/12/8 15:44
     **/
    Integer queryMenuMaxSortById(@Param("menuId")Long menuId);

    /**
     * @Param menuId
     * @Return java.util.List<com.puree.easycfg.domain.easy.EasyConfigMenu>
     * @Description 获取 父 ID 下的所有节点
     * <AUTHOR>
     * @Date 2024/1/11 14:14
     **/
    List<Menu> queryMenuSortByParentId(@Param("parentId")Long parentId);

    /**
     * @Param easyConfigMenu 实例对象
     * @Return java.lang.Integer
     * @Description 新增数据
     * <AUTHOR>
     * @Date 2023/12/8 16:15
     **/
    Integer insert(Menu platformMenu);

    /**
     * @Param id
     * @Return com.puree.easycfg.domain.easy.EasyConfigMenu
     * @Description 根据 ID 查询 
     * <AUTHOR>
     * @Date 2023/12/8 16:14
     **/
    Menu queryById(@Param("id")Long id);

    /**
     * @Param parentId
     * @Param sort 修改排序
     * @Return java.lang.Integer
     * @Description 根据 sort 将值后移 
     * <AUTHOR>
     * @Date 2023/12/8 16:14
     **/
    Integer updateSortMoveBack(@Param("id")Long parentId, @Param("sort")Integer sort);

    /**
     * @Param parentId
     * @Param sort
     * @Return java.lang.Integer
     * @Description 根据 sort 将值后移
     * <AUTHOR>
     * @Date 2024/1/11 15:09
     **/
    Integer updateSortForward(@Param("id")Long parentId, @Param("sort")Integer sort);

    /**
     * @Param id
     * @Param sort
     * @Return java.lang.Integer
     * @Description 根据 主键 修改 sort 
     * <AUTHOR>
     * @Date 2023/12/8 16:14
     **/
    Integer updateSort(@Param("id")Long id, @Param("sort")Integer sort);

    /**
     * @Param id
     * @Param parentId
     * @Param sort
     * @Return java.lang.Integer
     * @Description 根据 主键 修改 父节点
     * <AUTHOR>
     * @Date 2023/12/8 16:17
     **/
    Integer updateMenuId(@Param("id")Long id,@Param("parentId") Long parentId,@Param("sort") Integer sort);

    /**
     * @Param parentId
     * @Return java.lang.Integer
     * @Description 重置该父节点下的排序
     * <AUTHOR>
     * @Date 2024/1/11 12:18
     **/
    Integer resetSort(@Param("parentId") Long parentId);

    /**
     * @Param id
     * @Param menuName
     * @Return java.lang.Integer
     * @Description 按 ID 和项目名称删除
     * <AUTHOR>
     * @Date 2023/12/8 16:21
     **/
    Integer deleteByIdAndItemsName(@Param("id")Long id, @Param("menuName")String menuName);

    /**
     * @Param item
     * @Return java.lang.Integer
     * @Description 根据 ID 修改
     * <AUTHOR>
     * @Date 2023/12/8 16:32
     **/
    Integer update(Menu item);

    /**
     * 按菜单键查询
     *
     * @param menuKey 菜单键
     * @return {@link Menu }
     */
    Menu queryMenu(String menuKey);
}
