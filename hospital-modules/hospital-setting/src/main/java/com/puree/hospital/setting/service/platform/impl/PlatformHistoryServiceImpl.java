package com.puree.hospital.setting.service.platform.impl;

import com.puree.hospital.common.api.domain.Paging;
import com.puree.hospital.setting.domain.entity.platform.PlatformHistory;
import com.puree.hospital.setting.domain.entity.platform.PlatformItems;
import com.puree.hospital.setting.domain.vo.platform.ItemsHistoryVo;
import com.puree.hospital.setting.mapper.platform.PlatformHistoryMapper;
import com.puree.hospital.setting.service.platform.PlatformHistoryService;
import com.puree.hospital.setting.utils.JsonUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * @ClassName EasyConfigHistoryServiceImpl
 * <AUTHOR>
 * @Description 历史数据
 * @Date 2023/12/6 11:36
 * @Version 1.0
 */
@Slf4j
@Service
@AllArgsConstructor
public class PlatformHistoryServiceImpl implements PlatformHistoryService {

    private final PlatformHistoryMapper platformHistoryMapper;

    /**
     * @Param sourceData 源配置项数据
     * @Param targetData 目标配置项数据
     * @Param type 类型 0 开发 1 运维 如果为开发修改则只需要记录元数据，如果为运营修改只需要记录value值
     * @Return java.lang.Boolean
     * @Description 新增历史数据
     * <AUTHOR>
     * @Date 2023/12/7 15:47
     **/
    @Override
    public Boolean insertHistory(PlatformItems sourceData, PlatformItems targetData, Integer type) {

        Long itemId = targetData.getId();
        //查询有无历史数据
        List<PlatformHistory> easyConfigHistories = platformHistoryMapper.queryByItemId(itemId);

        PlatformHistory platformHistory = new PlatformHistory();
        platformHistory.setItemsId(targetData.getId());
        platformHistory.setItemsKey(targetData.getKey());
        platformHistory.setMenuId(targetData.getMenuId());
        platformHistory.setType(type);
        platformHistory.setCreateTime(sourceData == null? targetData.getCreateTime() : sourceData.getCreateTime());
        platformHistory.setUpdateTime(new Date());
        if (type == 0) {
            //开发修改只需要记录元数据
            ItemsHistoryVo sourceVo = new ItemsHistoryVo();
            ItemsHistoryVo targetVo = new ItemsHistoryVo();
            if (sourceData!= null){
                BeanUtils.copyProperties(sourceData, sourceVo);
            }
            BeanUtils.copyProperties(targetData, targetVo);
            //写入版本号
            platformHistory.setVersion(targetData.getRevision() + 1);
            platformHistory.setSourceData(JsonUtil.toJson(sourceVo));
            platformHistory.setTargetData(JsonUtil.toJson(targetVo));
        } else {
            //运营修改 只需要记录value值
            platformHistory.setSourceData(sourceData.getValue());
            platformHistory.setTargetData(targetData.getValue());
        }

        if (easyConfigHistories == null || easyConfigHistories.isEmpty()) {
            //无历史数据则版本号为 1
            platformHistory.setVersion(1);
        }


        return platformHistoryMapper.insert(platformHistory) >= 1;
    }


    /**
     * @Param sourceData 源配置项数据
     * @Param targetData 目标配置项数据
     * @Return java.lang.Boolean
     * @Description 开发新增历史数据
     * <AUTHOR>
     * @Date 2023/12/7 16:06
     **/
    @Override
    public Boolean developInsertHistory(PlatformItems sourceData, PlatformItems targetData) {
        return insertHistory(sourceData, targetData, 0);
    }

    /**
     * @Param sourceData 源配置项数据
     * @Param targetData 目标配置项数据
     * @Return java.lang.Boolean
     * @Description 运营新增历史数据
     * <AUTHOR>
     * @Date 2023/12/7 18:02
     **/
    @Override
    public Boolean operationsInsertHistory(PlatformItems sourceData, PlatformItems targetData) {
        return insertHistory(sourceData, targetData, 1);
    }

    /**
     * @Param itemsId
     * @Return java.util.List<com.puree.easycfg.domain.easy.EasyConfigHistory>
     * @Description 通过数据项ID 查询历史数据
     * <AUTHOR>
     * @Date 2023/12/8 16:40
     **/
    @Override
    public List<PlatformHistory> queryByItemId(Long itemsId) {
        return platformHistoryMapper.queryByItemId(itemsId);
    }

    /**
     * @Param blurSearch
     * @Param pageSize 每页几条
     * @Param pageNo 第几页
     * @Return CfgPage
     * @Description 模糊查询
     * <AUTHOR>
     * @Date 2024/1/16 18:01
     **/
    @Override
    public Paging<List<PlatformHistory>> blurSearch(String blurSearch, Integer pageSize, Integer pageNum, Integer type) {
        log.debug("模糊查询  blurSearch{} ：type={}",blurSearch,type);
        Long count = queryByKeyCount(blurSearch,type);

        if (count == null || count == 0) {
           return Paging.success(null, count, pageNum, pageSize);
        }
        List<PlatformHistory> easyConfigHistories = queryByKey(blurSearch, pageSize, pageNum,type);
        return Paging.success(easyConfigHistories, count, pageNum, pageSize);
    }

    /**
     * @Param blurSearch
     * @Param pageSize 每页几条
     * @Param pageNo 第几页
     * @Return java.util.List<com.puree.easycfg.domain.easy.EasyConfigHistory>
     * @Description 模糊查询
     * <AUTHOR>
     * @Date 2024/1/18 14:57
     **/
    @Override
    public List<PlatformHistory> queryByKey(String blurSearch, Integer pageSize, Integer pageNum, Integer type) {
        if (pageSize == null) {
            pageSize = 10;
        }
        if (pageNum == null) {
            pageNum = 1;
        }
        //分页
        pageNum = (pageNum - 1) * pageSize;
        return platformHistoryMapper.queryByKey(blurSearch, pageSize, pageNum,type);
    }

    /**
     * @Param blurSearch
     * @Return java.lang.Long
     * @Description 分页计数
     * <AUTHOR>
     * @Date 2024/1/18 14:57
     **/
    @Override
    public Long queryByKeyCount(String blurSearch, Integer type) {
        return platformHistoryMapper.queryByKeyCount(blurSearch,type);
    }

    /**
     * @Param
     * @Return java.util.List<com.puree.easycfg.domain.easy.EasyConfigHistory>
     * @Description 获取全部数据
     * <AUTHOR>
     * @Date 2024/1/16 17:43
     **/
    @Override
    public List<PlatformHistory> list() {
        return platformHistoryMapper.list();
    }
}
