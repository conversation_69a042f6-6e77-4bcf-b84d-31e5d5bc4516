package com.puree.hospital.setting.domain.nacos.query;

import com.puree.hospital.setting.config.NacosConfigurationProperties;
import lombok.Builder;
import lombok.Data;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

/**
 * @ClassName NacosSearchBuilder
 * <AUTHOR>
 * @Description Nacos 搜索查询包装器
 * @Date 2023/11/7 11:44
 * @Version 1.0
 */
@Data
@Builder
public class NacosPageSearchQueryWrapper {

    private static final  String URL = "http://%s/nacos/v1/cs/configs";

    /**
     * 文件名称，对应 nacos 控制台的 dataId
     */
    private String dataId;
    /**
     * 分组，对应 nacos 控制台的 group
     */
    private String group;
    /**
     * 应用名称，对应 nacos 控制台的 归属应用
     */
    private String appName;
    /**
     * 配置标签，对应 nacos 控制台的 标签
     */
    private String configTags;
    /**
     * 租户 ID，对应 nacos 控制台的 命名空间
     */
    private String tenant;
    /**
     * 搜索类型 blur 为模糊搜索，accurate 为精确搜索，加了这个就是分页查询
     */
    private String search;
    /**
     * Nacos 访问令牌
     */
    private String accessToken;
    /**
     * 用户名称
     */
    private String username;
    /**
     * 显示 all 为全部信息，不填写为只获取 content 信息，
     */
    private String show;
    /**
     * 分页查询，第几页
     */
    private Integer pageNo;
    /**
     * 分页查询，每页条数
     */
    private Integer pageSize;

    /**
     * nacosc 配置信息
     */
    private NacosConfigurationProperties nacoscConfigProperties;

    private RestTemplate restTemplate;

    /**
     * @Param
     * @Return cn.hutool.core.net.url.UrlBuilder
     * @Description 生成 UrlBuilder
     * <AUTHOR>
     * @Date 2023/11/7 12:06
     **/
    public String buildUrl() {
        return UriComponentsBuilder.fromHttpUrl(String.format(URL, nacoscConfigProperties.getServerAddr()))
                .queryParam("dataId", dataId)
                .queryParam("group", group == null || group.isEmpty() ? nacoscConfigProperties.getGroup() : group)
                .queryParam("appName", appName)
                .queryParam("config_tags", configTags)
                .queryParam("show", show)
                .queryParam("tenant", tenant == null || tenant.isEmpty() ? nacoscConfigProperties.getNamespace() : tenant)
                .queryParam("search", search)
                .queryParam("accessToken", accessToken)
                .queryParam("username", nacoscConfigProperties.getUsername())
                .queryParam("pageNo", pageNo)
                .queryParam("pageSize", pageSize)
                .toUriString();
    }

    /**
     * @Param
     * @Return cn.hutool.http.HttpRequest
     * @Description 生成 Http 请求
     * <AUTHOR>
     * @Date 2023/11/28 14:24
     **/
    public ResponseEntity<String> sendGetRequest() {
        String url = buildUrl();
        return restTemplate.getForEntity(url, String.class);
    }
}
