package com.puree.hospital.setting.service.platform.impl;


import com.puree.hospital.setting.domain.entity.platform.PlatformItems;
import com.puree.hospital.setting.domain.query.TemplateQuery;
import com.puree.hospital.setting.domain.vo.platform.ItemListByMenuKey;
import com.puree.hospital.setting.service.platform.PlatformConfigHttpApiService;
import com.puree.hospital.setting.service.platform.PlatformItemsService;
import com.puree.hospital.setting.utils.JsonUtil;
import com.puree.hospital.setting.utils.PropertiesUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName EasyConfigHttpApiServiceImpl
 * <AUTHOR>
 * @Description http api
 * @Date 2024/1/10 17:18
 * @Version 1.0
 */
@Slf4j
@Service
@AllArgsConstructor
public class PlatformConfigHttpApiServiceImpl implements PlatformConfigHttpApiService {

    private final PlatformItemsService platformItemsService;


    /**
     * @Param menuId
     * @Return java.util.Map<java.lang.String, java.lang.String>
     * @Description 获取菜单下所有的 Key / Name
     * <AUTHOR>
     * @Date 2024/3/22 17:30
     **/
    @Override
    public List<ItemListByMenuKey> getItemListByMenuKey(TemplateQuery templateQuery) {
        List<PlatformItems> items = platformItemsService.getItems(templateQuery);
        return items.stream().map(item -> {
                    ItemListByMenuKey itemListByMenuKey = new ItemListByMenuKey();
                    BeanUtils.copyProperties(item, itemListByMenuKey);
                    return itemListByMenuKey;
                })
                .collect(Collectors.toList());
    }


    /**
     * 处理器数据
     *
     * @param responseContentType 响应内容类型
     * @param value               价值
     * @param wrap                包装
     * @return {@link String }
     */
    @Override
    public String processorJsonData(String responseContentType, String value, Boolean wrap) {
        // 没有则走原逻辑 有则走 处理器逻辑
        if (JsonUtil.isJsonContentType(responseContentType) && wrap) {
            value = JsonUtil.formatToAjax(value);
        }
        return value;
    }

    /**
     * 角色权限认证
     *
     * @param accessibleRoles 无障碍角色
     * @param roles           角色
     * @return boolean
     */
    @Override
    public boolean authentication(String accessibleRoles, String roles) {
        log.debug("accessibleRoles:{}", accessibleRoles);
        //如果为无角色限制，则直接返回 true
        if (!StringUtils.hasText(accessibleRoles)) {
            return true;
        }
        //解密
        String header = PropertiesUtil.headerDecryptStr(roles);
        log.debug("header:{}", header);
        if (!StringUtils.hasText(header)) {
            return false;
        }
        List<String> sourceRoles = Arrays.asList(header.split(","));
        List<String> targetRoles = Arrays.asList(accessibleRoles.split(","));
        HashSet<String> set = new HashSet<>();
        //取交集，如果交集数量少于两者之和，则说明有交集
        set.addAll(sourceRoles);
        set.addAll(targetRoles);
        int size = set.size();

        int sourceSize = sourceRoles.size();
        int targetSize = targetRoles.size();

        return size != sourceSize + targetSize;
    }

}
