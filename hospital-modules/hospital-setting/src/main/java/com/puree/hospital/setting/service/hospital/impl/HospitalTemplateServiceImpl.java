package com.puree.hospital.setting.service.hospital.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNode;
import cn.hutool.core.lang.tree.TreeUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.github.pagehelper.PageHelper;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.SecurityUtils;
import com.github.pagehelper.PageHelper;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.setting.config.HttpCacheProperties;
import com.puree.hospital.setting.constants.FilterModeEnum;
import com.puree.hospital.setting.constants.ItemTypeEnum;
import com.puree.hospital.setting.constants.RedisConstants;
import com.puree.hospital.setting.constants.RequestConstants;
import com.puree.hospital.setting.domain.dto.TemplateDefaultValueDTO;
import com.puree.hospital.setting.domain.dto.hospital.ItemUpdateDto;
import com.puree.hospital.setting.domain.dto.hospital.TemplateSaveDto;
import com.puree.hospital.setting.domain.dto.hospital.TemplateUpdateDto;
import com.puree.hospital.setting.domain.entity.hospital.HospitalItem;
import com.puree.hospital.setting.domain.entity.Menu;
import com.puree.hospital.setting.domain.entity.hospital.HospitalTemplate;
import com.puree.hospital.setting.domain.query.TemplateQuery;
import com.puree.hospital.setting.domain.query.TemplateQuery;
import com.puree.hospital.setting.domain.entity.platform.PlatformHistory;
import com.puree.hospital.setting.domain.vo.hospital.ItemsOperationsHomeVo;
import com.puree.hospital.setting.mapper.hospital.HospitalMenuMapper;
import com.puree.hospital.setting.mapper.hospital.HospitalTemplateMapper;
import com.puree.hospital.setting.mapper.platform.PlatformHistoryMapper;
import com.puree.hospital.setting.service.hospital.HospitalHistoryService;
import com.puree.hospital.setting.service.hospital.HospitalItemsService;
import com.puree.hospital.setting.service.hospital.HospitalMenuService;
import com.puree.hospital.setting.service.hospital.HospitalTemplateService;
import com.puree.hospital.setting.service.platform.PlatformConfigHttpApiService;
import com.puree.hospital.setting.utils.JsonUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/09/24
 */
@Slf4j
@Service
@AllArgsConstructor
public class HospitalTemplateServiceImpl implements HospitalTemplateService {
    private final HospitalTemplateMapper hospitalTemplateMapper;
    private final HospitalMenuService hospitalMenuService;
    private final HospitalHistoryService hospitalHistoryService;
    private final HospitalMenuMapper hospitalMenuMapper;
    private final PlatformConfigHttpApiService platformConfigHttpApiService;
    private final HospitalItemsService hospitalItemsService;
    private final RedisTemplate redisTemplate;
    private final HttpCacheProperties cacheProperties;
    private final PlatformHistoryMapper platformHistoryMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insert(TemplateSaveDto dto, HttpServletRequest request) {
        String key = dto.getKey();
        String name = dto.getName();
        Long menuId = dto.getMenuId();
        String type = dto.getType();
        String restraint = dto.getRestraint();
        if (key == null) {
            throw new IllegalArgumentException("配置模板 key 不能为空");
        }
        if (name == null) {
            throw new IllegalArgumentException("配置模板 name 不能为空");
        }
        if (menuId == null) {
            throw new IllegalArgumentException("配置模板 menuId 不能为空");
        }
        if (type == null) {
            throw new IllegalArgumentException("配置模板 type 不能为空");
        }
        if (restraint == null) {
            throw new IllegalArgumentException("配置模板 restraint 不能为空");
        }
        HospitalTemplate item = new HospitalTemplate();
        BeanUtils.copyProperties(dto, item);

        //追加菜单尾部
        Integer sort = hospitalTemplateMapper.queryTemplateMaxSortById(menuId);
        String username = request.getHeader(RequestConstants.DETAILS_USERNAME);
        item.setCreateBy(username);
        item.setUpdateBy(username);
        item.setCreateTime(new Date());
        item.setUpdateTime(new Date());
        item.setRevision(1);
        item.setSort(sort);
        item.setIsDelete(false);
        item.setType(ItemTypeEnum.getOrdinal(type));
        //判断是否重复 Name
        Long count = hospitalTemplateMapper.count(key, name, null);
        if (count > 0) {
            log.warn("配置项 key={},或 配置项 name={} 已存在", key, name);
            throw new IllegalArgumentException("配置项 key 或 配置项 name 已存在");
        }
        try {
            if (Boolean.FALSE.equals(hospitalTemplateMapper.save(item) >= 1)) {
                log.warn("新增配置项失败");
                throw new IllegalArgumentException("新增配置项失败");
            }
        } catch (DuplicateKeyException e) {
            log.warn("配置项 key={},或 配置项 name={} 已存在 请检查数据库", key, name);
            throw new IllegalArgumentException("配置项 key 或 配置项 name 已存在 请检查数据库");
        }
        hospitalHistoryService.insertHistory(item,null);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean delete(Long id, String itemsName, HttpServletRequest request) {
        if (CharSequenceUtil.isEmpty(itemsName)) {
            throw new IllegalArgumentException("配置项名称不正确");
        }
        HospitalTemplate hospitalTemplate = hospitalTemplateMapper.getById(id, 0);

        String username = request.getHeader(RequestConstants.DETAILS_USERNAME);
        if (Boolean.FALSE.equals(hospitalTemplateMapper.deleteByIdAndItemsName(id, itemsName, username) >= 1)) {
            log.warn("配置项不存在、配置项名称不正确，或配置项已被删除:id={},itemsName={}", id, itemsName);
            throw new IllegalArgumentException("配置项不存在、配置项名称不正确，或配置项已被删除");
        }
        PlatformHistory platformHistory = new PlatformHistory();
        BeanUtils.copyProperties(hospitalTemplate, platformHistory);
        platformHistory.setItemsId(hospitalTemplate.getId());
        platformHistory.setItemsKey(hospitalTemplate.getKey());
        platformHistory.setType(hospitalTemplate.getType());
        platformHistory.setSourceData(JsonUtil.toJson(hospitalTemplate));
        platformHistory.setTargetData("已删除");
        platformHistoryMapper.insert(platformHistory);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean update(TemplateUpdateDto dto, HttpServletRequest request) {
        log.debug("修改配置项:{}", dto);
        Long id = dto.getId();
        String name = dto.getName();
        String type = dto.getType();
        String restraint = dto.getRestraint();
        if (id == null) {
            throw new IllegalArgumentException("配置模板 id 不能为空");
        }
        if (name == null) {
            throw new IllegalArgumentException("配置模板 name 不能为空");
        }
        if (type == null) {
            throw new IllegalArgumentException("配置模板 type 不能为空");
        }
        if (restraint == null) {
            throw new IllegalArgumentException("配置模板 restraint 不能为空");
        }

        //判断是否重复 Name
        Long count = hospitalTemplateMapper.count(null, name, id);
        if (count > 0) {
            log.warn("配置项 name={} 已存在", name);
            throw new IllegalArgumentException("配置项 name 已存在");
        }
        HospitalTemplate item = new HospitalTemplate();
        BeanUtils.copyProperties(dto, item);
        item.setUpdateBy(request.getHeader(RequestConstants.DETAILS_USERNAME));
        item.setType(ItemTypeEnum.getOrdinal(type));
        item.setUpdateTime(new Date());
        //拿到当前数据
        HospitalTemplate hospitalTemplate = getCurrentVersionData(item);

        //记录历史数据
        hospitalHistoryService.insertHistory(item, hospitalTemplate);
        String username = request.getHeader(RequestConstants.DETAILS_USERNAME);
        item.setUpdateBy(username);
        Boolean update = updateTemplate(item);
        if (Boolean.FALSE.equals(update)) {
            log.warn("修改配置项失败:{}", item.getKey());
            throw new IllegalArgumentException("修改配置项失败");
        }
        item.setKey(hospitalTemplate.getKey());
        processTemplateRenewal(item);
        return true;
    }

    @Override
    public Object list(String blurSearch) {
        List<HospitalTemplate> list = blurSearch(blurSearch);

        List<TreeNode<Long>> treeNodes = new ArrayList<>();

        Tree<Long> trees = hospitalMenuService.menuListTreeSingle();

        //转换 VO 后输出
        List<ItemsOperationsHomeVo> collect = list.stream().map(item -> {
            ItemsOperationsHomeVo vo = new ItemsOperationsHomeVo();
            BeanUtils.copyProperties(item, vo);
            treeToNode(trees, vo.getMenuId(), treeNodes);
            vo.setType(ItemTypeEnum.getType(item.getType()));
            return vo;
        }).collect(Collectors.toList());

        Map<String, Object> map = new HashMap<>();
        map.put("list", collect);
        map.put("trees", TreeUtil.build(treeNodes, 0L));
        return map;
    }

    @Override
    public HospitalTemplate getById(Long id,boolean isDelete) {
        //根据 ID 查询
        HospitalTemplate hospitalTemplate = hospitalTemplateMapper.getById(id,isDelete?1:0);

        if (Objects.isNull(hospitalTemplate)) {
            log.warn("配置项不存在，或配置项已被删除:id={}", id);
            throw new IllegalArgumentException("配置项不存在，或配置项已被删除");
        }
        return hospitalTemplate;
    }


    @Override
    public Object itemsList(String blurSearch, Long hospitalId) {
        log.debug("查询配置项列表，模糊搜索：{}  {}", blurSearch, hospitalId);
        List<HospitalTemplate> list = hospitalTemplateMapper.itemsList(blurSearch, hospitalId);

        List<TreeNode<Long>> treeNodes = new ArrayList<>();

        Tree<Long> trees = hospitalMenuService.menuListTreeSingle();

        filterByWhitList(list, hospitalId);

        //转换 VO 后输出
        List<ItemsOperationsHomeVo> collect = list.stream().map(item -> {
            if (item.getItemValue() == null) {
                item.setItemValue(item.getDefaultValue());
            }
            ItemsOperationsHomeVo vo = new ItemsOperationsHomeVo();
            BeanUtils.copyProperties(item, vo);
            treeToNode(trees, vo.getMenuId(), treeNodes);
            vo.setType(ItemTypeEnum.getType(item.getType()));
            return vo;
        }).collect(Collectors.toList());

        Map<String, Object> map = new HashMap<>();
        map.put("list", collect);
        map.put("trees", TreeUtil.build(treeNodes, 0L));
        return map;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateItems(ItemUpdateDto dto) {
        log.debug("修改配置项updateItems:{}", dto);

        String itemValue = dto.getItemValue();

        //如果请求头中没有 hospitalId 则该次请求是 运营平台的 反之医院后台

        HospitalTemplate hospitalTemplate;
        Boolean flag;
        HospitalTemplate item = new HospitalTemplate();
        BeanUtils.copyProperties(dto, item);
        String username = dto.getUsername();
        item.setUpdateBy(username);
        hospitalTemplate = getCurrentVersionData(item);
        hospitalTemplate.setHospitalId(dto.getHospitalId());
        //医院没有配置该配置项
        if (hospitalTemplateMapper.countItems(hospitalTemplate) == 0) {
            //拿到当前数据
            item.setItemRevision(0);
            item.setId(hospitalTemplate.getId());
            item.setCreateBy(username);
            item.setUpdateBy(username);
            item.setUpdateTime(new Date());
            flag = hospitalTemplateMapper.insertItems(item, dto.getHospitalId().toString());
        } else {
            hospitalTemplate = getCurrentItemRevisionData(item);
            item.setId(hospitalTemplate.getId());
            //医院管理端调用没有版本信息
            if (item.getItemRevision() == null) {
                item.setItemRevision(hospitalTemplate.getItemRevision());
            }
            flag = hospitalTemplateMapper.updateItems(item,dto.getHospitalId().toString());
            item.setItemRevision(item.getItemRevision() + 2);
        }
        if (Boolean.FALSE.equals(flag)) {
            log.warn("修改配置项失败");
            throw new IllegalArgumentException("修改配置项失败");
        }


        //配置项 Redis 缓存
        cacheItemToRedis(hospitalTemplate, itemValue);
        //记录历史数据
        item.setKey(hospitalTemplate.getKey());
        hospitalHistoryService.insertHospitalHistory(item, hospitalTemplate, dto.getHospitalId());
        return true;
    }


    /**
     * @param hospitalTemplate 模版
     *  配置项缓存到 Redis
     * <AUTHOR>
     * @date 2024/12/9 16:02
     **/
    @Override
    public void cacheItemToRedis(HospitalTemplate hospitalTemplate, String itemValue) {
        // 1.空值检查
        if (hospitalTemplate == null ||
                // 2.缓存启用检查
                hospitalTemplate.getCacheMinutes() == null) {
            return;
        }

        String key = hospitalTemplate.getKey();
        Long hospitalId = hospitalTemplate.getHospitalId();
        Integer cacheMinutes = hospitalTemplate.getCacheMinutes();

        // 3.参数完整性检查
        if (CharSequenceUtil.isEmpty(key) || hospitalId == null ||
                CharSequenceUtil.isEmpty(itemValue)) {
            log.debug("必要参数缺失: key={}, hospitalId={}", key, hospitalId);
            return;
        }

        // 4.缓存时间有效性检查
        if (cacheMinutes <= 0) {
            log.debug("缓存时间无效 key:{} cacheMinutes:{}", key, cacheMinutes);
            return;
        }

        // 5.构建 Redis key
        String redisKey = CharSequenceUtil.format(RedisConstants.REDIS_CACHE_KEY, key, hospitalId);

        // 6.写入 Redis
        redisTemplate.opsForValue().set(redisKey, itemValue, cacheMinutes, TimeUnit.MINUTES);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Object updateSortOrMenuId(Long id, Integer sort, Long menuId) {
        //同时为空或者同时不为空都为参数错误，因为排序和菜单只能修改一个，不允许同时修改
        if ((sort == null && menuId == null) || (sort != null && menuId != null)) {
            throw new IllegalArgumentException("参数错误");
        }
        if (sort != null) {
            //修改排序
            return updateSort(id, sort);
        }
        //修改父级菜单
        return updateMenuId(id, menuId);
    }

    @Override
    public List<HospitalTemplate> getTemplateList(Menu menu, Long hospitalId, String roles) {
        // 获取菜单下所有模板基础信息
        TemplateQuery templateQuery = new TemplateQuery();
        templateQuery.setMenuId(menu.getId());
        templateQuery.setHospitalId(hospitalId);
        List<HospitalTemplate> hospitalTemplates = hospitalTemplateMapper.selectTemplateList(templateQuery);
        //进行白名单过滤
        filterByWhitList(hospitalTemplates, hospitalId);
        Iterator<HospitalTemplate> iterator = hospitalTemplates.iterator();
        while (iterator.hasNext()) {
            HospitalTemplate hospitalTemplate = iterator.next();
            if (Boolean.FALSE.equals(hospitalTemplate.getEnableHttp())) {
                iterator.remove();
            }
            //匿名访问 0 为关闭，1为开启
            if (Boolean.FALSE.equals(hospitalTemplate.getAllowAnonymous())) {
                if (!platformConfigHttpApiService.authentication(hospitalTemplate.getAllowedRoles(), roles)) {
                    iterator.remove();
                }
            }
            if (hospitalTemplate.getItemValue() == null) {
                hospitalTemplate.setItemValue(hospitalTemplate.getDefaultValue());
            }
        }
        return hospitalTemplates;
    }

    @Override
    public HospitalTemplate getTemplate(String key) {
        //根据 ID 查询
        HospitalTemplate templates = hospitalTemplateMapper.getTemplate(key);
        if (Objects.isNull(templates)) {
            log.warn("配置项不存在，或配置项已被删除:key={}", key);
            throw new IllegalArgumentException("配置项不存在，或配置项已被删除");
        }
        templates.setType(templates.getType() - 1);
        return templates;
    }


    @Override
    public Map<String, Object> getItemsAsMap(Menu menu, String roles, Long hospitalId) {
        // 获取菜单下所有模板基础信息
        TemplateQuery templateQuery = new TemplateQuery();
        templateQuery.setMenuId(menu.getId());
        templateQuery.setHospitalId(hospitalId);
        List<HospitalTemplate> hospitalTemplates = hospitalTemplateMapper.selectTemplateList(templateQuery);
        //进行白名单过滤
        filterByWhitList(hospitalTemplates, hospitalId);
        Map<String, Object> map = new HashMap<>(hospitalTemplates.size());
        hospitalTemplates.forEach(e -> {
            //HTTP访问 0 为关闭，1为开启
            if (Boolean.FALSE.equals(e.getEnableHttp())) {
                return;
            }
            //匿名访问 0 为关闭，1为开启
            if (Boolean.FALSE.equals(e.getAllowAnonymous())) {
                if (!platformConfigHttpApiService.authentication(e.getAllowedRoles(), roles)) {
                    return;
                }
            }
            // 没设置值使用默认值
            if (e.getItemValue() == null) {
                e.setItemValue(e.getDefaultValue());
            }
            Object value;
            if (JsonUtil.isJsonContentType(e.getContentType())) {
                value = JsonUtil.fromJson(e.getItemValue(), Object.class);
            } else {
                value = e.getItemValue();
            }
            map.put(e.getKey(), value);
        });
        return map;
    }

    @Override
    public Map<String, Object> getItemsAsMapAdmin(Menu menu, Long hospitalId) {
        // 获取菜单下所有模板基础信息
        TemplateQuery templateQuery = new TemplateQuery();
        templateQuery.setMenuId(menu.getId());
        templateQuery.setHospitalId(hospitalId);
        List<HospitalTemplate> hospitalTemplates = hospitalTemplateMapper.selectTemplateList(templateQuery);
        //进行白名单过滤
        Map<String, Object> map = new HashMap<>(hospitalTemplates.size());
        hospitalTemplates.forEach(e -> {
            // 没设置值使用默认值
            if (e.getItemValue() == null) {
                e.setItemValue(e.getDefaultValue());
            }
            Object value;
            if (JsonUtil.isJsonContentType(e.getContentType())) {
                value = JsonUtil.fromJson(e.getItemValue(), Object.class);
            } else {
                value = e.getItemValue();
            }
            map.put(e.getKey(), value);
        });
        return map;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateHospitalItems(List<ItemUpdateDto> items) {
        items.forEach(this::updateItems);
    }


    @Override
    public Boolean updateDefaultValue(TemplateDefaultValueDTO dto) {
        HospitalTemplate template = new HospitalTemplate();
        BeanUtils.copyProperties(dto, template);
        template.setUpdateBy(SecurityUtils.getUsername());

        HospitalTemplate byId = hospitalTemplateMapper.getById(dto.getId(),0);
        //模版默认值变更; 配置项值也需要变更
        if (!dto.getDefaultValue().equals(byId.getDefaultValue()) &&
                ItemTypeEnum.isJsonStr(byId.getType())){
            hospitalItemsService.batchUpdateItemValueForDefaultValue(dto.getId(), dto.getDefaultValue());
        }

        template.setUpdateTime(new Date());
        if (!SqlHelper.retBool(hospitalTemplateMapper.updateDefaultValue(template))){
            log.warn("修改默认值失败 dto:{}",dto);
            throw new ServiceException("配置项不存在，或配置项已被删除");
        }
        return true;
    }

    @Override
    public Boolean physicalDelete(Long id, String itemsName) {
        // 先删除医院配置数据，再删除模版（数据库存在外键关联）
        hospitalItemsService.deleteByTemplateId(id);
        if (Boolean.FALSE.equals(hospitalTemplateMapper.physicalDeleteByIdAndItemsName(id,itemsName) >= 1)) {
            log.warn("配置项不存在、配置项名称不正确，或配置项已被删除:id={},itemsName={}", id);
            throw new IllegalArgumentException("配置项不存在、配置项名称不正确，或配置项已被删除");
        }
        return true;
    }

    private Object updateMenuId(Long id, Long menuId) {
        //拿菜单 ID
        HospitalTemplate hospitalTemplate = hospitalTemplateMapper.getById(id, 0);

        if (Objects.equals(hospitalTemplate.getMenuId(), menuId)) {
            log.warn("源菜单与目标菜单相同，禁止修改");
            throw new IllegalArgumentException("源菜单与目标菜单相同，禁止修改");
        }
        //通过 ID 查询菜单最大排序 +1
        Integer sort = hospitalMenuService.queryMenuMaxSortById(menuId);
        return hospitalMenuMapper.updateMenuId(id, menuId, sort) >= 1;

    }

    private Object updateSort(Long id, Integer sort) {
        //拿菜单 ID
        HospitalTemplate hospitalTemplate = hospitalTemplateMapper.getById(id, 0);

        Integer sourceSort = hospitalTemplate.getSort();
        if (sourceSort.equals(sort)) {
            return true;
        } else if (sort > sourceSort) {
            //从后向前移动
            hospitalTemplateMapper.updateSortForward(hospitalTemplate.getMenuId(), sort);
        } else {
            //从前向后移动
            hospitalTemplateMapper.updateSortMoveBack(hospitalTemplate.getMenuId(), sort);
        }
        //大于 sort 值的后移
        //不需要锁，低概率事件
        if (Boolean.FALSE.equals(hospitalTemplateMapper.updateSort(id, sort) >= 1)) {
            log.warn("修改失败，请稍后重试");
            throw new RuntimeException("修改失败，请稍后重试");
        }
        //修改 sort
        return hospitalTemplateMapper.resetSort(hospitalTemplate.getMenuId()) >= 1;
    }

    private HospitalTemplate getCurrentItemRevisionData(HospitalTemplate template) {
        //拿到当前版本号数据
        HospitalTemplate hospitalTemplate = hospitalTemplateMapper.queryItem(template);

        if (Objects.isNull(hospitalTemplate)) {
            log.warn("配置项不存在，或配置项已被修改:EasyConfigItems.id={},revision={},key",
                    template.getId(), template.getRevision(), template.getKey());
            throw new IllegalArgumentException("配置项不存在，或配置项已被修改");
        }

        return hospitalTemplate;
    }


    @Override
    public void filterByWhitList(List<HospitalTemplate> templates, Long hospitalId) {
        Iterator<HospitalTemplate> iterator = templates.iterator();
        while (iterator.hasNext()) {
            HospitalTemplate hospitalTemplate = iterator.next();
            //如果医院未设置该配置项的值则使用默认值
            String filterMode = hospitalTemplate.getFilterMode();
            String filterList = hospitalTemplate.getFilterList();
            //白名单过滤
            if (FilterModeEnum.white_list.name().equals(filterMode)) {
                if (StringUtils.isEmpty(filterList)) {
                    iterator.remove();
                    continue;
                }
                String[] list = filterList.split(",");
                if (!Arrays.asList(list).contains(hospitalId.toString())) {
                    iterator.remove();
                    continue;
                }
            }
            //黑名单过滤
            if (FilterModeEnum.black_list.name().equals(filterMode)) {
                if (StringUtils.isEmpty(filterList)) {
                    continue;
                }
                String[] list = filterList.split(",");
                if (Arrays.asList(list).contains(hospitalId.toString())) {
                    iterator.remove();
                }
            }
        }
    }

    private void treeToNode(Tree<Long> trees, Long menuId, List<TreeNode<Long>> treeNodes) {
        Tree<Long> node = TreeUtil.getNode(trees, menuId);
        if (node != null) {
            TreeNode<Long> treeNode = new TreeNode<>();
            treeNode.setId(node.getId());
            treeNode.setParentId(node.getParentId());
            treeNode.setName(node.getName());
            treeNode.setExtra(MapUtil.builder("key", node.get("key")).build());
            treeNode.setWeight(node.getWeight());
            treeNodes.add(treeNode);
            if (node.getParentId() != null) {
                treeToNode(trees, node.getParentId(), treeNodes);
            }
        }
    }

    private List<HospitalTemplate> blurSearch(String blurSearch) {
        return hospitalTemplateMapper.list(blurSearch);
    }

    private Boolean updateTemplate(HospitalTemplate item) {
        if (Objects.isNull(item) || item.getId() == null) {
            throw new IllegalArgumentException("参数错误");
        }

        Integer revision = item.getRevision();

        //判断如果修改失败，则抛异常方便回滚
        if (Boolean.FALSE.equals(hospitalTemplateMapper.update(item, revision) >= 1)) {
            log.warn("并发冲突，请稍后重试");
            throw new RuntimeException("并发冲突，请稍后重试");
        }
        return true;
    }

    private HospitalTemplate getCurrentVersionData(HospitalTemplate hospitalTemplate) {
        //拿到当前版本号数据
        HospitalTemplate template = hospitalTemplateMapper.query(hospitalTemplate);
        if (Objects.isNull(template)) {
            log.warn("配置项不存在，或配置项已被修改:EasyConfigItems.key={},revision={},id={}", hospitalTemplate.getKey(), hospitalTemplate.getRevision(), hospitalTemplate.getId());
            throw new IllegalArgumentException("配置项不存在，或配置项已被修改");
        }

        return template;
    }


    /**
     * @param cacheMinutes 缓存分钟数
     * @return List<HospitalTemplate>
     *  查询指定缓存分钟数的数据
     * <AUTHOR>
     * @date 2024/12/9 18:59
     **/
    @Override
    public List<HospitalTemplate> getCacheMinutesTemplateList(Integer cacheMinutes) {
        return hospitalTemplateMapper.getCacheMinutesTemplateList(cacheMinutes);
    }


    /**
     * @return void
     *  永久续期
     * <AUTHOR>
     * @date 2024/12/9 19:04
     **/
    @Override
    public boolean renewPermanentItem() {

        Integer permanentCacheMinutes = cacheProperties.getPermanentCacheMinutes();
        if (permanentCacheMinutes == null || permanentCacheMinutes <= 0) {
            log.error("永久缓存时间配置无效");
            return false;
        }
        //1.查询指定缓存分钟数的模板数据
        List<HospitalTemplate> templateList = getCacheMinutesTemplateList(permanentCacheMinutes);


        return processTemplateRenewal(templateList);
    }

    /**
     * @param template 模版
     * @return boolean
     *  续期操作
     * <AUTHOR>
     * @date 2024/12/12 15:07
     **/
    @Override
    public boolean processTemplateRenewal(HospitalTemplate template) {
        return processTemplateRenewal(CollUtil.toList(template));
    }

    /**
     * @param templateList 模板列表
     * @return boolean
     *  续期操作
     * <AUTHOR>
     * @date 2024/12/12 14:56
     **/
    @Override
    public boolean processTemplateRenewal(List<HospitalTemplate> templateList) {
        if (CollUtil.isEmpty(templateList)) {
            log.info("模板下没有item数据");
            return true;
        }
        // 批量获取所有模板对应的配置项
        List<Long> templateIds = templateList.stream()
                .map(HospitalTemplate::getId)
                .collect(Collectors.toList());

        HashMap<Long, List<HospitalItem>> templateItemsMap = Optional.ofNullable(hospitalItemsService.getItemsByTemplateIds(templateIds))
                .orElse(Collections.emptyList())
                .stream()
                .filter(Objects::nonNull)
                .filter(item -> item.getTemplateId() != null)
                .collect(Collectors.groupingBy(
                        item -> Long.valueOf(item.getTemplateId()),
                        HashMap::new,
                        Collectors.toList()
                ));

        // 批量续期处理
        int successCount = 0;
        for (HospitalTemplate template : templateList) {
            List<HospitalItem> items = templateItemsMap.getOrDefault(template.getId(), Collections.emptyList());
            if (!CollUtil.isEmpty(items)) {
                for (HospitalItem item : items) {
                    template.setHospitalId(item.getHospitalId());
                    cacheItemToRedis(template, item.getItemValue());
                    successCount += 1;
                }
            }
        }

        log.info("缓存完成，成功缓存{}个项目", successCount);
        return true;
    }

    @Override
    public List<HospitalTemplate> getTemplateListAdmin(Menu menu, Long hospitalId) {
        // 获取菜单下所有模板基础信息
        TemplateQuery templateQuery = new TemplateQuery();
        templateQuery.setMenuId(menu.getId());
        templateQuery.setHospitalId(hospitalId);
        return hospitalTemplateMapper.selectTemplateList(templateQuery);
    }

    @Override
    public List<HospitalTemplate> getTemplateListAdmin(Menu menu, TemplateQuery query) {
        if (menu.getId() == null) {
            menu = hospitalMenuMapper.queryMenu(menu.getMenuKey());
        }
        // 获取菜单下所有模板基础信息
        Long menuId = menu == null ? null : menu.getId();
        PageHelper.startPage(query.getPageNum(),query.getPageSize());
        query.setMenuId(menuId);
        return hospitalTemplateMapper.selectTemplateList(query);
    }

}
