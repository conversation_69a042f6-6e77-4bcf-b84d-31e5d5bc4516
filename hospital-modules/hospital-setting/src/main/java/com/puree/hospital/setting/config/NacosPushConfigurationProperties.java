package com.puree.hospital.setting.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName EasyConfigNacosPushConfigurationProperties
 * <AUTHOR>
 * @Description Easy Config Nacos 推送配置属性
 * @Date 2023/12/8 17:26
 * @Version 1.0
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "com.puree.easy.config")
public class NacosPushConfigurationProperties {
    /**
     * 配送中心推送映射 map
     * key: 别名
     * value: nacos 记录的 dataId
     */
    private Map<String, String> aliasMap = new HashMap<>();
}
