package com.puree.hospital.setting.domain.entity.platform;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * @ClassName EasyConfigItems
 * <AUTHOR>
 * @Description 历史数据
 * @Date 2023/12/4 19:01
 * @Version 1.0
 */
@Data
public class PlatformHistory {

    /**
     * 主键
     */
    private Long id;

    /**
     * 元数据ID
     */
    private Long itemsId;
    /**
     * 菜单ID
     */
    private Long menuId;
    /**
     * 元数据key
     */
    private String itemsKey;
    /**
     * 历史数据类型;0为开发修改，1为运营修改，如果为开发修改则只需要记录元数据，如果为运营修改只需要记录value值
     */
    private Integer type;
    /**
     * 源数据
     */
    private String sourceData;
    /**
     * 目标数据
     */
    private String targetData;
    /**
     * 版本号;用于记录历史版本，版本号最大的为最新记录
     */
    private Integer version;
    /**
     * 创建人
     */
    private String createBy;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
     * 更新人
     */
    private String updateBy;
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}
