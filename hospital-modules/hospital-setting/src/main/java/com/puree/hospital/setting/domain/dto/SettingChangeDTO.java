package com.puree.hospital.setting.domain.dto;

/**
 * <p>
 * 业务配置更改 dto
 * </p>
 *
 * <AUTHOR>
 * @date 2024/12/4 12:25
 */
public abstract class SettingChangeDTO  {

    /**
     * 医院 ID
     *
     * @return {@link Long }
     */
    public abstract Long getHospitalId();
    /**
     * 配置项/模版 key
     *
     * @return {@link String }
     */
    public abstract String getKey();

    /**
     * 获取模板名称
     *
     * @return {@link String }
     */
    public abstract String getName();


    /**
     * 获取 ID
     *
     * @return {@link Long }
     */
    public abstract Long getId();


    /**
     * 获取 配置项的值
     *
     * @return {@link String }
     */
    public abstract String getItemsValue();

    /**
     * 获取默认值
     *
     * @return {@link String }
     */
    public abstract String getDefaultValue();
}
