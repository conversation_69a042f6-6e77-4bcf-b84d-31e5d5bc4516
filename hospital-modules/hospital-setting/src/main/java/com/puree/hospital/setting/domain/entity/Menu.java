package com.puree.hospital.setting.domain.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 医院模板菜单
 * <AUTHOR>
 * @date 2024/09/23
 */
@Data
public class Menu implements TemplateAuth{
    /**
     * 主键
     */
    private Long id;
    /**
     * 类型名称
     */
    private String menuName;
    /**
     * 类型 key
     */
    private String menuKey;
    /**
     * 父级 ID;一级为 -1
     */
    private Long parentId;
    /**
     * 排序字段，第几位展示
     */
    private Integer sort;
    /**
     * 说明
     */
    private String remark;
    /**
     * 乐观锁
     */
    private Integer revision;
    /**
     * 创建人
     */
    private String createBy;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
     * 更新人
     */
    private String updateBy;
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    /**
     * 删除标记;0未删除1已删除
     */
    private Boolean isDelete;

    /**
     * 允许匿名 0 不允许 1 允许
     */
    private Boolean allowAnonymous;
    /**
     * 启用 HTTP 访问 0 不启用 1 启用
     */
    private Boolean enableHttp;
    /**
     * 允许角色 以逗号分隔开的字符串
     */
    private String allowedRoles;

    @Override
    public boolean isHttpEnabled() {
        return Boolean.TRUE.equals(enableHttp);
    }

    @Override
    public boolean isAnonymousEnabled() {
        return Boolean.TRUE.equals(allowAnonymous);
    }
}