package com.puree.hospital.setting.domain.dto.platform;

import com.puree.hospital.setting.domain.dto.SettingChangeDTO;
import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName EasyConfigItemsOperationsUpdateDto
 * <AUTHOR>
 * @Description 运营修改配置项 dto
 * @Date 2023/12/7 17:36
 * @Version 1.0
 */
@Data
public class ItemsOperationsUpdateDto extends SettingChangeDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 配置项 ID
     */
    private Long id;
    /**
     * 配置项 value
     */
    private String value;
    /**
     * 乐观锁
     */
    private Integer revision;

    @Override
    public String getKey() {
        return "";
    }

    @Override
    public String getName() {
        return "";
    }

    @Override
    public String getItemsValue() {
        return value;
    }

    @Override
    public String getDefaultValue() {
        return "";
    }


    @Override
    public Long getHospitalId() {
        return null;
    }
    /**
     * 配置项 key
     */
    private String key;

}
