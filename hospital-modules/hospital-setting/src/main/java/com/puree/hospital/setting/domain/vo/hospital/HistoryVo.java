package com.puree.hospital.setting.domain.vo.hospital;

import com.puree.hospital.setting.constants.ItemTypeEnum;
import lombok.Data;

/**
 * 医院配置历史数据
 * <AUTHOR>
 * @date 2024/09/24
 */
@Data
public class HistoryVo {
    /**
     * 主键
     */
    private Long id;
    /**
     * key
     */
    private String key;
    /**
     * 菜单类型ID
     */
    private Long menuId;
    /**
     * 名称
     */
    private String name;
    /**
     * 描述
     */
    private String remark;
    /**
     * 排序字段
     */
    private Integer sort;
    /**
     * 类型;前端定义值是多少
     * 0 单行文本
     * 1 多行文本
     * 2 富文本
     * 3 数字
     * 4 是/否
     * 5 选择项
     * 6 日期时间
     * 7 表格
     * 8 动态字典
     * 9 静态字典
     */
    private Integer type;
    /**
     * 约束
     */
    private String restraint;
    /**
     * 是否开启 http api 访问;0 为关闭，1为开启
     */
    private Boolean enableHttp;
    /**
     * 是否开启Redis缓存;0 为关闭，1为开启
     */
    private Boolean enableCache;
    /**
     * 是否为匿名访问;0 为关闭，1为开启
     */
    private Boolean allowAnonymous;
    /**
     * 响应内容类型
     */
    private String contentType;
    /**
     * 可访问角色;角色与角色之间用,号隔开
     */
    private String allowedRoles;
    /**
     * 可显示该配置模板的医院id;医院id与医院id之间用,号隔开
     */
    private String whiteList;
    /**
     * 默认值
     */
    private String defaultValue;
    /**
     * 乐观锁
     */
    private Integer revision;

    /**
     * 配置项的过滤模式;
     * @see com.puree.hospital.setting.constants.FilterModeEnum
     */
    private String filterMode;

    /**
     * 配置项的过滤列表;
     * 配合 filterMode {@link com.puree.hospital.setting.constants.FilterModeEnum} 使用
     */
    private String filterList;


}
