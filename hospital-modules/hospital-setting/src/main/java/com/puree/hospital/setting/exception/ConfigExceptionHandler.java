package com.puree.hospital.setting.exception;


import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.setting.enums.ResultExceptionStateEnum;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * @ClassName EasyConfigException
 * <AUTHOR>
 * @Description 异常拦截器
 * @Date 2023/12/6 18:10
 * @Version 1.0
 */
@ControllerAdvice("com.puree.hospital.setting.controller")
@ResponseBody
@Slf4j
@Component
@AllArgsConstructor
public class ConfigExceptionHandler {

    /**
     * @Param ex
     * @Return com.puree.easycfg.domain.R
     * @Description 方法参数无效异常
     * <AUTHOR>
     * @Date 2023/12/6 18:16
     **/
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public R<?> methodArgumentNotValidExceptionHandler(MethodArgumentNotValidException ex) {
        FieldError fieldError = ex.getFieldError();
        String field = fieldError.getField();
        String defaultMessage = fieldError.getDefaultMessage();
        String format = String
                .format(ResultExceptionStateEnum.METHOD_ARGUMENT_NOTVALID_EXCEPTION.getMsg(),
                        field,
                        defaultMessage);
        log.error(format);
        return R.fail(format);
    }


    /**
     * @Param ex
     * @Return com.puree.easycfg.domain.R
     * @Description 缺少 Servlet 请求参数异常
     * <AUTHOR>
     * @Date 2023/12/8 14:47
     **/
    @ExceptionHandler(MissingServletRequestParameterException.class)
    public R<?> missingServletRequestParameterExceptionHandler(MissingServletRequestParameterException ex) {
        String format = String
                .format(ResultExceptionStateEnum.MISSING_SERVLET_REQUEST_PARAMETER_EXCEPTION.getMsg(),
                        ex.getMessage());
        log.error(format);
        return R.fail(format);
    }

    /**
     * @Param ex
     * @Return com.puree.easycfg.domain.R
     * @Description 非法参数异常
     * <AUTHOR>
     * @Date 2023/12/18 16:44
     **/
    @ExceptionHandler(IllegalArgumentException.class)
    public R<?> missingServletRequestParameterExceptionHandler(IllegalArgumentException ex) {

        String format = String
                .format(ResultExceptionStateEnum.ILLEGAL_ARGUMENT_EXCEPTION.getMsg(),
                        ex.getMessage());
        log.error(format);
        return R.fail(format);
    }
}
