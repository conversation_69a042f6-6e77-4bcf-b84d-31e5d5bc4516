package com.puree.hospital.setting.mapper.hospital;

import com.puree.hospital.setting.domain.entity.hospital.HospitalHistory;
import com.puree.hospital.setting.domain.entity.platform.PlatformHistory;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/09/23
 */
@Mapper
public interface HospitalHistoryMapper {


    /**
     * 查询医院配置历史数据
     * @param templateId 模版id
     * @param hospitalId 医院id
     * @return {@link List }<{@link HospitalHistory }> 医院配置历史数据
     */
    List<HospitalHistory> queryByItemIdAndHospitalId(@Param("templateId") Long templateId, @Param("hospitalId") Long hospitalId);

    Integer insert(@Param("hospitalHistory") HospitalHistory hospitalHistory);

    /**
     * 统计条数
     *
     * @param hospitalId
     * @param itemsKey
     * @return {@link Long }
     */
    Long queryByKeyCount(@Param("hospitalId") Integer hospitalId, @Param("itemsKey") String itemsKey);

    /**
     * 通过items_key 查询医院配置历史数据
     * @param itemsKey
     * @param hospitalId
     * @param pageSize
     * @param pageNum
     * @return {@link List }<{@link PlatformHistory }>
     */
    List<HospitalHistory> queryByKey(@Param("itemsKey") String itemsKey, @Param("hospitalId") Integer hospitalId, @Param("pageSize") Integer pageSize, @Param("pageNum") Integer pageNum);
}
