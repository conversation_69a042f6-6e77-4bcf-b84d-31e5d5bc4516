package com.puree.hospital.setting.aspect;

import cn.hutool.core.util.StrUtil;
import com.puree.hospital.common.security.token.TokenService;
import com.puree.hospital.setting.annotation.SettingChangeNotify;
import com.puree.hospital.setting.domain.dto.SettingChangeDTO;
import com.puree.hospital.setting.domain.entity.Template;
import com.puree.hospital.setting.model.event.SettingChangeEvent;
import com.puree.hospital.setting.queue.producer.SettingChangeProducer;
import com.puree.hospital.setting.service.hospital.HospitalTemplateService;
import com.puree.hospital.setting.service.platform.PlatformItemsService;
import com.puree.hospital.setting.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;

/**
 * 业务配置 模版、设置，变更通知 切面
 *
 * <AUTHOR>
 */
@Slf4j
@Aspect
@Component
public class SettingChangeNotifyAspect {


    @Lazy
    @Resource
    SettingChangeProducer settingChangeProducer;


    @Resource
    private TokenService tokenService;

    @Value("${spring.profiles.active}")
    private String environment;

    @Resource
    private HospitalTemplateService hospitalTemplateService;

    @Resource
    private PlatformItemsService platformItemsService;

    /**
     * 飞书卡变 变更数据显示最大文本长度
     */
    public static final int MAX_BODY_SIZE = 384;


    @Pointcut("@annotation(com.puree.hospital.setting.annotation.SettingChangeNotify)")
    public void notifyPointcut() {
    }


    @Around("notifyPointcut()")
    public Object around(ProceedingJoinPoint point) throws Throwable {
        Object result = point.proceed();
        try {
            Object[] args = point.getArgs();
            log.debug("通知切面，参数：{}", args);
            Signature signature = point.getSignature();
            MethodSignature methodSignature = (MethodSignature) signature;
            Method method = methodSignature.getMethod();
            SettingChangeNotify annotation = method.getAnnotation(SettingChangeNotify.class);
            SettingChangeEvent event = null;
            if (SettingChangeEvent.Operate.DELETE.equals(annotation.operate())) {
                Template template = null;
                if (SettingChangeEvent.Classify.HOSPITAL.equals(annotation.classify())) {
                    Long templateId = (Long) args[0];
                    template = hospitalTemplateService.getById(templateId, true);
                } else {
                    template = platformItemsService.getById((Long) args[0], true);
                }
                event = getSettingChangeEvent(annotation, template);
            } else {
                // 获取通知参数
                Object arg = args[0];
                if (!(arg instanceof SettingChangeDTO)) {
                    return result;
                }
                SettingChangeDTO settingChangeDTO = (SettingChangeDTO) arg;
                event = getSettingChangeEvent(annotation, settingChangeDTO);
            }

            // 发送通知
            settingChangeProducer.send(event);
        } catch (Throwable e) {
            log.error("通知切面异常：{}", e.getMessage());
        }
        return result;
    }

    private SettingChangeEvent getSettingChangeEvent(SettingChangeNotify annotation, SettingChangeDTO settingChangeDTO) {
        // 构造通知事件
        SettingChangeEvent event = beforeSetParams(annotation, settingChangeDTO.getKey(), settingChangeDTO.getName());
        event.setHospitalId(settingChangeDTO.getHospitalId());
        String itemsValue = settingChangeDTO.getItemsValue();
        if (StrUtil.isEmpty(itemsValue)) {
            itemsValue = settingChangeDTO.getDefaultValue();
        }
        afterSetParams(event, settingChangeDTO.getId(), annotation, itemsValue);
        return event;
    }

    private void afterSetParams(SettingChangeEvent event, Long settingChangeDTO, SettingChangeNotify annotation, String value) {
        event.setId(settingChangeDTO);
        event.setEnvironment(environment);
        String json = "";
        if (SettingChangeEvent.Type.ITEMS.equals(annotation.type())) {
            json = JsonUtil.toJson(value);
        }
        byte[] bytes = json.getBytes(StandardCharsets.UTF_8);
        // 限制文本大小
        if (bytes.length > MAX_BODY_SIZE) {
            event.setRequestBody(json.substring(0, MAX_BODY_SIZE));
        } else {
            event.setRequestBody(json);
        }
    }

    private SettingChangeEvent getSettingChangeEvent(SettingChangeNotify annotation, Template template) {
        // 构造通知事件
        SettingChangeEvent event = beforeSetParams(annotation, template.getKey(), template.getName());
        event.setHospitalId(null);
        String itemsValue = template.getItemsValue();
        if (StrUtil.isEmpty(itemsValue)) {
            itemsValue = template.getDefaultValue();
        }
        afterSetParams(event, template.getId(), annotation, itemsValue);
        return event;
    }

    private SettingChangeEvent beforeSetParams(SettingChangeNotify annotation, String key,String name) {
        SettingChangeEvent event = new SettingChangeEvent();
        event.setKey(key);
        event.setTemplateName(name);
        event.setClassify(annotation.classify());
        event.setOperate(annotation.operate());
        event.setTime(LocalDateTime.now().toString());
        event.setOperator(tokenService.getLoginUser().getUsername());
        event.setType(annotation.type());
        return event;
    }


}
