package com.puree.hospital.setting.converter;


import com.fasterxml.jackson.databind.JsonNode;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.springframework.http.HttpOutputMessage;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageNotWritableException;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.io.StringWriter;

/**
 * 将JsonArray对象转换成CSV格式
 *
 * <AUTHOR>
 * @date 2024/12/30 19:42
 */
@Component
public class CsvHttpMessageConverter extends WriteOnlyHttpMessageConverter<JsonNode> {

    public static final MediaType CSV_TYPE = MediaType.valueOf("text/csv");

    public CsvHttpMessageConverter() {
        super(CSV_TYPE);
    }

    @Override
    protected void writeInternal(JsonNode jsonNode, @NonNull HttpOutputMessage outputMessage)
            throws IOException, HttpMessageNotWritableException {
        StringWriter csvWriter = new StringWriter();
        CSVPrinter csvPrinter = new CSVPrinter(csvWriter, CSVFormat.DEFAULT);

        // Write header
        JsonNode firstObject = jsonNode.elements().next();
        firstObject.fieldNames().forEachRemaining(fieldName -> {
            try {
                csvPrinter.print(fieldName);
            } catch (IOException e) {
                throw new RuntimeException("Error writing CSV header", e);
            }
        });
        csvPrinter.println();

        // Write data rows
        for (JsonNode node : jsonNode) {
            firstObject.fieldNames().forEachRemaining(fieldName -> {
                try {
                    csvPrinter.print(node.get(fieldName).asText());
                } catch (IOException e) {
                    throw new RuntimeException("Error writing CSV data", e);
                }
            });
            csvPrinter.println();
        }

        csvPrinter.flush();
        outputMessage.getBody().write(csvWriter.toString().getBytes());
    }
}