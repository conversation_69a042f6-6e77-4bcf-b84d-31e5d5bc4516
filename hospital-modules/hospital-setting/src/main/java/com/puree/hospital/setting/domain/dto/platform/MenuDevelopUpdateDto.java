package com.puree.hospital.setting.domain.dto.platform;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * @ClassName EasyConfigMenuDevelopUpdateDto
 * <AUTHOR>
 * @Description 配置项菜单修改
 * @Date 2023/12/8 16:22
 * @Version 1.0
 */
@Data
public class MenuDevelopUpdateDto implements Serializable {

    /**
     * 主键
     */
    private Long id;
    /**
     * 类型名称
     */
    @NotBlank(message = "类型名称 不能为空")
    @Size(min = 0, max = 255, message = "参数 类型名称 不能超过 255 个字符")
    private String menuName;
    /**
     * 说明
     */
    @Size(min = 0, max = 255, message = "参数 说明 不能超过 255 个字符")
    private String remark;
    /**
     * 可访问角色;角色与角色之间用,号隔开
     */
    private String allowedRoles;
    /**
     * 是否为匿名访问;0 为关闭，1为开启
     */
    private Boolean allowAnonymous;
    /**
     * 是否开启 http api 访问;0 为关闭，1为开启
     */
    private Boolean enableHttp;

}
