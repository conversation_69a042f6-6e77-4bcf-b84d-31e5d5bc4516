package com.puree.hospital.setting.service.platform.impl;

import com.puree.hospital.setting.config.NacosConfigurationProperties;
import com.puree.hospital.setting.domain.nacos.NacosConfigAll;
import com.puree.hospital.setting.domain.nacos.NacosLogin;
import com.puree.hospital.setting.domain.nacos.NacosPage;
import com.puree.hospital.setting.domain.nacos.query.NacosConfigUpdateWrapper;
import com.puree.hospital.setting.domain.nacos.query.NacosLoginWrapper;
import com.puree.hospital.setting.domain.nacos.query.NacosPageSearchQueryWrapper;
import com.puree.hospital.setting.service.platform.NacosService;
import com.puree.hospital.setting.utils.JsonUtil;
import com.puree.hospital.setting.utils.NacosTokenCache;
import com.puree.hospital.setting.utils.PropertiesUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;


import java.util.Objects;
import java.util.Properties;


/**
 * @ClassName EasyNacosServiceImpl
 * <AUTHOR>
 * @Description Nacos 实现类
 * @Date 2023/12/4 15:26
 * @Version 1.0
 */
@Slf4j
@Service
public class NacosServiceImpl implements NacosService {


    @Autowired
    private NacosConfigurationProperties nacosConfigurationProperties;
    @Autowired
    private RestTemplate restTemplate;

    /**
     * @Param
     * @Return com.puree.easycfg.domain.nacos.NacosLogin
     * @Description 登录
     * <AUTHOR>
     * @Date 2023/12/4 15:53
     **/
    @Override
    public NacosLogin nacosLogin() {
        //过期，重新登录 获取token
        ResponseEntity<String> response = NacosLoginWrapper.builder()
                .username(nacosConfigurationProperties.getUsername())
                .password(nacosConfigurationProperties.getPassword())
                .address(nacosConfigurationProperties.getServerAddr())
                .restTemplate(restTemplate)
                .build().sendPostRequest();
        if (response == null || !response.getStatusCode().is2xxSuccessful()) {
            log.error("获取accessToken失败，请重试");
            return null;
        }
        String body = response.getBody();
        if (StringUtils.isEmpty(body)) {
            log.error("获取accessToken失败，请重试");
            return null;
        }
        log.debug("获取accessToken成功:{}", body);
        return JsonUtil.fromJson(body, NacosLogin.class);
    }

    /**
     * @Param dataId
     * @Return com.puree.easycfg.domain.nacos.NacosPage
     * @Description 模糊查询分页, 第一页前十条，需要手动带上 * 号，如：*test*、*.yaml、application.* 等
     * <AUTHOR>
     * @Date 2023/12/4 16:04
     **/
    @Override
    public NacosPage blurPage(String dataId) {
        return page(dataId, 1, 10, "blur");
    }

    /**
     * 获取访问令牌
     *
     * @return {@link String }
     */
    @Override
    public String getAccessToken() {
        //判断token是否过期
        if (System.currentTimeMillis() > NacosTokenCache.EXPIRE_TIME) {
            NacosLogin nacosLogin = nacosLogin();
            //将token缓存到本地
            NacosTokenCache.EXPIRE_TIME = System.currentTimeMillis() + Long.parseLong(nacosLogin.getTokenTtl() + "000") - (60 * 1000);
            NacosTokenCache.ACCESS_TOKEN = nacosLogin.getAccessToken();
        }
        return NacosTokenCache.ACCESS_TOKEN;
    }

    /**
     * @Param dataId
     * @Param pageNo 第几页
     * @Param pageSize 一页多少条
     * @Param search blur为模糊查询，accurate 为精确查询
     * @Return com.puree.easycfg.domain.nacos.NacosPage
     * @Description 查询分页，
     * <AUTHOR>
     * @Date 2023/12/4 16:11
     **/
    public NacosPage page(String dataId, Integer pageNo, Integer pageSize, String search) {
        ResponseEntity<String> response = NacosPageSearchQueryWrapper
                .builder()
                .dataId(dataId)
                .restTemplate(restTemplate)
                .nacoscConfigProperties(nacosConfigurationProperties)
                .search(search)
                .pageNo(pageNo)
                .pageSize(pageSize)
                .accessToken(getAccessToken())
                .build()
                .sendGetRequest();
        log.debug("查询分页 执行结果:{}", response);
        return JsonUtil.fromJson(response.getBody(), NacosPage.class);
    }

    /**
     * @Param dataId
     * @Return com.puree.easycfg.domain.nacos.NacosPage
     * @Description 精确查询分页，第一页前十条
     * <AUTHOR>
     * @Date 2023/12/4 16:19
     **/
    @Override
    public NacosPage accuratePage(String dataId) {
        return page(dataId, 1, 10, "accurate");
    }

    /**
     * @Param dataId
     * @Param show all 为全部，不填写为只读取 content，默认是空
     * @Return com.puree.easycfg.domain.nacos.NacosConfigAll
     * @Description 精确查询配置文件信息
     * <AUTHOR>
     * @Date 2023/12/4 16:28
     **/
    @Override
    public NacosConfigAll accurateNacosConfig(String dataId, String show) {
        ResponseEntity<String> response = NacosPageSearchQueryWrapper
                .builder()
                .restTemplate(restTemplate)
                .nacoscConfigProperties(nacosConfigurationProperties)
                .dataId(dataId)
                .accessToken(getAccessToken())
                .show(show)
                .build()
                .sendGetRequest();
        log.debug("精确查询配置文件信息:{}", response);
        String body = response.getBody();
        //all 为全量信息
        if (Objects.equals(show, "all")) {
            return JsonUtil.fromJson(body, NacosConfigAll.class);
        }
        //否则只查 content
        NacosConfigAll bean = new NacosConfigAll();
        bean.setContent(body);
        return bean;
    }

    /**
     * @Param dataId
     * @Return java.lang.String
     * @Description 精确查询配置文件内容，只返回 content 的数据
     * <AUTHOR>
     * @Date 2023/12/4 17:05
     **/
    @Override
    public String accurateNacosConfigContent(String dataId) {
        NacosConfigAll nacosConfigAll = accurateNacosConfig(dataId, "");
        log.debug("精确查询配置文件内容，只返回 content 的数据:{}", nacosConfigAll);
        if (nacosConfigAll == null) {
            return "";
        }
        return nacosConfigAll.getContent();
    }

    /**
     * @Param dataId
     * @Param key
     * @Param value
     * @Return java.lang.Boolean
     * @Description 新增或修改配置文件 key
     * <AUTHOR>
     * @Date 2023/12/4 17:22
     **/
    @Override
    public Boolean updateNacosConfigItem(String dataId, String key, String value) {
        //拿到最开始的内容数据
        String content = accurateNacosConfigContent(dataId);
        Properties properties = PropertiesUtil.stringConversionProperties(content);
        properties.put(key, value);
        ResponseEntity<String> response = NacosConfigUpdateWrapper.builder()
                .dataId(dataId)
                .restTemplate(restTemplate)
                .nacosConfigurationProperties(nacosConfigurationProperties)
                .accessToken(getAccessToken())
                .content(PropertiesUtil.propertiesConversionString(properties))
                .build()
                .sendPostRequest();
        log.debug("新增或修改配置文件 执行结果:{}", response);
        return response.getStatusCode().is2xxSuccessful();
    }

    /**
     * @Param dataId
     * @Param properties
     * @Return java.lang.Boolean
     * @Description 全量替换配置文件
     * <AUTHOR>
     * @Date 2023/12/12 17:54
     **/
    @Override
    public Boolean FullReplacementNacosConfig(String dataId, Properties properties) {
        ResponseEntity<String> response = NacosConfigUpdateWrapper.builder()
                .dataId(dataId)
                .restTemplate(restTemplate)
                .nacosConfigurationProperties(nacosConfigurationProperties)
                .accessToken(getAccessToken())
                .content(PropertiesUtil.propertiesConversionString(properties))
                .build()
                .sendPostRequest();

        log.debug("写入配置文件 执行结果:{}", response);
        return response.getStatusCode().is2xxSuccessful();
    }

    /**
     * @Param dataId
     * @Param key
     * @Return java.lang.Boolean
     * @Description 删除配置文件 key
     * <AUTHOR>
     * @Date 2023/12/8 19:00
     **/
    @Override
    public Boolean deleteNacosConfigItem(String dataId, String key) {
        //拿到最开始的内容数据
        String content = accurateNacosConfigContent(dataId);
        Properties properties = PropertiesUtil.stringConversionProperties(content);
        properties.remove(key);
        ResponseEntity<String> response = NacosConfigUpdateWrapper.builder()
                .dataId(dataId)
                .restTemplate(restTemplate)
                .nacosConfigurationProperties(nacosConfigurationProperties)
                .accessToken(getAccessToken())
                .content(PropertiesUtil.propertiesConversionString(properties))
                .build()
                .sendPostRequest();
        log.debug("删除配置文件 key:{} 执行结果:{}", key, response);
        return response.getStatusCode().is2xxSuccessful();
    }

}
