package com.puree.hospital.setting.domain.entity.platform;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @ClassName EasyConfigPushBind
 * <AUTHOR>
 * @Description 配置项推送绑定表
 * @Date 2023/12/18 17:36
 * @Version 1.0
 */
@Data

public class PlatformPushBind implements Serializable{

    /**
     * 主键
     */

    private Long  id ;
    /**
     * 配置项ID
     */
    private Long itemId ;
    /**
     * 推送到配置中心的别名
     */
    private String pushAliasName ;
    /**
     * 推送到配置中心的实际名称
     */
    private String pushName ;
    /**
     * 创建人
     */
    private String createBy;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
     * 更新人
     */
    private String updateBy;
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    /**
     * 删除标记;0未删除1已删除
     */
    private Boolean delFlag;

}
