package com.puree.hospital.setting.mapper.platform;


import com.puree.hospital.setting.domain.entity.platform.PlatformItemsPushDo;
import com.puree.hospital.setting.domain.entity.platform.PlatformPushBind;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * @ClassName EasyConfigPushBindMapper
 * <AUTHOR>
 * @Description 配置项推送绑定表
 * @Date 2023/12/18 17:40
 * @Version 1.0
 */
@Mapper
public interface PlatformPushBindMapper {
    /**
     * @Param push
     * @Return java.util.List<com.puree.easycfg.domain.easy.EasyConfigItemsPushDo>
     * @Description 获取绑定关系
     * <AUTHOR>
     * @Date 2023/12/18 19:22
     **/
    List<PlatformItemsPushDo> getBindingRelationship(@Param("platformItems") Collection<String> push);

    /**
     * @Param easyConfigPushBind
     * @Return java.lang.Boolean
     * @Description 新增绑定关系
     * <AUTHOR>
     * @Date 2023/12/18 19:22
     **/
    Boolean insert(PlatformPushBind platformPushBind);

    /**
     * @Param itemId
     * @Return java.lang.Boolean
     * @Description 删除绑定关系
     * <AUTHOR>
     * @Date 2023/12/19 12:09
     **/
    Boolean deleteBindingRelationship(@Param("itemId")Long itemId);

    List<PlatformPushBind> getBindingList(Long id);
}
