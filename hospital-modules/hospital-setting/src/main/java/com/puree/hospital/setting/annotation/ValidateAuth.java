package com.puree.hospital.setting.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @date 2024/12/25
 * 自定义HTTP_API校验注解，校验是否有开启HTTP_API权限，是否匿名访问，以及是否有权限访问某个接口
 * <p>默认校验范围为平台，如果需要校验医院权限，则需要在注解上添加scope参数，如：@ValidateAuth(scope = Scope.HOSPITAL)</p>
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface ValidateAuth {

    /**
     * 校验接口的权限范围，默认值为Scope.PLATFORM
     */
    Scope value() default Scope.PLATFORM_ITEM;


    /**
     * 校验接口的配置项或菜单项的key的参数名，默认值为key
     */
    String key() default "key";

    enum Scope {
        HOSPITAL_MENU,
        HOSPITAL_ITEM,
        PLATFORM_MENU,
        PLATFORM_ITEM
    }
}
