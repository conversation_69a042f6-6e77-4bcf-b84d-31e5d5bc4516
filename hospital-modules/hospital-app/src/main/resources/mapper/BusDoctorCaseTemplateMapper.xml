<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.puree.hospital.app.mapper.BusDoctorCaseTemplateMapper">

    <resultMap type="com.puree.hospital.app.domain.BusDoctorCaseTemplate" id="BusDoctorCaseTemplateResult">
        <result property="id" column="id" />
        <result property="createTime" column="create_time" />
        <result property="updateTime" column="update_time" />
        <result property="templateName" column="template_name" />
        <result property="doctorId" column="doctor_id" />
        <result property="hospitalId" column="hospital_id" />
        <result property="chiefComplaint" column="chief_complaint" />
        <result property="historyOfPresentIllness" column="history_of_present_illness" />
        <result property="pastHistory" column="past_history" />
        <result property="diagnosis" column="diagnosis" />
        <result property="tcmDiagnosis" column="tcm_diagnosis" />
        <result property="allergicDrugs" column="allergic_drugs" />
        <result property="advice" column="advice" />
    </resultMap>

    <sql id="selectBusDoctorCaseTemplateVo">
        select id, create_time, update_time, template_name, doctor_id, hospital_id,
               chief_complaint, history_of_present_illness, past_history, diagnosis,
               tcm_diagnosis, allergic_drugs, advice
        from bus_doctor_case_template
    </sql>

    <!-- 查询医生的模板列表 -->
    <select id="selectByDoctorId" parameterType="java.util.Map" resultMap="BusDoctorCaseTemplateResult">
        <include refid="selectBusDoctorCaseTemplateVo"/>
        where doctor_id = #{doctorId} 
          and hospital_id = #{hospitalId}
        order by create_time desc
    </select>

</mapper>
