package com.puree.hospital.app.controller;

import cn.hutool.core.util.ObjectUtil;
import com.aliyun.oss.ServiceException;
import com.puree.hospital.app.domain.BusDoctorCaseTemplate;
import com.puree.hospital.app.domain.dto.BusDoctorCaseTemplateCreateDTO;
import com.puree.hospital.app.domain.dto.BusDoctorCaseTemplateUpdateDTO;
import com.puree.hospital.app.service.IBusDoctorCaseTemplateService;
import com.puree.hospital.app.service.IBusDoctorHospitalService;
import com.puree.hospital.common.api.domain.Paging;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.common.core.base.page.PageUtil;
import com.puree.hospital.common.core.utils.SecurityUtils;
import com.puree.hospital.common.core.web.controller.BaseController;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 医生病历模板控制器
 * <AUTHOR>
 * @date 2025-08-08
 */
@Slf4j
@RestController
@RequestMapping("/case-template")
public class BusDoctorCaseTemplateController extends BaseController {

    @Resource
    private IBusDoctorCaseTemplateService doctorCaseTemplateService;

    @Resource
    private IBusDoctorHospitalService doctorHospitalService;

    /**
     * 获取医生的模板列表
     * @return 模板列表
     */
//    @GetMapping("/list")
//    public TableDataInfo list(@RequestParam("hospitalId") Long hospitalId, @RequestParam("pageNum") Integer pageNum, @RequestParam("pageSize") Integer pageSize) {
//        Long doctorId = SecurityUtils.getUserId();
//        checkPermission(doctorId, hospitalId);
//        startPage();
//        List<BusDoctorCaseTemplate> templateList = doctorCaseTemplateService.getTemplateList(doctorId, hospitalId);
//        return getDataTable(templateList);
//    }
    @GetMapping("/list")
    public Paging<List<BusDoctorCaseTemplate>> list(@RequestParam("hospitalId") Long hospitalId,
                                                    @RequestParam("pageNum") Integer pageNum,
                                                    @RequestParam("pageSize") Integer pageSize) {
        Long doctorId = SecurityUtils.getUserId();
        checkPermission(doctorId, hospitalId);
        PageUtil.startPage();  // 使用PageUtil
        List<BusDoctorCaseTemplate> templateList = doctorCaseTemplateService.getTemplateList(doctorId, hospitalId);
        return PageUtil.buildPage(templateList);
    }

    /**
     * 根据ID获取模板详情
     * @param id 模板ID
     * @return 模板详情
     */
    @GetMapping("/{id}")
    public R<BusDoctorCaseTemplate> getById(@PathVariable Long id) {
        BusDoctorCaseTemplate template = doctorCaseTemplateService.getCheckedTemplate(id, SecurityUtils.getUserId());
        return R.ok(template);
    }

    /**
     * 新增模板
     * @param createDto 新增模板数据
     * @return 新增结果
     */
    @PostMapping
    public R<String> add(@Validated @RequestBody BusDoctorCaseTemplateCreateDTO createDto) {
        Long hospitalId = SecurityUtils.getHospitalId();
        Long doctorId = SecurityUtils.getUserId();
        checkPermission(doctorId, hospitalId);
        doctorCaseTemplateService.insertTemplate(createDto, doctorId, hospitalId);
        return R.ok("新增成功");
    }

    /**
     * 更新模板
     * @param updateDto 更新模板数据
     * @return 更新结果
     */
    @PutMapping
    public R<String> edit(@Validated @RequestBody BusDoctorCaseTemplateUpdateDTO updateDto) {
        Long doctorId = SecurityUtils.getUserId();
        doctorCaseTemplateService.updateTemplate(updateDto, doctorId);
        return R.ok("更新成功");
    }

    /**
     * 删除模板
     * @param id 模板ID
     * @return 删除结果
     */
    @DeleteMapping("/{id}")
    public R<String> delete(@PathVariable Long id) {
        Long doctorId = SecurityUtils.getUserId();
        doctorCaseTemplateService.deleteTemplate(id, doctorId);
        return R.ok("删除成功");
    }

    /**
     * 校验权限
     * @param doctorId 医生ID
     * @param hospitalId 医院ID
     */
    private void checkPermission(Long doctorId, Long hospitalId) {
        if (ObjectUtil.isNull(doctorHospitalService.selectDoctorHospitalInfo(hospitalId, doctorId))) {
            throw new ServiceException("医生和医院无关联");
        }
    }

}
