package com.puree.hospital.app.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 医生病历模板实体
 * <AUTHOR>
 * @date 2025-08-08
 */
@Data
@TableName("bus_doctor_case_template")
public class BusDoctorCaseTemplate {

    /**
     * 主键ID
     */
    @TableId(type= IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(fill = FieldFill.UPDATE)
    private Date updateTime;

    /** 
     * 模板名称 
     */
    private String templateName;

    /** 
     * 医生ID 
     */
    private Long doctorId;

    /** 
     * 医院ID 
     */
    private Long hospitalId;

    /** 
     * 主诉 
     */
    private String chiefComplaint;

    /** 
     * 现病史 
     */
    private String historyOfPresentIllness;

    /** 
     * 既往史 
     */
    private String pastHistory;

    /** 
     * 西医诊断 
     */
    private String diagnosis;

    /** 
     * 中医诊断 
     */
    private String tcmDiagnosis;

    /** 
     * 过敏史 
     */
    private String allergicDrugs;

    /** 
     * 医嘱 
     */
    private String advice;


}
