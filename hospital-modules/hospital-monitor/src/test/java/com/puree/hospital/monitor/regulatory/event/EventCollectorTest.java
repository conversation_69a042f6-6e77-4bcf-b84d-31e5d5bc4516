//package com.puree.hospital.monitor.regulatory.event;
//
//import com.puree.hospital.common.rabbitmq.producer.PureeRabbitProducer;
//import com.puree.hospital.monitor.AbstractHospitalMonitorTest;
//import com.puree.hospital.monitor.api.event.constant.EventMessageQueueConstant;
//import com.puree.hospital.monitor.api.event.enums.RegulatoryEventTypeEnum;
//import com.puree.hospital.monitor.api.event.regulatory.collect.RegulatoryCollectEvent;
//import org.junit.Test;
//
//import javax.annotation.Resource;
//
///**
// * <p>
// * 事件采集测试
// * </p>
// *
// * <AUTHOR>
// * @date 2024/7/10 8:43
// */
//public class EventCollectorTest extends AbstractHospitalMonitorTest {
//
//    @Resource
//    private PureeRabbitProducer pureeRabbitProducer;
//
//    @Test
//    public void testSendHospitalEvent() {
//        RegulatoryCollectEvent event = new RegulatoryCollectEvent(30L, RegulatoryEventTypeEnum.HOSPITAL_FILING, "30");
//        pureeRabbitProducer.send(EventMessageQueueConstant.REGULATORY_MONITOR_COLLECTOR_EXCHANGE, EventMessageQueueConstant.REGULATORY_MONITOR_COLLECTOR_TOPIC, event);
//
//    }
//
//}
