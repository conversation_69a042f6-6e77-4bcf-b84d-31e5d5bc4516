package com.puree.hospital.monitor.regulatory.event.assembler.yunnan;

import com.puree.hospital.monitor.HospitalMonitorApplication;
import com.puree.hospital.monitor.api.event.regulatory.report.yunnan.YnPrescriptionWmCreate;
import com.puree.hospital.monitor.common.domain.BusPrescription;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

@SpringBootTest(classes = HospitalMonitorApplication.class)
public class YnPrescriptionWmCreateEventDataAssemblerTest {

    @Resource
    private YnPrescriptionWmCreateEventDataAssembler ynPrescriptionWmCreateEventDataAssembler;

    @Test
    public void assemblerTest() {
        BusPrescription busPrescription = new BusPrescription();
        busPrescription.setId(1431L);
        ynPrescriptionWmCreateEventDataAssembler.assembleExtra(new YnPrescriptionWmCreate(), busPrescription, null);
    }


}