package com.puree.hospital.monitor.guangdong.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.puree.hospital.common.core.utils.AgeCalculationUtil;
import com.puree.hospital.monitor.config.HospitalJgIdConfig;
import com.puree.hospital.monitor.guangdong.domain.MedicalRecord;
import com.puree.hospital.monitor.guangdong.mapper.MedicalRecordMapper;
import com.puree.hospital.monitor.guangdong.service.IMedicalRecordService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;

@Service
@Slf4j
public class MedicalRecordServiceImpl implements IMedicalRecordService {
    private final MedicalRecordMapper medicalRecordMapper;
    private final HospitalJgIdConfig hospitalJgIdConfig;

    @Autowired
    public MedicalRecordServiceImpl(MedicalRecordMapper medicalRecordMapper,HospitalJgIdConfig hospitalJgIdConfig) {
        this.medicalRecordMapper = medicalRecordMapper;
        this.hospitalJgIdConfig = hospitalJgIdConfig;
    }

    @Override
    public List<MedicalRecord> selectList(Long hospitalId,String isNewHos) {
        LambdaQueryWrapper<MedicalRecord> queryWrapper;
        if ("0".equals(isNewHos)) {
            queryWrapper = new LambdaQueryWrapper<MedicalRecord>()
                    .eq(MedicalRecord::getHospitalId, hospitalId)
                    .ge(MedicalRecord::getCreateTime, LocalDate.now());
        }else{
            queryWrapper = new LambdaQueryWrapper<MedicalRecord>()
                    .eq(MedicalRecord::getHospitalId, hospitalId);
        }
        return medicalRecordMapper.selectList(queryWrapper);
    }

    @Override
    public int syncData(Long hospitalId) {
        List<MedicalRecord> syncDataList = medicalRecordMapper.selectSyncDataList(hospitalId);
        syncDataList.forEach(i -> {
            i.setJgdm(hospitalJgIdConfig.getJgId(hospitalId));
            i.setFwwddm(hospitalJgIdConfig.getJgId(hospitalId));
            i.setJzlx("100");
            i.setKh(String.valueOf(i.getFamilyId()));
            i.setKlx("3");//系统内部号
            String xbdm = i.getXbdm().equals("0") ? "2" : "1";
            i.setXbdm(xbdm);
            int nls = AgeCalculationUtil.getAge(i.getCsrq());
            i.setNls(nls);
            if (nls == 0) {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                String csrq = sdf.format(i.getCsrq());
                String[] arrays = csrq.split("-");
                csrq = arrays[1] + " " + arrays[2] + "/30";
                i.setNly(csrq);
            }
            i.setGmsbs("F");
            i.setCzbzdm("1");
            i.setMzzzzdbmlx("01");
            i.setCzylwsjgdm(hospitalJgIdConfig.getJgId(hospitalId));
            i.setCzylwsjgmc(hospitalJgIdConfig.getJgId(hospitalId));
            i.setDzcfwjcfdz("T");
            i.setYsdspwjcfdz("F");
            i.setHzdspwjcfdz("F");
            i.setCxbz("1");
            i.setSjscsj(new Date());
            try {
                String diagnosis = i.getDiagnosis();
                JSONArray json = JSONArray.parseArray(diagnosis);
                JSONObject jsonObject = json.getJSONObject(0);
                String tcmDiagnosis = jsonObject.getString("tcmDiagnosis");
                i.setJzzdsm(tcmDiagnosis);
            }catch (Exception e){
                log.warn("同步诊疗记录诊断数据失败,原因：{}",e.getMessage());
            }
        });

        for (MedicalRecord p : syncDataList) {
            QueryWrapper<MedicalRecord> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("KH", p.getKh());
            queryWrapper.eq("hospital_id",hospitalId);
            List<MedicalRecord> plist = medicalRecordMapper.selectList(queryWrapper);
            if (CollectionUtils.isEmpty(plist)) {
                p.setHospitalId(hospitalId);
                medicalRecordMapper.insert(p);
            }
        }
        return 1;
    }
}
