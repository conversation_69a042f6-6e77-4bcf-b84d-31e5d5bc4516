package com.puree.hospital.monitor.guangdong.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("gd_t_ins_contacts")
public class Contacts {
    /** 部门 */
    private String bm;
    /** 撤销标志 */
    private String cxbz;
    /** 负责人姓名 */
    private String fzrxm;
    /** 机构标识 */
    private String jgdm;
    /** 联系电话 */
    private String lxdh;
    /** 数据生成日期时间 */
    private Date sjscsj;
    /** 创建时间（辅助作用） */
    private Date createTime;
    private Long hospitalId;
}
