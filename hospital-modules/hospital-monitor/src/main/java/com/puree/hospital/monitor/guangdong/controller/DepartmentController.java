package com.puree.hospital.monitor.guangdong.controller;

import com.puree.hospital.common.core.web.controller.BaseController;
import com.puree.hospital.monitor.guangdong.domain.Department;
import com.puree.hospital.monitor.guangdong.service.IDepartmentService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("guangdong/department")
public class DepartmentController extends BaseController {
    @Resource
    private IDepartmentService departmentService;

    /**
     * 科室字典表
     */
    @GetMapping("list")
    public List<Department> list(@RequestParam("hospitalId") Long hospitalId, @RequestParam(defaultValue = "0") String isNewHos) {
        logger.info("科室字典表：入参{}", hospitalId);
        // 先同步拉取数据
        departmentService.selectSyncDataList(hospitalId);
        List<Department> list = departmentService.selectList(hospitalId, isNewHos);
        logger.info("科室字典表：出参{}", list);
        return list;
    }
}
