package com.puree.hospital.monitor.common.repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.puree.hospital.monitor.guangdong.domain.MedicalPrescription;
import com.puree.hospital.monitor.guangdong.mapper.MedicalPrescriptionMapper;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class MedicalPrescriptionRepository extends ServiceImpl<MedicalPrescriptionMapper, MedicalPrescription> {
    public List<MedicalPrescription> findByRecordNo(String recordNo) {
        return baseMapper.selectList(new QueryWrapper<MedicalPrescription>().eq("MZH", recordNo));
    }
}
