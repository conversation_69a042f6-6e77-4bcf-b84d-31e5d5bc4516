package com.puree.hospital.monitor.regulatory.event.listener;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.common.rabbitmq.annotation.RabbitListenerEnhance;
import com.puree.hospital.common.rabbitmq.model.MessageModel;
import com.puree.hospital.monitor.api.event.constant.EventMessageQueueConstant;
import com.puree.hospital.monitor.api.event.regulatory.collect.RegulatoryCollectEvent;
import com.puree.hospital.monitor.api.event.regulatory.report.BaseRegulatory;
import com.puree.hospital.monitor.config.HospitalJgIdConfig;
import com.puree.hospital.monitor.regulatory.event.assembler.DefaultEventDataAssembler;
import com.puree.hospital.monitor.regulatory.event.assembler.EventDataAssembler;
import com.puree.hospital.monitor.regulatory.event.model.RegulatoryHospitalConfig;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <p>
 * 监管-采集事件监听器
 * </p>
 *
 * <AUTHOR>
 * @date 2024/6/25 09:12
 */
@Slf4j
@Component
public class RegulatoryCollectEventListener<T extends BaseRegulatory> {

    @Resource
    private Map<String, EventDataAssembler<T>> eventDataAssemblerMap;

    @Resource
    private HospitalJgIdConfig hospitalJgIdConfig;

    /**
     * 监听监管数据采集事件
     *
     * @param messageModel  消息数据
     * @param channel       rabbitmq-channel
     * @param message       rabbitmq原始消息
     * @throws IOException  IO异常
     */
    @RabbitListener(queues = EventMessageQueueConstant.REGULATORY_MONITOR_COLLECTOR_QUEUE)
    @RabbitListenerEnhance(maxConsumeFailCount = 50)
    public void onMessage(MessageModel<RegulatoryCollectEvent> messageModel, Channel channel, Message message) throws IOException {
        log.info("监听到数据采集事件：{}", JSON.toJSONString(messageModel));
        RegulatoryCollectEvent event = messageModel.getMsg();
        boolean basicAck = true;
        try {
            // 1.根据hospitalId从配置中心获取 区域 以及医院社会统一代码和地址信息
            RegulatoryHospitalConfig hospitalConfig = hospitalJgIdConfig.getHospitalConfig(event.getHospitalId());
            if (Objects.isNull(hospitalConfig)) {
                log.warn("根据hospitalId:{}未获取到医院配置信息,不做监管上报", event.getHospitalId());
                return;
            }
            // 2.通过区域获取handler, 可能收集的一个事件会有多种数据上报，因此需要做适配
            List<EventDataAssembler<T>> assemblers = getEventDataAssembler(event, hospitalConfig.getProvinceAbbreviation());
            if (CollectionUtil.isEmpty(assemblers)) {
                log.warn("根据hospitalId:{}未获取到对应的监管事件处理器,不做监管上报", event.getHospitalId());
                return;
            }
            for (EventDataAssembler<T> assembler : assemblers) {
                //3.监管数组组装
                T regulatory;
                try {
                    regulatory = assembler.assemble(event, hospitalConfig);
                } catch (IllegalArgumentException e) {
                    log.info("监管数据组装参数错误:{}", e.getMessage(), e);
                    continue;
                } catch (IllegalStateException e) {
                    log.warn("监管数据组装数据状态错误:{}", e.getMessage(), e);
                    basicAck = false;
                    return;
                } catch (Exception e) {
                    log.error("监管数据组装异常:{}", e.getMessage(), e);
                    basicAck = false;
                    return;
                }
                // 4.推送MQTT消息
                if (Objects.nonNull(regulatory)) {
                    basicAck = basicAck && assembler.pushMessage(regulatory, channel, message);
                }
            }
        } finally {
            if (basicAck) {
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            } else {
                channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
            }
        }
    }

    /**
     * 获取监管事件处理器
     *
     * @param event                事件
     * @param provinceAbbreviation 省份简称
     * @return 监管事件处理器
     */
    @SuppressWarnings("unchecked")
    private List<EventDataAssembler<T>> getEventDataAssembler(RegulatoryCollectEvent event, String provinceAbbreviation) {
        List<EventDataAssembler<T>> assemblers = Lists.newArrayList();
        if (Objects.nonNull(event) && Objects.nonNull(provinceAbbreviation) && StringUtils.isNotBlank(provinceAbbreviation)) {
            List<String> eventCodes = event.getEventType().getCodes();
            if (CollectionUtil.isNotEmpty(eventCodes)) {
                eventCodes.forEach(code -> {
                    String key = provinceAbbreviation + code + EventDataAssembler.SUFFIX;
                    EventDataAssembler<T> assembler = eventDataAssemblerMap.get(key);
                    if (Objects.nonNull(assembler)) {
                        assemblers.add(assembler);
                    }
                });
            }
        }
        //如果未找到对应的实现类
        if (CollectionUtil.isEmpty(assemblers)) {
            assemblers.add((EventDataAssembler<T>) new DefaultEventDataAssembler());
        }
        //获取默认的数据
        return assemblers;
    }

}
