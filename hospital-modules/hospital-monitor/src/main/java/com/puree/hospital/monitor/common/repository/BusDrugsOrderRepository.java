package com.puree.hospital.monitor.common.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.puree.hospital.monitor.common.domain.BusDrugsOrder;
import com.puree.hospital.monitor.common.mapper.BusDrugsOrderMapper;
import org.springframework.stereotype.Repository;

import java.sql.Wrapper;

/**
 * <p>
 * 药品订单 Repository
 * </p>
 *
 * <AUTHOR>
 * @date 2024/7/3 14:18
 */
@Repository
public class BusDrugsOrderRepository extends ServiceImpl<BusDrugsOrderMapper, BusDrugsOrder> {

    /**
     * 根据订单号查询订单信息
     *
     * @param orderNo 订单号
     * @return 药品订单信息
     */
    public BusDrugsOrder getByOrderNo(String orderNo) {
        LambdaQueryWrapper<BusDrugsOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BusDrugsOrder::getOrderNo, orderNo);
        return this.getOne(queryWrapper, false);
    }
}
