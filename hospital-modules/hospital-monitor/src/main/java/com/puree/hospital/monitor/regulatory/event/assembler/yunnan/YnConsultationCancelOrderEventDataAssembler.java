package com.puree.hospital.monitor.regulatory.event.assembler.yunnan;

import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.monitor.api.event.constant.RegulatoryAreaConstant;
import com.puree.hospital.monitor.api.event.constant.RegulatoryEventConstant;
import com.puree.hospital.monitor.api.event.regulatory.report.yunnan.YnConsultationCancelOrder;
import com.puree.hospital.monitor.common.domain.BusConsultationOrder;
import com.puree.hospital.monitor.regulatory.event.assembler.AbstractEventDataAssembler;
import com.puree.hospital.monitor.regulatory.event.assembler.EventDataAssembler;
import com.puree.hospital.monitor.regulatory.event.model.RegulatoryHospitalConfig;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <p>
 * 云南问诊订单取消（退号）
 * </p>
 *
 * <AUTHOR>
 * @date 2024/6/25 14:36
 */
@Component(RegulatoryAreaConstant.YUN_NAN + RegulatoryEventConstant.CONSULTATION_CANCEL_ORDER + EventDataAssembler.SUFFIX)
public class YnConsultationCancelOrderEventDataAssembler extends AbstractEventDataAssembler<YnConsultationCancelOrder> {

    @Override
    protected YnConsultationCancelOrder newInstance() {
        return new YnConsultationCancelOrder();
    }

    @Override
    protected void assembleOtherInfo(YnConsultationCancelOrder regulatory, RegulatoryHospitalConfig config) {
        BusConsultationOrder busConsultationOrder = super.getConsultationOrder(regulatory.getBusinessId());
        if (Objects.isNull(busConsultationOrder)) {
            throw new IllegalStateException(String.format("未找到问诊订单信息，问诊订单id：%s", regulatory.getBusinessId()));
        }
        if (Objects.isNull(busConsultationOrder.getDoctorId())) {
            throw new IllegalArgumentException(String.format("当前问诊订单没有关联接诊医生，问诊订单id:%s", regulatory.getBusinessId()));
        }
        regulatory.setVisitNo(busConsultationOrder.getOrderNo());
        regulatory.setWithdrawalTime(Objects.nonNull(busConsultationOrder.getWithdrawalTime()) ? busConsultationOrder.getWithdrawalTime(): regulatory.getEventTime());
        String reason = regulatory.getReason();
        if (StringUtils.isBlank(reason)) {
            reason = "系统自动退号";
        }
        regulatory.setReason(reason);
    }

    @Override
    protected String getEventType() {
        return RegulatoryEventConstant.CONSULTATION_CANCEL_ORDER;
    }
}
