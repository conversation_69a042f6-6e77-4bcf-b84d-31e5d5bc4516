package com.puree.hospital.monitor.guangdong.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("gd_t_ins_department")
public class Department {
    /** 标准科室代码 */
    private String bzksdm;
    /** 撤销标志 */
    private String cxbz;
    /** 机构标识 */
    private String jgdm;
    /** 科室编码 */
    private String ksbm;
    /** 科室名称 */
    private String ksmc;
    /** 数据生成日期时间 */
    private Date sjscsj;
    /** 创建时间（辅助作用） */
    private Date createTime;
    private Long hospitalId;
}
