package com.puree.hospital.monitor.guangdong.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("gd_t_ms_treatment_record")
public class MedicalRecord {
    /** 出生日期 */
    private Date csrq;
    /** 撤销标志 */
    private String cxbz;
    /** 初诊标志代码 */
    private String czbzdm;
    /** 初诊医疗卫生机构代码 */
    private String czylwsjgdm;
    /** 初诊医疗卫生机构名称 */
    private String czylwsjgmc;
    /** 服务网点代码 */
    private String fwwddm;
    /** 过敏史标识 */
    private String gmsbs;
    /** 机构标识 */
    private String jgdm;
    /** 就诊类型 */
    private String jzlx;
    /** 就诊日期时间 */
    private Date jzrqsj;
    /** 卡号 */
    private String kh;
    /** 卡类型 */
    private String klx;
    /** 科室编码 */
    private String ksbm;
    /** 科室名称 */
    private String ksmc;
    /** 门诊号 */
    private String mzh;
    /** 门诊症状诊断编码类型 */
    private String mzzzzdbmlx;
    /** 年龄 */
    private Integer nls;
    /** 年龄月 */
    private String nly;
    /** 数据生成日期时 */
    private Date sjscsj;
    /** 性别 */
    private String xbdm;
    /** 姓名 */
    private String xm;
    /** 应诊医师签名 */
    private String yzysgh;
    /** 应诊医师签名 */
    private String yzysqm;
    /** 电子处方文件存放地址 */
    private String dzcfwjcfdz;
    /** 患者端视频文件存放地址 */
    private String hzdspwjcfdz;
    /** 医生端视频文件存放地址 */
    private String ysdspwjcfdz;
    private Long hospitalId;
    /** 就诊诊断说明 */
    private String jzzdsm;
    /** 就诊诊断json */
    @TableField(exist = false)
    private String diagnosis;
    @TableField(exist = false)
    private String allergicHistory;
    @TableField(exist = false)
    private Long familyId;
    /** 创建时间（辅助作用） */
    private Date createTime;
}
