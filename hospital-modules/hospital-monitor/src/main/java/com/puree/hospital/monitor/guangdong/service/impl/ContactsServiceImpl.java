package com.puree.hospital.monitor.guangdong.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.puree.hospital.monitor.guangdong.domain.Contacts;
import com.puree.hospital.monitor.guangdong.mapper.ContactsMapper;
import com.puree.hospital.monitor.guangdong.service.IContactsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;

@Service
public class ContactsServiceImpl implements IContactsService {
    private final ContactsMapper contactsMapper;

    @Autowired
    public ContactsServiceImpl(ContactsMapper contactsMapper) {
        this.contactsMapper = contactsMapper;
    }

    @Override
    public List<Contacts> selectList(Long hospitalId,String isNewHos) {
        LambdaQueryWrapper<Contacts> queryWrapper;
        if ("0".equals(isNewHos)) {
            queryWrapper = new LambdaQueryWrapper<Contacts>()
                    .eq(Contacts::getHospitalId, hospitalId)
                    .ge(Contacts::getCreateTime, LocalDate.now());
        } else {
            queryWrapper = new LambdaQueryWrapper<Contacts>()
                    .eq(Contacts::getHospitalId, hospitalId);
        }
        return contactsMapper.selectList(queryWrapper);
    }
}
