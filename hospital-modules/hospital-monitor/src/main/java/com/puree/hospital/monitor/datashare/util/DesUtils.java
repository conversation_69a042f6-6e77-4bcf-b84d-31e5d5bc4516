package com.puree.hospital.monitor.datashare.util;

import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.monitor.datashare.constant.HospitalSignConstant;
import org.apache.commons.codec.binary.Base64;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @date 2023/1/31 10:29
 */
public class DesUtils {
    /**
     * 加密
     * @param sSrc 明文
     * @param sKey 密钥
     * @return
     * @throws Exception
     */
    public static String Encrypt(String sSrc, String sKey) throws Exception {
        if (sKey == null) {
            throw new ServiceException("Key为空null");
        }
        // 判断Key是否为8位
        if (sKey.length() != 8) {
            throw new ServiceException("Key长度不是8位");
        }
        byte[] raw = sKey.getBytes(StandardCharsets.UTF_8);
        SecretKeySpec skeySpec = new SecretKeySpec(raw, "DES");
        //"算法/模式/补码方式"，使用cbc模式加密
        Cipher cipher = Cipher.getInstance("DES/CBC/PKCS5Padding");
        //使用CBC模式，需要一个向量iv，可增加加密算法的强度
        IvParameterSpec iv = new IvParameterSpec(HospitalSignConstant.SECRET_KEY.getBytes());
        cipher.init(Cipher.ENCRYPT_MODE, skeySpec, iv);
        byte[] encrypted = cipher.doFinal(sSrc.getBytes());
        //此处使用BASE64做转码功能，同时能起到2次加密的作用。
        return Base64.encodeBase64String(encrypted);
    }

    /**
     * 解密
     * @param sSrc 密文
     * @param sKey 密钥
     * @return
     * @throws Exception
     */
    public static String Decrypt(String sSrc, String sKey) {
        try {
            // 判断Key是否正确
            if (sKey == null) {
                throw new ServiceException("Key为空null");
            }
            // 判断Key是否为8位
            if (sKey.length() != 8) {
                throw new ServiceException("Key长度不是8位");
            }
            byte[] raw = sKey.getBytes(StandardCharsets.UTF_8);
            SecretKeySpec skeySpec = new SecretKeySpec(raw, "DES");
            Cipher cipher = Cipher.getInstance("DES/CBC/PKCS5Padding");
            IvParameterSpec iv = new IvParameterSpec(HospitalSignConstant.SECRET_KEY.getBytes());
            cipher.init(Cipher.DECRYPT_MODE, skeySpec, iv);
            //先用base64解密
            byte[] encrypted1 = Base64.decodeBase64(sSrc);
            try {
                byte[] original = cipher.doFinal(encrypted1);
                String originalString = new String(original);
                return originalString;
            } catch (Exception e) {
                System.out.println(e.toString());
                return null;
            }
        } catch (Exception ex) {
            System.out.println(ex.toString());
            return null;
        }
    }

}
