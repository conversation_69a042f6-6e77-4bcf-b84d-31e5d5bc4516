package com.puree.hospital.monitor.datashare.controller;

import com.alibaba.fastjson.JSONObject;
import com.puree.hospital.common.core.utils.bean.OrikaUtils;
import com.puree.hospital.common.core.web.controller.BaseController;
import com.puree.hospital.common.api.domain.AjaxResult;
import com.puree.hospital.common.core.web.domain.MapResult;
import com.puree.hospital.monitor.datashare.constant.HospitalSignConstant;
import com.puree.hospital.monitor.datashare.constant.UrlConstant;
import com.puree.hospital.monitor.datashare.domain.vo.BusHospitalVO;
import com.puree.hospital.monitor.datashare.domain.vo.HospitalResult;
import com.puree.hospital.monitor.datashare.service.IBusHospitalService;
import com.puree.hospital.monitor.datashare.util.DesUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static com.puree.hospital.monitor.datashare.util.SignCheck.signCheck;
import static com.puree.hospital.monitor.datashare.util.UrlCheck.urlCheck;

/**
 * <AUTHOR>
 * @date 2023/1/16 15:52
 */
@RestController
@RequestMapping("/hospital")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BusHospitalController extends BaseController {
    private final IBusHospitalService busHospitalService;
    @Value("${aliyun.oss.fileAddressPrefix}")
    private String fileAddressPrefix;

    /**
     * 查询医院信息
     * @param key 时间戳
     * @param token key（导引平台提供）+“_”+salt（医院端定义服务器端密码）值
     * @return
     */
    @GetMapping("/acquireHospital")
    public MapResult queryHospital(String key, String token, String hospitalCode) {
        logger.info("导引平台查询医院入参1={}，入参2={}", key, token);
        MapResult ajaxResult = new MapResult();
        try {
            signCheck(key, token);
            BusHospitalVO hospitalVo = busHospitalService.queryHospital(hospitalCode);
            HospitalResult result = OrikaUtils.convert(hospitalVo, HospitalResult.class);
            result.setHosLevel("三级甲等");
            result.setHosLogo(fileAddressPrefix + hospitalVo.getHospitalPhone());
            result.setWxMpAppId("");
            result.setWxMpPath("");
            String hosUrl = urlCheck(hospitalCode);
            result.setHosUrl(hosUrl + UrlConstant.HOSPITAL_URL + "&hospitalId=" + hospitalVo.getId());
            String jsonString = JSONObject.toJSONString(result);
            logger.info("医院信息={}", jsonString);
            // 使用DES加密
            String data = DesUtils.Encrypt(jsonString, HospitalSignConstant.SECRET_KEY);
            ajaxResult.put("status", 1);
            ajaxResult.put("message", "医院信息查询成功");
            ajaxResult.put("data", data);
        } catch (Exception e) {
            ajaxResult.put("status", 0);
            ajaxResult.put("message", "医院信息查询失败");
            ajaxResult.put("data", e.getMessage());
        }
        return ajaxResult;
    }

}
