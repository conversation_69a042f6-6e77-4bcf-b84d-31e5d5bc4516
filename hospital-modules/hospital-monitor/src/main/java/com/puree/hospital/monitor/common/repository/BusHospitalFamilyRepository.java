package com.puree.hospital.monitor.common.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.puree.hospital.monitor.common.domain.BusHospitalFamily;
import com.puree.hospital.monitor.common.mapper.BusHospitalFamilyMapper;
import org.springframework.stereotype.Repository;

/**
 * <p>
 * 医院就诊人信息 Repository
 * </p>
 *
 * <AUTHOR>
 * @date 2024/8/15 19:36
 */
@Repository
public class BusHospitalFamilyRepository extends ServiceImpl<BusHospitalFamilyMapper, BusHospitalFamily> {

    /**
     * 查询医院就诊人信息
     *
     * @param hospitalId 医院id
     * @param patientId  患者id
     * @param familyId   就诊人id
     * @return 医院就诊人信息
     */
    public BusHospitalFamily getHospitalFamily(Long hospitalId, Long patientId, Long familyId) {
        LambdaQueryWrapper<BusHospitalFamily> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BusHospitalFamily::getHospitalId, hospitalId)
                .eq(BusHospitalFamily::getPatientId, patientId)
                .eq(BusHospitalFamily::getFamilyId, familyId)
                .orderByDesc(BusHospitalFamily::getId);
        return this.getOne(wrapper, false);
    }

}
