package com.puree.hospital.monitor.guangdong.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName("gd_t_ins_institution_business")
public class InstitutionBusiness {
    /** 撤销标志 */
    private String cxbz;
    /** 对外投资 */
    private BigDecimal dwtz;
    /** 负债 */
    private BigDecimal fz;
    /** 固定资产 */
    private BigDecimal gdzc;
    /** 机构标识 */
    private String jgdm;
    /** 净资产 */
    private BigDecimal jzc;
    /** 客户服务人员总数 */
    private Integer khffryzs;
    /** 流动资产 */
    private BigDecimal ldzc;
    /** 月份 */
    private String nf;
    /** 日均坐诊医生数 */
    private Integer rjzzyspbs;
    /** 数据生成日期时间 */
    private Date sjscsj;
    /** 无形资产及开办费 */
    private BigDecimal wxzcjkbf;
    /** 职工总数 */
    private Integer zgzs;
    /** 总收入 */
    private BigDecimal zsr;
    /** 总支出 */
    private BigDecimal zzc;
    /** 总资产 */
    private BigDecimal zzch;
    /** 创建时间（辅助作用） */
    private Date createTime;
    private Long hospitalId;
}
