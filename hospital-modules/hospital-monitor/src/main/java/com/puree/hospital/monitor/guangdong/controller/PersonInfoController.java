package com.puree.hospital.monitor.guangdong.controller;

import com.puree.hospital.common.core.web.controller.BaseController;
import com.puree.hospital.common.api.domain.AjaxResult;
import com.puree.hospital.monitor.guangdong.domain.PersonInfo;
import com.puree.hospital.monitor.guangdong.service.IPersonInfoService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("guangdong/personinfo")
public class PersonInfoController extends BaseController {
    @Resource
    private IPersonInfoService personInfoService;

    /**
     * 患者信息表
     */
    @GetMapping("list")
    public List<PersonInfo> list(@RequestParam("hospitalId") Long hospitalId, @RequestParam(defaultValue = "0") String isNewHos) {
        logger.info("患者信息表：入参{}", hospitalId);
        personInfoService.syncData(hospitalId);
        List<PersonInfo> list = personInfoService.selectList(hospitalId, isNewHos);
        logger.info("患者信息表：出参{}", list);
        return list;
    }

    /**
     * 同步数据
     */
    @PostMapping("/sync/{id}")
    public AjaxResult sync(@PathVariable Long id) {
        return toAjax(personInfoService.syncData(id));
    }
}
