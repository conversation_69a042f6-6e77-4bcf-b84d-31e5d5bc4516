package com.puree.hospital.monitor.datashare.service;


import com.puree.hospital.system.api.model.SysDictData;

import java.util.List;
import java.util.Map;
import java.util.function.Function;

public interface SysDictDataService {

    /**
     * 根据1个dict_type 获取所有相关的字典信息
     */
    List<SysDictData> getDictByType(String type);

    /**
     * 根据多个dict_type，返回所有相关字典信息
     */
    Map<String,List<SysDictData>> getDictByTypes(List<String> types);

    /**
     * 根据dict_code，查询其对应某个值
     */
    SysDictData getDictByCode(Long dictCode);

    /**
     * 根据dict_type,返回字典中某两个值的映射表
     */
    <T, U> Map<T,U> getDictMapByTypes(List<String> types, Function<SysDictData, T> key, Function<SysDictData, U> value);
}
