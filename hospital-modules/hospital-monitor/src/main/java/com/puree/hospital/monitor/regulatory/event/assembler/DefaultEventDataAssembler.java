package com.puree.hospital.monitor.regulatory.event.assembler;

import com.puree.hospital.monitor.api.event.regulatory.report.BaseRegulatory;
import com.puree.hospital.monitor.regulatory.event.model.RegulatoryHospitalConfig;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;

/**
 * <p>
 * 默认的实现
 * </p>
 *
 * <AUTHOR>
 * @date 2024/6/25 09:49
 */
@Slf4j
public class DefaultEventDataAssembler extends AbstractEventDataAssembler<BaseRegulatory> {

    @Override
    public boolean pushMessage(BaseRegulatory baseRegulatory, Channel channel, Message message) {
        log.warn("未找到对应事件MQTT推送实现，不做事件的数据上报");
        return true;
    }

    @Override
    protected BaseRegulatory newInstance() {
        return new BaseRegulatory();
    }

    @Override
    protected void assembleOtherInfo(BaseRegulatory regulatory, RegulatoryHospitalConfig config) {
        log.warn("未找到对应事件的数据组装实现，不做数据组装");
    }

    @Override
    protected String getEventType() {
        return "NONE";
    }
}
