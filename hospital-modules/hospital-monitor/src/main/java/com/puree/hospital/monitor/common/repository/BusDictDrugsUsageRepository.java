package com.puree.hospital.monitor.common.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.puree.hospital.monitor.common.domain.BusDictDrugsUsage;
import com.puree.hospital.monitor.common.mapper.BusDictDrugsUsageMapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>
 * 药品用法信息 Repository
 * </p>
 *
 * <AUTHOR>
 * @date 2024/6/28 15:38
 */
@Repository
public class BusDictDrugsUsageRepository extends ServiceImpl<BusDictDrugsUsageMapper, BusDictDrugsUsage> {

    /**
     * 根据名称列表查询
     *
     * @param nameList 名称列表
     * @return 药品用法信息列表
     */
    public List<BusDictDrugsUsage> getByNameList(List<String> nameList) {
        LambdaQueryWrapper<BusDictDrugsUsage> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(BusDictDrugsUsage::getName, nameList);
        return list(wrapper);
    }

}
