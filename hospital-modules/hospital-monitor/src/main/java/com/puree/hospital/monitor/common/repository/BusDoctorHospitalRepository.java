package com.puree.hospital.monitor.common.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.puree.hospital.monitor.common.domain.BusDoctorHospital;
import com.puree.hospital.monitor.common.mapper.BusDoctorHospitalMapper;
import org.springframework.stereotype.Repository;

import java.util.Objects;

/**
 * <p>
 * 医生医院关联表 Repository
 * </p>
 *
 * <AUTHOR>
 * @date 2024/6/27 11:01
 */
@Repository
public class BusDoctorHospitalRepository extends ServiceImpl<BusDoctorHospitalMapper, BusDoctorHospital> {

    /**
     * 根据医生id和医院id查询
     *
     * @param doctorId   医生id
     * @param hospitalId 医院id
     * @return 医院医生关联信息
     */
    public BusDoctorHospital getByDoctorIdAndHospitalId(Long doctorId, Long hospitalId) {
        if (Objects.isNull(doctorId) || Objects.isNull(hospitalId)) {
            return null;
        }
        LambdaQueryWrapper<BusDoctorHospital> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BusDoctorHospital::getDoctorId, doctorId)
                .eq(BusDoctorHospital::getHospitalId, hospitalId);
        return this.getOne(queryWrapper, false);
    }

}
