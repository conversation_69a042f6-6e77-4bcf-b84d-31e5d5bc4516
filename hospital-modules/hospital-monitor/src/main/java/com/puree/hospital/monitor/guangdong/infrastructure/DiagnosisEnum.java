package com.puree.hospital.monitor.guangdong.infrastructure;

public enum DiagnosisEnum {
    XYZDZY("11", "西医诊断（主要）"),
    XYZDQT("12", "西医诊断（其他）"),
    ZYZDZB("21", "中医诊断（主病）"),
    ZYZDZZ("22", "中医诊断（主证）"),
    ZYZDQTB("23", "中医诊断（其他病）"),
    ZYZDQTZ("24", "中医诊断（其他证）"),
    YNGR("31", "院内感染"),
    BFZ("41", "并发症");

    private final String code;
    private final String info;

    DiagnosisEnum(String code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public String getCode()
    {
        return code;
    }
    public String getInfo()
    {
        return info;
    }
}
