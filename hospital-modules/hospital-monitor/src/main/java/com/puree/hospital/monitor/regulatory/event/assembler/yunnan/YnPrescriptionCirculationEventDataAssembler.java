package com.puree.hospital.monitor.regulatory.event.assembler.yunnan;

import com.puree.hospital.common.core.enums.DirectoryTypeEnum;
import com.puree.hospital.monitor.api.event.constant.RegulatoryAreaConstant;
import com.puree.hospital.monitor.api.event.constant.RegulatoryEventConstant;
import com.puree.hospital.monitor.api.event.regulatory.report.yunnan.YunnanPrescriptionCirculation;
import com.puree.hospital.monitor.common.domain.BusConsultationOrder;
import com.puree.hospital.monitor.common.domain.BusPrescription;
import com.puree.hospital.monitor.common.domain.BusPrescriptionDualChannel;
import com.puree.hospital.monitor.common.repository.BusPrescriptionDualChannelRepository;
import com.puree.hospital.monitor.common.repository.BusPrescriptionRepository;
import com.puree.hospital.monitor.regulatory.event.assembler.AbstractEventDataAssembler;
import com.puree.hospital.monitor.regulatory.event.assembler.EventDataAssembler;
import com.puree.hospital.monitor.regulatory.event.model.RegulatoryHospitalConfig;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <p>
 * 云南处方流转事件数据组装
 * </p>
 *
 * <AUTHOR>
 * @date 2024/12/08 15:50
 */
@Component(RegulatoryAreaConstant.YUN_NAN + RegulatoryEventConstant.PRESCRIPTION_CIRCULATION + EventDataAssembler.SUFFIX)
public class YnPrescriptionCirculationEventDataAssembler extends AbstractEventDataAssembler<YunnanPrescriptionCirculation> {

    @Resource
    private BusPrescriptionRepository busPrescriptionRepository;

    @Resource
    private BusPrescriptionDualChannelRepository busPrescriptionDualChannelRepository;

    @Override
    protected YunnanPrescriptionCirculation newInstance() {
        return new YunnanPrescriptionCirculation();
    }

    @Override
    protected void assembleOtherInfo(YunnanPrescriptionCirculation regulatory, RegulatoryHospitalConfig config) {
        BusPrescription busPrescription = busPrescriptionRepository.getById(regulatory.getBusinessId());
        if (Objects.isNull(busPrescription)) {
            throw new IllegalStateException(String.format("处方信息不存在,处方id：%s", regulatory.getBusinessId()));
        }
        BusConsultationOrder consultationRecord = super.getConsultationRecord(busPrescription);
        if (Objects.isNull(consultationRecord)) {
            throw new IllegalArgumentException(String.format("处方关联的问诊记录信息不存在,处方id：%s", regulatory.getBusinessId()));
        }
        //如果处方走的药品目录是院内目录，则不做处方流转上报
        if (!DirectoryTypeEnum.isDualChannel(busPrescription.getDrugDirectoryType())) {
            throw new IllegalArgumentException(String.format("当前处方的药品目录是院内处方,不做处方流转上报, 处方id：%s,", regulatory.getBusinessId()));
        }
        BusPrescriptionDualChannel dualChannel = busPrescriptionDualChannelRepository.getById(busPrescription.getId());
        if (Objects.isNull(dualChannel)) {
            throw new IllegalArgumentException(String.format("当前处方关联的双通到信息缺失, 处方id：%s,", regulatory.getBusinessId()));
        }
        regulatory.setHosRxCircCode(dualChannel.getHiRxno());
        regulatory.setHosRxCode(busPrescription.getPrescriptionNumber());
        //todo 不知道如何取值
        regulatory.setSupplyOrgName("");
    }

    @Override
    protected String getEventType() {
        return RegulatoryEventConstant.PRESCRIPTION_CIRCULATION;
    }
}
