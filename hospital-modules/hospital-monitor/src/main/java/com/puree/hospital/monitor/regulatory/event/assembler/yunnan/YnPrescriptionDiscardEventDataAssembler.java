package com.puree.hospital.monitor.regulatory.event.assembler.yunnan;

import com.puree.hospital.monitor.api.event.constant.RegulatoryAreaConstant;
import com.puree.hospital.monitor.api.event.constant.RegulatoryEventConstant;
import com.puree.hospital.monitor.api.event.regulatory.report.yunnan.YnPrescriptionDiscard;
import com.puree.hospital.monitor.common.domain.BusConsultationOrder;
import com.puree.hospital.monitor.common.domain.BusPrescription;
import com.puree.hospital.monitor.common.repository.BusPrescriptionRepository;
import com.puree.hospital.monitor.regulatory.event.assembler.AbstractEventDataAssembler;
import com.puree.hospital.monitor.regulatory.event.assembler.EventDataAssembler;
import com.puree.hospital.monitor.regulatory.event.model.RegulatoryHospitalConfig;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <p>
 * 云南处方撤销
 * </p>
 *
 * <AUTHOR>
 * @date 2024/6/25 14:49
 */
@Component(RegulatoryAreaConstant.YUN_NAN + RegulatoryEventConstant.PRESCRIPTION_DISCARD + EventDataAssembler.SUFFIX)
public class YnPrescriptionDiscardEventDataAssembler extends AbstractEventDataAssembler<YnPrescriptionDiscard> {

    @Resource
    private BusPrescriptionRepository busPrescriptionRepository;

    @Override
    protected YnPrescriptionDiscard newInstance() {
        return new YnPrescriptionDiscard();
    }

    @Override
    protected void assembleOtherInfo(YnPrescriptionDiscard regulatory, RegulatoryHospitalConfig config) {
        BusPrescription busPrescription = busPrescriptionRepository.getById(regulatory.getBusinessId());
        if (Objects.isNull(busPrescription)) {
            throw new IllegalStateException(String.format("处方信息不存在,处方id：%s", regulatory.getBusinessId()));
        }
        BusConsultationOrder consultationRecord = super.getConsultationRecord(busPrescription);
        if (Objects.isNull(consultationRecord)) {
            throw new IllegalArgumentException(String.format("处方关联的问诊记录信息不存在,处方id：%s", regulatory.getBusinessId()));
        }
        regulatory.setPrescriptionNumber(busPrescription.getPrescriptionNumber());
        regulatory.setDiscardTime(regulatory.getEventTime());
    }

    @Override
    protected String getEventType() {
        return RegulatoryEventConstant.PRESCRIPTION_DISCARD;
    }
}
