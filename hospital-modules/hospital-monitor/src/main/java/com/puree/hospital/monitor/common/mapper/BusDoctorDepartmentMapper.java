package com.puree.hospital.monitor.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.puree.hospital.monitor.common.domain.BusDoctorDepartment;

import java.util.List;

/**
 * <p>
 * 医生科室关联信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @date 2024/6/26 08:39
 */
public interface BusDoctorDepartmentMapper extends BaseMapper<BusDoctorDepartment> {

    /**
     * 查询医生科室关联信息
     *
     * @param busDoctorDepartment 查询参数
     * @return 医生科室关联信息集合
     */
    List<BusDoctorDepartment> selectList(BusDoctorDepartment busDoctorDepartment);

}
