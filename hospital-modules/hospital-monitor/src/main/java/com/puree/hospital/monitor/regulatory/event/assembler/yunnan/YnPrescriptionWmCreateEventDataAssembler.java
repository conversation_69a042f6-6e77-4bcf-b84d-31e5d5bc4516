package com.puree.hospital.monitor.regulatory.event.assembler.yunnan;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import com.puree.hospital.app.api.model.BusDictDrugsFrequencyDTO;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.monitor.api.event.constant.RegulatoryAreaConstant;
import com.puree.hospital.monitor.api.event.constant.RegulatoryEventConstant;
import com.puree.hospital.monitor.api.event.regulatory.report.yunnan.YnPrescriptionWmCreate;
import com.puree.hospital.monitor.api.event.regulatory.report.yunnan.YnWmPrescriptionDrug;
import com.puree.hospital.monitor.common.domain.BusDictDrugsUsage;
import com.puree.hospital.monitor.common.domain.BusDrugs;
import com.puree.hospital.monitor.common.domain.BusPrescription;
import com.puree.hospital.monitor.common.domain.BusPrescriptionDrugs;
import com.puree.hospital.monitor.common.repository.BusDictDrugsUsageRepository;
import com.puree.hospital.monitor.regulatory.event.assembler.EventDataAssembler;
import com.puree.hospital.monitor.regulatory.event.helper.WmTotalDosageHelper;
import com.puree.hospital.monitor.regulatory.event.model.RegulatoryHospitalConfig;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 云南处方(西药)
 * </p>
 *
 * <AUTHOR>
 * @date 2024/6/25 14:46
 */
@Component(RegulatoryAreaConstant.YUN_NAN + RegulatoryEventConstant.PRESCRIPTION_WM_CREATE + EventDataAssembler.SUFFIX)
public class YnPrescriptionWmCreateEventDataAssembler extends YnBasePrescriptionCreateEventDataAssembler<YnPrescriptionWmCreate> {

    @Resource
    private BusDictDrugsUsageRepository busDictDrugsUsageRepository;

    @Resource
    private WmTotalDosageHelper wmTotalDosageHelper;

    @Override
    protected YnPrescriptionWmCreate newInstance() {
        return new YnPrescriptionWmCreate();
    }

    @Override
    protected void assembleExtra(YnPrescriptionWmCreate regulatory, BusPrescription busPrescription, RegulatoryHospitalConfig config) {
        List<BusPrescriptionDrugs> prescriptionDrugs = super.getPrescriptionDrugs(busPrescription.getId());
        Map<Long, BusDrugs> busDrugsMap = super.getDrugsMap(prescriptionDrugs);
        List<String> usageValueList = prescriptionDrugs.stream().map(BusPrescriptionDrugs::getDrugsUsageValue).collect(Collectors.toList());
        List<BusDictDrugsUsage> busDictDrugsUsages = busDictDrugsUsageRepository.getByNameList(usageValueList);
        if (CollectionUtil.isEmpty(busDictDrugsUsages)) {
            throw new IllegalArgumentException(String.format("未找到药品用法关联信息, 处方id：%s", busPrescription.getId()));
        }
        Map<String, BusDictDrugsUsage> busDictDrugsUsageMap = busDictDrugsUsages.stream().collect(Collectors.toMap(BusDictDrugsUsage::getName, Function.identity()));
        List<YnWmPrescriptionDrug> ynWmPrescriptionDrugs = prescriptionDrugs.stream().map(prescriptionDrug -> {
            BusDrugs busDrugs = busDrugsMap.get(prescriptionDrug.getDrugsId());
            if (Objects.isNull(busDrugs)) {
                throw new IllegalArgumentException(String.format("药品名：%s未找到关联的药品基础信息", prescriptionDrug.getDrugsName()));
            }
            BusDictDrugsUsage busDictDrugsUsage = busDictDrugsUsageMap.get(prescriptionDrug.getDrugsUsageValue());
            if (Objects.isNull(busDictDrugsUsage)) {
                throw new IllegalArgumentException(String.format("药品名：%s未找到药品用法关联信息", prescriptionDrug.getDrugsName()));
            }
            Map<Long, String> commodityClassifyMap = getCommodityClassifyMap();
            return convert(prescriptionDrug, busDrugs, busDictDrugsUsage, commodityClassifyMap);
        }).collect(Collectors.toList());
        regulatory.setPrescriptionDrugList(ynWmPrescriptionDrugs);
        //设置处方金额
        regulatory.setPrescriptionAmount(ynWmPrescriptionDrugs.stream().map(YnWmPrescriptionDrug::getTotalAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
    }

    @Override
    protected String getEventType() {
        return RegulatoryEventConstant.PRESCRIPTION_WM_CREATE;
    }

    /**
     * 西药药品明细转换
     *
     * @param prescriptionDrug     西药药品明细
     * @param busDrugs             药品基础信息
     * @param busDictDrugsUsage    药品用法关联信息
     * @param commodityClassifyMap 药品分类
     * @return 西药药品明细
     */
    private YnWmPrescriptionDrug convert(BusPrescriptionDrugs prescriptionDrug,
                                         BusDrugs busDrugs,
                                         BusDictDrugsUsage busDictDrugsUsage,
                                         Map<Long, String> commodityClassifyMap) {
        YnWmPrescriptionDrug drug = new YnWmPrescriptionDrug();
        drug.setDrugsNumber(prescriptionDrug.getId() + "");
        //药品标准编码 -- ypid
        drug.setDrugsStandardCode(getDrugStandardCode(busDrugs, commodityClassifyMap));
        drug.setDrugsName(prescriptionDrug.getDrugsName());
        drug.setDrugsManufacturer(prescriptionDrug.getDrugsManufacturer());
        drug.setNmpn(busDrugs.getNmpn());
        drug.setDrugsSpecification(prescriptionDrug.getDrugsSpecification());
        drug.setMedicationDays(prescriptionDrug.getMedicationDays());
        drug.setSingleDose(prescriptionDrug.getSingleDose());
        drug.setUnit(prescriptionDrug.getUnit());
        String medicationFrequency = prescriptionDrug.getMedicationFrequency();
        String medicationFrequencyRemarks = prescriptionDrug.getMedicationFrequencyRemarks();
        if ("其他".equals(medicationFrequency)) {
            medicationFrequency = "oth";
            medicationFrequencyRemarks = "其他";
        }
        drug.setMedicationFrequency(medicationFrequency);
        drug.setMedicationFrequencyRemarks(medicationFrequencyRemarks);
        drug.setDrugsUsageValue(prescriptionDrug.getDrugsUsageValue());
        drug.setDrugsUsageValueCode(busDictDrugsUsage.getCode() + "");
        drug.setMinPackUnit(StringUtils.isNotBlank(busDrugs.getMinPackUnit()) ? busDrugs.getMinPackUnit() : "盒");
        if (Objects.nonNull(prescriptionDrug.getSellingPrice())) {
            drug.setSellingPrice(prescriptionDrug.getSellingPrice().setScale(4, RoundingMode.HALF_UP));
        } else {
            drug.setSellingPrice(BigDecimal.ZERO);
        }
        drug.setQuantity(prescriptionDrug.getQuantity());
        BigDecimal totalAmount = drug.getSellingPrice().multiply(new BigDecimal(drug.getQuantity())).setScale(2, RoundingMode.HALF_UP);
        drug.setTotalAmount(totalAmount);
        //总剂量计算 = 单次剂量 * 每天占用药物剂量 * 持续天数

        BigDecimal singleDose = BigDecimal.valueOf(prescriptionDrug.getSingleDose());
        BigDecimal bigDecimal = new BigDecimal(prescriptionDrug.getMedicationDays());
        BusDictDrugsFrequencyDTO busDictDrugsFrequency = wmTotalDosageHelper.getFrequencyByName(medicationFrequency, prescriptionDrug.getHospitalId());
        BigDecimal totalDosage = wmTotalDosageHelper.getTotalDosage(busDictDrugsFrequency, singleDose, bigDecimal);
        //向上取整
        drug.setTotalDosage(totalDosage.toPlainString());
        return drug;
    }

    /**
     * 获取药品标准编码
     * 国家药品标准编码。详见《国家药管平台药品分类编码与基本数据库 4.0 版》。
     * 无法找到对应国家药品标准编码的本院制剂化学药，填写“199609999999”。
     * 无法找到对应国家药品标准编码的本院制剂中成药，填写“399609999999”。
     *
     * @param busDrugs              药品信息
     * @param commodityClassifyMap  药品分类
     * @return 药品标准编码
     */
    private String getDrugStandardCode(BusDrugs busDrugs, Map<Long, String> commodityClassifyMap) {
        if (StringUtils.isNotBlank(busDrugs.getYpid())) {
            return busDrugs.getYpid();
        }
        if (MapUtil.isNotEmpty(commodityClassifyMap) && commodityClassifyMap.containsKey(busDrugs.getDrugsType())) {
            String classifyName = commodityClassifyMap.get(busDrugs.getDrugsType());
            if (classifyName.contains("中成药")) {
                return "399609999999";
            }
        }
        return "199609999999";
    }
}
