package com.puree.hospital.monitor.guangdong.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.puree.hospital.monitor.guangdong.domain.MedicalRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface MedicalRecordMapper extends BaseMapper<MedicalRecord> {
    List<MedicalRecord> selectSyncDataList(@Param("hospitalId") Long hospitalId);
}
