package com.puree.hospital.monitor.guangdong.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("gd_t_ms_treatment_diagnose")
public class MedicalDiagnosis {
    /** 出生日期 */
    private Date csrq;
    /** 撤销标志 */
    private String cxbz;
    /** 服务网点代码 */
    private String fwwddm;
    /** 机构标识 */
    private String jgdm;
    /** 就诊日期时间 */
    private Date jzrqsj;
    /** 卡号 */
    private String kh;
    /** 卡类型 */
    private String klx;
    /** 门诊号 */
    private String mzh;
    /** 年龄 */
    private Integer nls;
    /** 年龄月 */
    private String nly;
    /** 数据生成日期时 */
    private Date sjscsj;
    /** 性别 */
    private String xbdm;
    /** 姓名 */
    private String xm;
    /** 诊断标志 */
    private String zdbz;
    /** 诊断类型编码 */
    private String zdlxbm;
    /** 中医诊断编码 */
    private String zyzdbm;
    /** 中医诊断编码类型 */
    private String zyzdbmlx;
    /** 中医诊断名称 */
    private String zyzdmc;
    /** 西医诊断编码 */
    private String xyzdbm;
    /** 西医诊断编码类型 */
    private String xyzdbmlx;
    /** 西医诊断名称 */
    private String xyzdmc;
    /** 诊断信息ID */
    private String zdxxid;
    /** 诊断医生工号 */
    private String zdysgh;
    /** 诊断医生姓名 */
    private String zdysxm;
    /** 创建时间（辅助作用） */
    private Date createTime;
    private Long hospitalId;
    @TableField(exist = false)
    private Long familyId;
    @TableField(exist = false)
    private String prescriptionType;
    @TableField(exist = false)
    private String clinicalDiagnosis;
}
