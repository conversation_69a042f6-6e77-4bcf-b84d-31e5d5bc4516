package com.puree.hospital.monitor.datashare.service;

import com.puree.hospital.monitor.datashare.domain.vo.BusBizDepartmentVO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/1/16 15:47
 */
public interface IBusBizDepartmentService {
    /**
     * 查询科室信息
     * @param hospitalCode 医院标识
     * @return
     */
    List<BusBizDepartmentVO> queryDepartment(String hospitalCode);

    /**
     * 查询科室ID
     * @param doctorId
     * @return
     */
    String queryDrDeptId(String doctorId, String hospitalCode);
}
