package com.puree.hospital.monitor.datashare.domain.vo;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/1/17 15:44
 */
@Data
public class BusBizDepartmentVO {
    /**
     * 医院编码
     */
    private String hosId;
    /**
     * 科室ID
     */
    private String deptId;
    /**
     * 科室名称
     */
    private String deptName;
    /**
     * 科室介绍
     */
    private String deptIntro;
    /**
     * 科室级别（1一级科室 2二级科室）
     */
    private Integer level;
    /**
     * 上级科室Id
     */
    private String parentDeptId;
    /**
     * 对应标准科室ID
     */
    private String stdDeptId;
    /**
     * 科室链接地址
     */
    private String deptUrl;
    /**
     * 科室小程序跳转路径
     */
    private String deptWxMpPath;
}
