package com.puree.hospital.monitor.common.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.puree.hospital.monitor.common.domain.ChineseMedicalHerbalClassifyCode;
import com.puree.hospital.monitor.common.mapper.ChineseMedicalHerbalClassifyCodeMapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>
 * 中草药分类编码 Repository
 * </p>
 *
 * <AUTHOR>
 * @date 2024/6/28 10:12
 */
@Repository
public class ChineseMedicalHerbalClassifyCodeRepository extends ServiceImpl<ChineseMedicalHerbalClassifyCodeMapper,
        ChineseMedicalHerbalClassifyCode> {

    /**
     * 根据药品名称查询
     *
     * @param drugsNameList 药品名称列表
     * @return 中草药分类编码列表
     */
    public List<ChineseMedicalHerbalClassifyCode> getByDrugsName(List<String> drugsNameList) {
        LambdaQueryWrapper<ChineseMedicalHerbalClassifyCode> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ChineseMedicalHerbalClassifyCode::getDrugName, drugsNameList);
        return list(queryWrapper);
    }

}
