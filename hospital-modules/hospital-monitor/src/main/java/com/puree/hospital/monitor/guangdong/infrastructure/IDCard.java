package com.puree.hospital.monitor.guangdong.infrastructure;

public enum IDCard {
    JMSFZ("01", "居民身份证"),
    JMHKB("02", "居民户口簿"),
    HZ("03", "护照"),
    JGZ("04", "军官证"),
    JSZ("05", "驾驶证"),
    GAJMLWNDTXZ("06", "港澳居民来往内地通行证"),
    TWJMLWNDTXZ("07", "台湾居民来往内地通行证"),
    QT("99", "其他");

    private final String code;
    private final String info;

    IDCard(String code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public String getCode()
    {
        return code;
    }
    public String getInfo()
    {
        return info;
    }
}
