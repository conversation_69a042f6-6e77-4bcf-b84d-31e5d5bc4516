package com.puree.hospital.monitor.guangdong.infrastructure;

/**
 * 专业技术职务代码
 */
public enum Zyjszwdm {
    ZY230("230", "卫生技术人员（医师）"),
    ZY231("231", "主任医师"),
    <PERSON>Y232("232", "副主任医师"),
    <PERSON>Y233("233", "主治医师"),
    <PERSON><PERSON>234("234", "医师"),
    <PERSON>Y235("235", "医士"),
    <PERSON>Y240("240", "卫生技术人员（药剂）"),
    <PERSON>Y241("241", "主任药师"),
    ZY242("242", "副主任药师"),
    <PERSON>Y243("243", "主管药师"),
    <PERSON>Y244("244", "药师"),
    ZY245("245", "药士"),
    ZY250("250", "卫生技术人员（护理））"),
    ZY251("251", "主任护师"),
    ZY252("252", "副主任护师"),
    ZY253("253", "主管护师"),
    ZY254("254", "护师"),
    <PERSON>Y255("255", "护士"),
    <PERSON>Y260("260", "卫生技术人员（技师）"),
    <PERSON>Y261("261", "主任技师"),
    ZY262("262", "副主任技师"),
    ZY263("263", "主管技师"),
    ZY264("264", "技师"),
    ZY265("265", "技士");

    private final String code;
    private final String info;

    Zyjszwdm(String code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public String getCode()
    {
        return code;
    }
    public String getInfo()
    {
        return info;
    }
}
