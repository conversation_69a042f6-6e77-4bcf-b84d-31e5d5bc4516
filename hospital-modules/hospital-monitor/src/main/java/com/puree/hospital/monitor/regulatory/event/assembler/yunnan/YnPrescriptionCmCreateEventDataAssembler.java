package com.puree.hospital.monitor.regulatory.event.assembler.yunnan;

import cn.hutool.core.util.NumberUtil;
import com.google.common.collect.ImmutableMap;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.monitor.api.event.constant.RegulatoryAreaConstant;
import com.puree.hospital.monitor.api.event.constant.RegulatoryEventConstant;
import com.puree.hospital.monitor.api.event.regulatory.report.yunnan.YnCmPrescriptionDrug;
import com.puree.hospital.monitor.api.event.regulatory.report.yunnan.YnPrescriptionCmCreate;
import com.puree.hospital.monitor.common.domain.BusPrescription;
import com.puree.hospital.monitor.common.domain.BusPrescriptionDrugs;
import com.puree.hospital.monitor.common.domain.ChineseMedicalHerbalClassifyCode;
import com.puree.hospital.monitor.common.repository.ChineseMedicalHerbalClassifyCodeRepository;
import com.puree.hospital.monitor.regulatory.event.assembler.EventDataAssembler;
import com.puree.hospital.monitor.regulatory.event.model.RegulatoryHospitalConfig;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 云南处方（中药）
 * </p>
 *
 * <AUTHOR>
 * @date 2024/6/25 14:47
 */
@Component(RegulatoryAreaConstant.YUN_NAN + RegulatoryEventConstant.PRESCRIPTION_CM_CREATE + EventDataAssembler.SUFFIX)
public class YnPrescriptionCmCreateEventDataAssembler extends YnBasePrescriptionCreateEventDataAssembler<YnPrescriptionCmCreate> {

    @Resource
    private ChineseMedicalHerbalClassifyCodeRepository chineseMedicalHerbalClassifyCodeRepository;

    @Override
    protected YnPrescriptionCmCreate newInstance() {
        return new YnPrescriptionCmCreate();
    }

    @Override
    protected void assembleExtra(YnPrescriptionCmCreate regulatory, BusPrescription busPrescription, RegulatoryHospitalConfig config) {
        String usages = busPrescription.getUsages();
        if (StringUtils.isBlank(usages)) {
            throw new IllegalArgumentException(String.format("缺少药品用法, 处方id：%s", busPrescription.getId()));
        }
        // 药品用法分为 4个属性， 第一->内服、外用， 第二->处方总帖数、 第三->每天多少剂 第四->每剂多少次
        String[] usagesArr = usages.split(",");
        if (usagesArr.length < 4) {
            throw new IllegalArgumentException(String.format("药品用法格式错误, 处方id：%s", busPrescription.getId()));
        }
        Integer doseCountPerDay = Integer.valueOf(usagesArr[2]);
        //帖数
        Integer tcmPasteCnt = Integer.valueOf(usagesArr[1]);
        //总帖数
        regulatory.setTcmPasteCnt(tcmPasteCnt);
        String medicationFrequencyCode = getMedicationFrequencyCode(doseCountPerDay, Integer.valueOf(usagesArr[3]));
        //频次
        regulatory.setMedicationFrequency(medicationFrequencyCode);
        //频次备注
        regulatory.setMedicationFrequencyRemarks(MEDICAL_FREQUENCY_CODE_MAP.get(medicationFrequencyCode));
        //药品明细信息
        List<BusPrescriptionDrugs> prescriptionDrugsList = super.getPrescriptionDrugs(busPrescription.getId());
        List<String> durgsNameList = prescriptionDrugsList.stream().map(s -> {
            if (StringUtils.isNotBlank(s.getDrugsName())) {
                return s.getDrugsName().replaceAll("颗粒", "");
            }
            return s.getDrugsName();
        }).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        List<ChineseMedicalHerbalClassifyCode> list = chineseMedicalHerbalClassifyCodeRepository.getByDrugsName(durgsNameList);
        Map<String, ChineseMedicalHerbalClassifyCode> classifyCodeMap = list.stream().collect(Collectors.toMap(ChineseMedicalHerbalClassifyCode::getDrugName, s -> s));
        List<YnCmPrescriptionDrug> ynWmPrescriptionDrugs = prescriptionDrugsList.stream()
                .map(prescriptionDrug -> convert(prescriptionDrug, tcmPasteCnt, doseCountPerDay, classifyCodeMap))
                .collect(Collectors.toList());
        regulatory.setPrescriptionDrugList(ynWmPrescriptionDrugs);
        //设置处方总金额
        regulatory.setPrescriptionAmount(ynWmPrescriptionDrugs.stream().map(YnCmPrescriptionDrug::getTotalAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
    }

    @Override
    protected String getEventType() {
        return RegulatoryEventConstant.PRESCRIPTION_CM_CREATE;
    }

    /**
     * 中药药品明细转换
     *
     * @param prescriptionDrug 处方药品明显
     * @param tcmPasteCnt      总帖数
     * @param doseCountPerDay  每日多少剂
     * @param classifyCodeMap  种草药品分类编码
     * @return 中药处方名称
     */
    private YnCmPrescriptionDrug convert(BusPrescriptionDrugs prescriptionDrug,
                                         Integer tcmPasteCnt,
                                         Integer doseCountPerDay,
                                         Map<String, ChineseMedicalHerbalClassifyCode> classifyCodeMap) {
        YnCmPrescriptionDrug drug = new YnCmPrescriptionDrug();
        drug.setDrugsNumber(prescriptionDrug.getId() + "");
        String drugsName = prescriptionDrug.getDrugsName();
        //药品标准编码 -> 逻辑
        String drugsStandardCode = "49900999";
        ChineseMedicalHerbalClassifyCode chineseMedicalHerbalClassifyCode = classifyCodeMap.get(drugsName.replaceAll("颗粒", ""));
        if (Objects.nonNull(chineseMedicalHerbalClassifyCode)) {
            drugsStandardCode = chineseMedicalHerbalClassifyCode.getDrugStandardCode();
        }
        //药品标准编码
        drug.setDrugsStandardCode(drugsStandardCode);
        drug.setDrugsName(drugsName);
        drug.setDrugsManufacturer(prescriptionDrug.getDrugsManufacturer());
        //处方天数 = 向上取整（总帖数 / 每日多少剂）
        drug.setMedicationDays((int) Math.ceil((double) tcmPasteCnt / doseCountPerDay));
        //单次剂量 = 处方重量 / 总帖数
        drug.setSingleDose(NumberUtil.div(new BigDecimal(prescriptionDrug.getWeight()), BigDecimal.valueOf(tcmPasteCnt), 2, RoundingMode.HALF_UP));
        //数量 = 单剂重量 * 计数
        drug.setQuantity((int)Math.ceil((double) tcmPasteCnt * Double.parseDouble(prescriptionDrug.getWeight())));
        if (Objects.nonNull(prescriptionDrug.getSellingPrice())) {
            drug.setSellingPrice(prescriptionDrug.getSellingPrice().setScale(4, RoundingMode.HALF_UP));
        } else {
            drug.setSellingPrice(BigDecimal.ZERO);
        }
        BigDecimal totalAmount = drug.getSellingPrice().multiply(new BigDecimal(drug.getQuantity())).setScale(2, RoundingMode.HALF_UP);
        drug.setTotalAmount(totalAmount);
        return drug;
    }

    /**
     * 中药频次代码
     * 编码 名称
     * 01 每日一剂
     * 02 每日二剂
     * 03 每日三剂
     * 04 每日一次
     * 05 每日二次
     * 06 每日三次
     * 07 每日多次
     * 99 其他
     * 注意:static属性必须保证类在初始化过程中不能出错（如下，ImmutableMap的put方法不能put-key相同的数据），
     * 一旦出错，spring就无法找到这个bean（java.lang.NoClassDefFoundError），会导致spring的bean创建失败
     */
    private static final ImmutableMap<String, String> MEDICAL_FREQUENCY_CODE_MAP = ImmutableMap.<String, String>builder()
            .put("01", "每日一剂")
            .put("02", "每日二剂")
            .put("03", "每日三剂")
            .put("04", "每日一次")
            .put("05", "每日二次")
            .put("06", "每日三次")
            .put("07", "每日多次")
            .put("99", "其他")
            .build();

    /**
     * 频次
     *
     * @param dosePerDay   每日剂量
     * @param timesPerDose 每剂使用次数
     * @return 频次
     */
    private String getMedicationFrequencyCode(Integer dosePerDay, Integer timesPerDose) {
        int times = dosePerDay * timesPerDose;
        switch (times) {
            case 1:
                return "04";
            case 2:
                return "05";
            case 3:
                return "06";
            default:
                return "07";
        }
    }
}
