package com.puree.hospital.monitor.guangdong.controller;


import com.puree.hospital.operate.api.annotation.Log;
import com.puree.hospital.operate.api.model.enums.BusinessType;
import com.puree.hospital.monitor.guangdong.domain.model.UrlResult;
import com.puree.hospital.monitor.guangdong.service.SpotCheckService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/***
 * @Author: liuwangda
 * 广东监管正式接入抽查接口
 */
@RestController
@RequestMapping("/gd")
public class SpotCheckController {
    private static final Logger log = LoggerFactory.getLogger(SpotCheckController.class);
    @Autowired
    private SpotCheckService spotCheckService;

    /**
     * 处方的prescriptionPdf查询接口-返回对应的url
     * @return - 返回对应的url
     */
    @GetMapping("/GetPrescription")
    @Log(title = "处方的prescriptionPdf查询接口", businessType = BusinessType.QUERY)
    public UrlResult prescriptionPdf(@RequestParam(value = "Token",required = false) String token, @RequestParam(value = "Time",required = false) String time,
                                  @RequestParam(value = "RecordNo",required = false) String recordNo, @RequestParam(value = "Env", required = false,defaultValue = "prod") String environment
                                   , HttpServletResponse response) {
        log.debug("prescriptionPdf接口请求参数,token:{},time:{},recordNo:{},environment:{}", token, time, recordNo, environment);
        try {
            if (token == null || time == null || recordNo == null) {
                // 参数不全-返回错误码202
                response.setStatus(HttpServletResponse.SC_ACCEPTED);
                return null;
            }
            UrlResult urlResult = null;
            boolean flag = spotCheckService.checkToken(token, time);
            if (!flag) {
                // 鉴权失败-返回错误码401
                response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                return null;
            }
            List<String> pdfUrls = spotCheckService.getPrescriptionPdfUrl(recordNo, environment);
            if (pdfUrls == null) {
                // 未找到-返回错误码404
                response.setStatus(HttpServletResponse.SC_NOT_FOUND);
            } else {
                urlResult = new UrlResult(null, pdfUrls);
            }
            return urlResult;
        } catch (Exception e) {
            log.warn("prescriptionPdf接口异常,{}", e.getMessage());
            // 服务器内部错误-返回错误码500
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            throw new IllegalArgumentException("内部服务器错误");
        }
    }

    @GetMapping("/GetDoctorVedio")
    @Log(title = "医生视频查询接口", businessType = BusinessType.QUERY)
    public String doctorVideo(@RequestParam(value = "Token",required = false) String token, @RequestParam(value = "Time",required = false) String time,
                                     @RequestParam(value = "RecordNo",required = false) String recordNo, @RequestParam(value = "Env", required = false,defaultValue = "prod") String environment,
                              HttpServletResponse response) {
        log.debug("doctorVideo接口请求参数,token:{},time:{},recordNo:{},environment:{}", token, time, recordNo, environment);
        try {
            if (token == null || time == null || recordNo == null) {
                // 参数不全-返回错误码202
                response.setStatus(HttpServletResponse.SC_ACCEPTED);
                return null;
            }
            UrlResult urlResult = null;
            boolean flag = spotCheckService.checkToken(token, time);
            if (!flag) {
                // 鉴权失败-返回错误码401
                response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                return null;
            }
            String doctorVideoUrl = spotCheckService.getVideoUrl(recordNo,environment);
            if (doctorVideoUrl == null || doctorVideoUrl.isEmpty()) {
                // 未找到-返回错误码404
                response.setStatus(HttpServletResponse.SC_NOT_FOUND);
            }
            return doctorVideoUrl;
        } catch (Exception e) {
            log.warn("医生视频查询接口异常,{}", e.getMessage());
            // 服务器内部错误-返回错误码500
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            throw new IllegalArgumentException("内部服务器错误");
        }
    }

    @GetMapping("/GetPatientVedio")
    @Log(title = "患者视频查询接口", businessType = BusinessType.QUERY)
    public String patientVideo(@RequestParam(value = "Token",required = false) String token, @RequestParam(value = "Time",required = false) String time,
                                 @RequestParam(value = "RecordNo",required = false) String recordNo, @RequestParam(value = "Env", required = false,defaultValue = "prod") String environment,
                               HttpServletResponse response) {
        log.debug("患者视频查询接口请求参数,token:{},time:{},recordNo:{},environment:{}", token, time, recordNo, environment);
        try {
            if (token == null || time == null || recordNo == null) {
                // 参数不全-返回错误码202
                response.setStatus(HttpServletResponse.SC_ACCEPTED);
                return null;
            }
            UrlResult urlResult = null;
            boolean flag = spotCheckService.checkToken(token, time);
            if (!flag) {
                // 鉴权失败-返回错误码401
                response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                return null;
            }
            String pdfUrls = spotCheckService.getVideoUrl(recordNo,environment);
            // 未找到-返回错误码404
            if (pdfUrls == null || pdfUrls.isEmpty()) {
                response.setStatus(HttpServletResponse.SC_NOT_FOUND);
            }
            return pdfUrls;
        } catch (Exception e) {
            log.warn("患者视频查询接口,{}", e.getMessage());
            // 服务器内部错误-返回错误码500
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            throw new IllegalArgumentException("内部服务器错误");
        }
    }



}
