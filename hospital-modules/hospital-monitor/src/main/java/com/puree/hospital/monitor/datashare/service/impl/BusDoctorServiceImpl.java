package com.puree.hospital.monitor.datashare.service.impl;

import com.puree.hospital.monitor.datashare.domain.vo.BusDoctorVO;
import com.puree.hospital.monitor.common.mapper.BusDoctorMapper;
import com.puree.hospital.monitor.datashare.service.IBusDoctorService;
import com.puree.hospital.monitor.datashare.service.SysDictDataService;
import com.puree.hospital.system.api.model.SysDictData;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/1/16 15:47
 */
@Service
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BusDoctorServiceImpl implements IBusDoctorService {
    private final BusDoctorMapper busDoctorMapper;
    private final SysDictDataService sysDictDataService;
    /**
     * 查询医生信息
     *
     * @param hospitalCode 医院标识
     * @return
     */
    @Override
    public List<BusDoctorVO> queryDoctor(String hospitalCode) {
        //查询到doctorVo信息（不包含doctor_title）,dict_code存入title属性中
        List<BusDoctorVO> busDoctorVOS = busDoctorMapper.queryDoctor(hospitalCode);
        List<String> dictTypes=new ArrayList<>();
        dictTypes.add("bus_doctor_title");
        dictTypes.add("bus_pharmacist_title");
        dictTypes.add("bus_nurse_tile");
        //获取所有的医生职称code->职称名字的map
        Map<Long, String> dictMap = sysDictDataService.getDictMapByTypes(dictTypes, SysDictData::getDictCode, SysDictData::getDictLabel);
        //将职称code转为对应名字
        busDoctorVOS.forEach(busDoctorVO -> {
            busDoctorVO.setDoctorTitle(dictMap.getOrDefault(busDoctorVO.getTitle(),null));
        });
        return busDoctorVOS;
    }


}
