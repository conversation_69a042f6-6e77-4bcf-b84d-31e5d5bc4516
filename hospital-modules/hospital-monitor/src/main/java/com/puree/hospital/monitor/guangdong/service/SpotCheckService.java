package com.puree.hospital.monitor.guangdong.service;

import com.puree.hospital.monitor.guangdong.domain.model.UrlResult;

import java.util.List;

public interface SpotCheckService {
    /**
     * 检查token是否合法
     * @param token - 校验参数
     * @param time - 时间戳
     * @return - 是否合法
     */
    boolean checkToken(String token, String time);

    /**
     * 获取处方pdf的url
     * @param recordNo- 门诊号
     * @param environment 调用方环境-默认生产环境
     * @return - 处方pdf的url
     */
    List<String> getPrescriptionPdfUrl(String recordNo, String environment);

    /**
     * 获取视频url
     * @param recordNo - 门诊号
     * @return - 视频url
     */
    String getVideoUrl(String recordNo, String environment);
}
