package com.puree.hospital.monitor.regulatory.event.assembler;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.ImmutableMultimap;
import com.puree.hospital.business.api.model.BusConsultationRecordWithSignVO;
import com.puree.hospital.common.core.enums.RegisterTypeEnum;
import com.puree.hospital.common.core.utils.IdCardNumberUtils;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.common.core.utils.sign.MD5SignatureUtil;
import com.puree.hospital.common.rabbitmq.enums.SendCallbackType;
import com.puree.hospital.common.rabbitmq.producer.PureeRabbitProducer;
import com.puree.hospital.monitor.api.event.constant.EventMessageQueueConstant;
import com.puree.hospital.monitor.api.event.regulatory.collect.RegulatoryCollectEvent;
import com.puree.hospital.monitor.api.event.regulatory.report.BaseRegulatory;
import com.puree.hospital.monitor.common.domain.BusCommodityClassify;
import com.puree.hospital.monitor.common.domain.BusConsultationOrder;
import com.puree.hospital.monitor.common.domain.BusDoctorPatientGroup;
import com.puree.hospital.monitor.common.domain.BusPatientFamily;
import com.puree.hospital.monitor.common.domain.BusPrescription;
import com.puree.hospital.monitor.common.repository.BusCommodityClassifyRepository;
import com.puree.hospital.monitor.common.repository.BusConsultationOrderRepository;
import com.puree.hospital.monitor.common.repository.BusDoctorPatientGroupRepository;
import com.puree.hospital.monitor.regulatory.event.model.RegulatoryHospitalConfig;
import com.rabbitmq.client.Channel;
import org.apache.commons.text.similarity.LevenshteinDistance;
import org.springframework.amqp.core.Message;

import javax.annotation.Resource;
import java.io.Serializable;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * <p>
 * 抽象的事件数据组装处理器
 * </p>
 *
 * <AUTHOR>
 * @date 2024/6/25 09:41
 */
public abstract class AbstractEventDataAssembler<T extends BaseRegulatory> implements EventDataAssembler<T> {

    @Resource
    protected PureeRabbitProducer pureeRabbitProducer;

    @Resource
    private BusDoctorPatientGroupRepository busDoctorPatientGroupRepository;

    @Resource
    private BusCommodityClassifyRepository busCommodityClassifyRepository;

    @Resource
    protected BusConsultationOrderRepository busConsultationOrderRepository;

    @Override
    public T assemble(RegulatoryCollectEvent event, RegulatoryHospitalConfig config) {
        T regulatory = newInstance();
        regulatory.setEventType(getEventType());
        regulatory.setHospitalId(event.getHospitalId());
        regulatory.setBusinessId(event.getBusinessId());
        regulatory.setEventTime(event.getEventTime());
        regulatory.setUnifiedCreditCode(config.getUnifiedCreditCode());
        regulatory.setProvinceAbbreviation(config.getProvinceAbbreviation());
        assembleExtraData(regulatory, event);
        assembleOtherInfo(regulatory, config);
        return regulatory;
    }

    @Override
    public boolean pushMessage(T regulatory, Channel channel, Message message) {
        return pureeRabbitProducer.send(getMqttExchange(), getMqttRoutingKey(regulatory), regulatory, SendCallbackType.NACK, channel, message.getMessageProperties().getDeliveryTag());
    }

    /**
     * <p>
     * 获取mqtt topic
     * </p>
     *
     * @return mqtt topic
     */
    protected String getMqttExchange() {
        return EventMessageQueueConstant.REGULATORY_MQTT_EXCHANGE;
    }

    /**
     * <p>
     * 获取mqtt routing key
     * </p>
     *
     * @param regulatory 监管实体对象
     * @return mqtt routing key
     */
    protected String getMqttRoutingKey(T regulatory) {
        //约定规则， topic = 根据区域简称 + hospital + hospitalId + 固定后缀
        return regulatory.getProvinceAbbreviation() + "-hospital-" + regulatory.getHospitalId() + EventMessageQueueConstant.REGULATORY_MQTT_ROUTING_KEY_SUFFIX;
    }

    /**
     * <p>
     * 创建一个新的实例
     * </p>
     *
     * @return 返回实例
     */
    protected abstract T newInstance();

    /**
     * 组装其他信息
     *
     * @param regulatory 监管实体对象
     * @param config     监管医院配置
     */
    protected abstract void assembleOtherInfo(T regulatory, RegulatoryHospitalConfig config);

    /**
     * 事件类型
     *
     * @return 事件类型
     */
    protected abstract String getEventType();

    /**
     * <p>
     * 获取生日
     * </p>
     *
     * @param idCardNo 身份证号码
     * @return 生日
     */
    protected final String getBirthday(String idCardNo) {
        String birthday = IdCardNumberUtils.getBirthFromIdCard(idCardNo);
        if (StringUtils.isNotBlank(birthday)) {
            return birthday.replace("-", "");
        }
        return null;
    }

    /**
     * <p>
     * 获取两个字符串之间的相似度
     * </p>
     *
     * @param str1 字符串1
     * @param str2 字符串2
     * @return 相似度
     */
    protected final double getSimilarity(String str1, String str2) {
        int distance = LevenshteinDistance.getDefaultInstance().apply(str1, str2);
        return 1 - (double) distance / (double) Math.max(str1.length(), str2.length());
    }

    /**
     * <p>
     * 获取文件地址前缀
     * </p>
     *
     * @return 文件地址前缀
     */
    protected final String getFileAddressPrefix() {
        String fileAddressPrefix = SpringUtil.getProperty("aliyun.oss.fileAddressPrefix");
        if (StringUtils.isBlank(fileAddressPrefix)) {
            throw new IllegalStateException("未找到服务文件url前缀配置信息");
        }
        return fileAddressPrefix;
    }

    /**
     * 医生职称数据
     */
    protected static final ImmutableMultimap<String, String> DOCTOR_TITLE_CODE_MAP = ImmutableMultimap.<String, String>builder()
            .put("主任医师", "231")
            .put("副主任医师", "232")
            .put("主治医师", "233")
            .put("医师", "234")
            .put("医士", "235")
            .build();

    /**
     * <p>
     * 获取就诊聊天记录url前缀
     * </p>
     *
     * @return 就诊聊天记录url前缀
     */
    protected final String getConsultationRecordUrlPrefix() {
        String consentRecordUrlPrefix = SpringUtil.getProperty("puree.consultationRecode.urlPrefix");
        if (StringUtils.isBlank(consentRecordUrlPrefix)) {
            throw new IllegalStateException("未找到就诊聊天记录url前缀配置信息");
        }
        return consentRecordUrlPrefix;
    }

    /**
     * <p>
     * 获取就诊聊天记录签名key
     * </p>
     *
     * @param appId appId
     * @return 就诊聊天记录签名key
     */
    protected final String getConsultationRecordSignKey(String appId) {
        String signKeyStr = SpringUtil.getProperty("puree.consultationRecode.signKeys");
        if (StringUtils.isBlank(signKeyStr)) {
            throw new IllegalStateException("未找到就诊聊天记录签名key配置信息");
        }
        //获取秘钥配置
        Map<String, String> keyConfig = JSON.parseObject(signKeyStr, new TypeReference<Map<String, String>>() {
        });
        String key = keyConfig.get(appId);
        if (StringUtils.isBlank(key)) {
            throw new IllegalStateException("未找到就诊聊天记录签名key配置信息");
        }
        return key;
    }

    /**
     * <p>
     * 获取咨询记录页面url
     * </p>
     *
     * @param hospitalId          医院id
     * @param doctorId            医生id
     * @param departmentId        科室id
     * @param patientId           患者id
     * @param familyId            就诊人id
     * @param appId               appId
     * @param consultationOrderId 问诊订单id
     * @return 咨询记录页面url
     */
    protected final String getConsultationRecordUrl(Long hospitalId,
                                                    Long doctorId,
                                                    Long departmentId,
                                                    Long patientId,
                                                    Long familyId,
                                                    String appId,
                                                    Long consultationOrderId) {
        BusDoctorPatientGroup group = getGroup(doctorId, patientId, familyId, departmentId, hospitalId);
        return getConsultationRecordUrl(hospitalId, doctorId, familyId, appId, consultationOrderId, group);
    }

    /**
     * <p>
     * 获取咨询记录页面url
     * </p>
     *
     * @param hospitalId          医院id
     * @param doctorId            医生id
     * @param familyId            就诊人id
     * @param appId               appId
     * @param consultationOrderId 问诊订单id
     * @param group               医生与患者关联的群组信息
     * @return 咨询记录页面url
     */
    protected final String getConsultationRecordUrl(Long hospitalId,
                                                    Long doctorId,
                                                    Long familyId,
                                                    String appId,
                                                    Long consultationOrderId,
                                                    BusDoctorPatientGroup group) {
        BusConsultationRecordWithSignVO vo = new BusConsultationRecordWithSignVO();
        vo.setId(group.getId());
        vo.setDoctorId(doctorId);
        vo.setHospitalId(hospitalId);
        vo.setFamilyId(familyId);
        vo.setAppId(appId);
        vo.setConsultationOrderId(consultationOrderId);
        String params = MD5SignatureUtil.getPreSignString(vo);
        return getConsultationRecordUrlPrefix() + "?" + params + "&sign=" + MD5SignatureUtil.createSign(vo, getConsultationRecordSignKey(appId));
    }


    /**
     * <p>
     * 获取医生与患者关联的群组信息
     * </p>
     *
     * @param doctorId     医生id
     * @param patientId    患者id
     * @param familyId     就诊人id
     * @param departmentId 科室id
     * @param hospitalId   医院id
     * @return 医生与患者关联的群组信息
     */
    protected final BusDoctorPatientGroup getGroup(Long doctorId,
                                                   Long patientId,
                                                   Long familyId,
                                                   Long departmentId,
                                                   Long hospitalId) {
        BusDoctorPatientGroup group = busDoctorPatientGroupRepository.getGroup(doctorId, patientId, familyId, departmentId, hospitalId);
        if (Objects.isNull(group)) {
            throw new IllegalArgumentException(String.format("未找到医生id:%s与患者:%s关联的群组信息", doctorId, patientId));
        }
        return group;
    }

    /**
     * <p>
     * 获取患者生日
     * </p>
     *
     * @param family 患者信息
     * @return 患者生日
     */
    protected final String getPatientFamilyBirthday(BusPatientFamily family) {
        if (Objects.isNull(family) || Objects.isNull(family.getDateOfBirth())) {
            throw new IllegalArgumentException("患者生日信息为空");
        }
        return DateUtil.format(family.getDateOfBirth(), DatePattern.PURE_DATE_PATTERN);
    }

    /**
     * 医生的电子签名
     * 监管需要，因此通过随机数生成唯一的电子签名
     *
     * @return 医生的电子签名
     */
    protected final String getDoctorCaSign() {
        return DigestUtil.sha256Hex(UUID.randomUUID().toString(), StandardCharsets.UTF_8.name());
    }

    /**
     * 组装附加信息信息
     *
     * @param regulatory 数据对象
     * @param event      监管事件对象
     */
    protected void assembleExtraData(T regulatory, RegulatoryCollectEvent event) {

    }

    /**
     * 获取药品分类名称
     *
     * @return 药品分类名称map
     */
    protected Map<Long, String> getCommodityClassifyMap() {
        //目前查询所有分类
        List<BusCommodityClassify> commodityClassifyList = busCommodityClassifyRepository.list();
        if (CollectionUtil.isEmpty(commodityClassifyList)) {
            return Collections.emptyMap();
        }
        return commodityClassifyList.stream().collect(Collectors.toMap(BusCommodityClassify::getId, BusCommodityClassify::getClassifyName));
    }

    /**
     * <p>
     * 根据处方查询关联的问诊记录
     * </p>
     *
     * @param prescription 处方信息
     * @return 是否有咨询记录
     */
    protected BusConsultationOrder getConsultationRecord(BusPrescription prescription) {
        if (Objects.isNull(prescription) || Objects.isNull(prescription.getConsultationOrderId())) {
            return null;
        }
        BusConsultationOrder consultationOrder = this.getConsultationOrder(prescription.getConsultationOrderId());
        //咨询记录
        if (Objects.nonNull(consultationOrder) && RegisterTypeEnum.isVisit(consultationOrder.getRegisterType())) {
            return consultationOrder;
        }
        return null;
    }

    /**
     * <p>
     * 获取咨询订单信息
     * </p>
     *
     * @param consultationOrderId 咨询订单id
     * @return 咨询订单信息
     */
    protected BusConsultationOrder getConsultationOrder(Serializable consultationOrderId) {
        if (Objects.isNull(consultationOrderId)) {
            return null;
        }
        return busConsultationOrderRepository.getById(consultationOrderId);
    }
}
