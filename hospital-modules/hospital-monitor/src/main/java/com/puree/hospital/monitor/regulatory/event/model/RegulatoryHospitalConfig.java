package com.puree.hospital.monitor.regulatory.event.model;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 监管医院配置信息
 * </p>
 *
 * <AUTHOR>
 * @date 2024/6/25 11:03
 */
@Data
public class RegulatoryHospitalConfig implements Serializable {

    private static final long serialVersionUID = -2086850406107540508L;

    /**
     * 医院名称(监管专用)
     */
    private String hospitalName;

    /**
     * 统一社会信用代码
     */
    private String unifiedCreditCode;

    /**
     * 机构代码(监管局下发的)
     */
    private String institutionCode;

    /**
     * 省份简称
     */
    private String provinceAbbreviation;

    /**
     * 医院类型:参考云南 参考卫生机构类别编码表
     */
    private String hosTypeCode;

    /**
     * 医院级别，参考医院等级（级）分类代码表
     */
    private String hosClassCode;

    /**
     * 医院等次，参考医院等级（等）分类代码表
     */
    private String hosDegreeCode;

    /**
     * 正式成立日期
     */
    private Date openDatetime;

    /**
     * 医院官网 URL
     */
    private String websiteUrl;

    /**
     * 医院法定代表人
     */
    private String legalRepresentative;

    /**
     * 负责人
     */
    private String principal;

    /**
     * 详细地址, 由于地址的省市区只存储了行政区域代码，因此直接通过配置中心配置地址
     */
    private String address;

    /**
     * 互联网执业许可发证日期 yyyyMMdd
     */
    private Date authorizeDate;

    /**
     * 互联网执业许可生效日期 yyyyMMdd
     */
    private Date effectiveDate;

    /**
     * 互联网执业许可失效日期  yyyyMMdd
     */
    private Date expireDate;
}
