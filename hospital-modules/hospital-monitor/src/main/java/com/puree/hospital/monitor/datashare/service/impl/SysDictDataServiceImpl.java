package com.puree.hospital.monitor.datashare.service.impl;

import com.puree.hospital.common.api.constant.SecurityConstants;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.monitor.datashare.service.SysDictDataService;
import com.puree.hospital.system.api.RemoteSysDictDataService;
import com.puree.hospital.system.api.model.SysDictData;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Description monitor模块统一的字典处理，远程调用system，获取字典信息，并做进一步处理
 * Date 2025/1/7  16:46
 * Author hemingyang
 */
@Service
public class SysDictDataServiceImpl implements SysDictDataService {
    @Resource
    private RemoteSysDictDataService remoteSysDictDataService;

    /**
     * 根据1个dict_type 获取所有相关的字典信息
     * @param type dict_type
     * @return 字典集合
     */
    @Override
    public List<SysDictData> getDictByType(String type) {
        //远程调用，查询字典
        R<List<SysDictData>> response = remoteSysDictDataService.getDictList(new SysDictData().setDictType(type), SecurityConstants.INNER);
        if(!response.isSuccess()){
            throw new RuntimeException("远程查询字典失败");
        }
        return response.getData();
    }

    /**
     * 根据多个dict_type，返回所有相关字典信息
     * @param types 多个dict_type
     * @return key为dict_type，value为对应字典集合的map
     */
    @Override
    public Map<String, List<SysDictData>> getDictByTypes(List<String> types) {
        //将types转为SysDictData
        List<SysDictData> dictList = types.stream().map(type -> new SysDictData().setDictType(type)).collect(Collectors.toList());
        //远程调用，查询字典
        R<Map<String, List<SysDictData>>> response = remoteSysDictDataService.getDictMap(dictList, SecurityConstants.INNER);
        if(!response.isSuccess()){
            throw new RuntimeException("远程查询字典失败");
        }
        return response.getData();
    }

    /**
     * 根据dict_code，查询字典
     * @param dictCode dict_code
     * @return 字典
     */
    @Override
    public SysDictData getDictByCode(Long dictCode) {
        R<SysDictData> response = remoteSysDictDataService.getDictByCode(dictCode,SecurityConstants.INNER);
        if(!response.isSuccess()){
            throw new RuntimeException("远程查询字典失败");
        }
        return response.getData();
    }

    /**
     * 根据dict_type,返回字典中某两个值的映射表
     * @param types dict_type列表
     * @param keyFunction 从SysDictData中获取key的lambda表达式
     * @param valueFunction 从SysDictData中获取value的lambda表达式
     * @return key->value映射的map
     */
    @Override
    public <T, U> Map<T, U> getDictMapByTypes(List<String> types, Function<SysDictData, T> keyFunction, Function<SysDictData, U> valueFunction) {
        //先获取到types对应的字典集合
        Map<String, List<SysDictData>> dictByTypes = getDictByTypes(types);
        //将map转为list
        List<SysDictData> dictList = dictByTypes.values().stream().flatMap(Collection::stream).collect(Collectors.toList());
        //将list，根据传入的规则，转为map
        Map<T,U> result = new HashMap<>();
        dictList.forEach(sysDictData -> result.put(keyFunction.apply(sysDictData),valueFunction.apply(sysDictData)));
        return result;
    }


}
