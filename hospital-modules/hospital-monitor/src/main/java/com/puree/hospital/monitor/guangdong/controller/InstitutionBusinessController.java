package com.puree.hospital.monitor.guangdong.controller;

import com.puree.hospital.common.core.web.controller.BaseController;
import com.puree.hospital.monitor.guangdong.domain.InstitutionBusiness;
import com.puree.hospital.monitor.guangdong.service.IInstitutionBusinessService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("guangdong/institutionbusiness")
public class InstitutionBusinessController extends BaseController {
    @Resource
    private IInstitutionBusinessService institutionBusinessService;

    /**
     * 机构开展业务表
     */
    @GetMapping("list")
    public List<InstitutionBusiness> list(@RequestParam("hospitalId") Long hospitalId,@RequestParam(defaultValue = "0") String isNewHos) {
        logger.info("机构开展业务表：入参{}", hospitalId);
        List<InstitutionBusiness> list = institutionBusinessService.selectList(hospitalId,isNewHos);
        logger.info("机构开展业务表：出参{}", list);
        return list;
    }
}
