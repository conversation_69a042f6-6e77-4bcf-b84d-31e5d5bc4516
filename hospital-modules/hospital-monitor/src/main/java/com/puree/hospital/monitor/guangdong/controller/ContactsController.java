package com.puree.hospital.monitor.guangdong.controller;

import com.puree.hospital.common.core.web.controller.BaseController;
import com.puree.hospital.monitor.guangdong.domain.Contacts;
import com.puree.hospital.monitor.guangdong.service.IContactsService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("guangdong/contacts")
public class ContactsController extends BaseController {
    @Resource
    private IContactsService contactsService;


    /**
     * 行政管理通讯表
     */
    @GetMapping("list")
    public List<Contacts> list(@RequestParam("hospitalId") Long hospitalId, @RequestParam(defaultValue = "0") String isNewHos) {
        logger.info("行政管理通讯表：入参{}", hospitalId);
        List<Contacts> list = contactsService.selectList(hospitalId, isNewHos);
        logger.info("行政管理通讯表：出参{}", list);
        return list;
    }

}
