package com.puree.hospital.monitor.common.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.puree.hospital.monitor.common.domain.BusAssociateDepartment;
import com.puree.hospital.monitor.common.mapper.BusAssociateDepartmentMapper;
import org.springframework.stereotype.Repository;

/**
 * <p>
 * 业务科室关联信息 repository
 * </p>
 *
 * <AUTHOR>
 * @date 2024/6/25 16:43
 */
@Repository
public class BusAssociateDepartmentRepository extends ServiceImpl<BusAssociateDepartmentMapper, BusAssociateDepartment> {

    /**
     * 根据业务科室id + 医院id查询 科室关联信息
     *
     * @param bizDepartmentId 业务科室id
     * @param hospitalId      医院id
     * @return BusAssociateDepartment
     */
    public BusAssociateDepartment getByBizDepartmentIdAndHospitalId(Long bizDepartmentId, Long hospitalId) {
        LambdaQueryWrapper<BusAssociateDepartment> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BusAssociateDepartment::getBizDepartmentId, bizDepartmentId)
                .eq(BusAssociateDepartment::getHospitalId, hospitalId);

        return this.getOne(queryWrapper, false);
    }

}
