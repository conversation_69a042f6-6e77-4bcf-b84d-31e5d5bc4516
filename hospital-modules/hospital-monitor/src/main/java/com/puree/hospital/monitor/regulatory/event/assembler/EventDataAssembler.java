package com.puree.hospital.monitor.regulatory.event.assembler;

import com.puree.hospital.monitor.api.event.regulatory.collect.RegulatoryCollectEvent;
import com.puree.hospital.monitor.api.event.regulatory.report.BaseRegulatory;
import com.puree.hospital.monitor.regulatory.event.model.RegulatoryHospitalConfig;
import com.rabbitmq.client.Channel;
import org.springframework.amqp.core.Message;

/**
 * <p>
 * 监管事件组装器-接口
 * </p>
 *
 * <AUTHOR>
 * @date 2024/6/25 09:30
 */
public interface EventDataAssembler<T extends BaseRegulatory> {

    String SUFFIX = "EventDataAssembler";

    /**
     * 组装推送上报事件数据
     *
     * @param event  事件信息
     * @param config 医院配置信息
     * @return 组成好的数据
     */
    T assemble(RegulatoryCollectEvent event, RegulatoryHospitalConfig config);

    /**
     * 推送消息
     *
     * @param regulatory 上报信息
     * @param channel    消息通道
     * @param message    原始消息
     * @return 是否需要ack
     */
    boolean pushMessage(T regulatory, Channel channel, Message message);
}
