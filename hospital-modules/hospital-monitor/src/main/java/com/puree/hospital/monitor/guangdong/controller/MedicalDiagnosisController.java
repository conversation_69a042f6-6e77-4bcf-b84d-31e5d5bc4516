package com.puree.hospital.monitor.guangdong.controller;

import com.puree.hospital.common.core.web.controller.BaseController;
import com.puree.hospital.common.api.domain.AjaxResult;
import com.puree.hospital.monitor.guangdong.domain.MedicalDiagnosis;
import com.puree.hospital.monitor.guangdong.service.IMedicalDiagnosisService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("guangdong/medicaldiagnosis")
public class MedicalDiagnosisController extends BaseController {
    @Resource
    private IMedicalDiagnosisService medicalDiagnosisService;

    /**
     * 医学诊断
     */
    @GetMapping("list")
    public List<MedicalDiagnosis> list(@RequestParam("hospitalId") Long hospitalId, @RequestParam(defaultValue = "0") String isNewHos) {
        logger.info("医学诊断：入参{}", hospitalId);
        //先同步
        medicalDiagnosisService.syncData(hospitalId);
        //再拉取
        List<MedicalDiagnosis> list = medicalDiagnosisService.selectList(hospitalId, isNewHos);
        logger.info("医学诊断：出参{}", list);
        return list;
    }

    /**
     * 同步数据
     */
    @PostMapping("/sync/{id}")
    public AjaxResult sync(@PathVariable Long id) {
        return toAjax(medicalDiagnosisService.syncData(id));
    }

    /**
     * 添加数据
     */
    @PostMapping("/insert")
    public AjaxResult insert(@RequestBody MedicalDiagnosis medicalDiagnosis) {
        return toAjax(medicalDiagnosisService.insert(medicalDiagnosis));
    }
}
