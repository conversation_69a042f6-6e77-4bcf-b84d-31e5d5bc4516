package com.puree.hospital.monitor.datashare.service.impl;

import com.puree.hospital.monitor.datashare.domain.vo.BusBizDepartmentVO;
import com.puree.hospital.monitor.common.mapper.BusBizDepartmentMapper;
import com.puree.hospital.monitor.datashare.service.IBusBizDepartmentService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/1/16 15:48
 */
@Service
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BusBizDepartmentServiceImpl implements IBusBizDepartmentService {
    private final BusBizDepartmentMapper busBizDepartmentMapper;

    /**
     * 查询科室信息
     *
     * @param hospitalCode 医院标识
     * @return
     */
    @Override
    public List<BusBizDepartmentVO> queryDepartment(String hospitalCode) {
        return busBizDepartmentMapper.queryDepartment(hospitalCode);
    }

    /**
     * 查询科室ID
     *
     * @param doctorId
     * @return
     */
    @Override
    public String queryDrDeptId(String doctorId, String hospitalCode) {
        return busBizDepartmentMapper.queryDrDeptId(doctorId, hospitalCode);
    }
}
