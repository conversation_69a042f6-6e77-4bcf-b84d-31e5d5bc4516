package com.puree.hospital.monitor.guangdong.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.puree.hospital.monitor.guangdong.domain.Contacts;
import com.puree.hospital.monitor.guangdong.domain.Device;
import com.puree.hospital.monitor.guangdong.mapper.DeviceMapper;
import com.puree.hospital.monitor.guangdong.service.IDeviceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;

@Service
public class DeviceServiceImpl implements IDeviceService {
    private final DeviceMapper deviceMapper;

    @Autowired
    public DeviceServiceImpl(DeviceMapper deviceMapper) {
        this.deviceMapper = deviceMapper;
    }

    @Override
    public List<Device> selectList(Long hospitalId,String isNewHos) {
        LambdaQueryWrapper<Device> queryWrapper;
        if ("0".equals(isNewHos)) {
            queryWrapper = new LambdaQueryWrapper<Device>()
                    .eq(Device::getHospitalId, hospitalId)
                    .ge(Device::getCreateTime, LocalDate.now());
        } else {
            queryWrapper = new LambdaQueryWrapper<Device>()
                    .eq(Device::getHospitalId, hospitalId);
        }
        return deviceMapper.selectList(queryWrapper);
    }
}
