package com.puree.hospital.monitor.guangdong.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.puree.hospital.monitor.guangdong.domain.Contacts;
import com.puree.hospital.monitor.guangdong.domain.ServicePoint;
import com.puree.hospital.monitor.guangdong.mapper.ServicePointMapper;
import com.puree.hospital.monitor.guangdong.service.IServicePointService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;

@Service
public class ServicePointServiceImpl implements IServicePointService {
    private final ServicePointMapper servicePointMapper;

    @Autowired
    public ServicePointServiceImpl(ServicePointMapper servicePointMapper) {
        this.servicePointMapper = servicePointMapper;
    }

    @Override
    public List<ServicePoint> selectList(Long hospitalId,String isNewHos) {
        LambdaQueryWrapper<ServicePoint> queryWrapper;
        if ("0".equals(isNewHos)) {
            queryWrapper = new LambdaQueryWrapper<ServicePoint>()
                    .eq(ServicePoint::getHospitalId, hospitalId)
                    .ge(ServicePoint::getCreateTime, LocalDate.now());
        }else{
            queryWrapper = new LambdaQueryWrapper<ServicePoint>()
                    .eq(ServicePoint::getHospitalId, hospitalId);
        }
        return servicePointMapper.selectList(queryWrapper);
    }
}
