package com.puree.hospital.monitor.guangdong.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.puree.hospital.monitor.guangdong.domain.Contacts;
import com.puree.hospital.monitor.guangdong.domain.Institution;
import com.puree.hospital.monitor.guangdong.mapper.InstitutionMapper;
import com.puree.hospital.monitor.guangdong.service.IInstitutionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class InstitutionServiceImpl implements IInstitutionService {

    private final InstitutionMapper institutionMapper;

    @Autowired
    public InstitutionServiceImpl(InstitutionMapper institutionMapper) {
        this.institutionMapper = institutionMapper;
    }

    @Override
    public List<Institution> selectList(Long hospitalId, String isNewHos) {
        LambdaQueryWrapper<Institution> queryWrapper;
        if ("0".equals(isNewHos)) {
            queryWrapper = new LambdaQueryWrapper<Institution>()
                    .eq(Institution::getHospitalId, hospitalId)
                    .ge(Institution::getCreateTime, LocalDate.now());
        } else {
            queryWrapper = new LambdaQueryWrapper<Institution>()
                    .eq(Institution::getHospitalId, hospitalId);
        }
        return institutionMapper.selectList(queryWrapper);
    }

}
