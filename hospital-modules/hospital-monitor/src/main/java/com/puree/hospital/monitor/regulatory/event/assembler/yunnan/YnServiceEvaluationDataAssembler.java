package com.puree.hospital.monitor.regulatory.event.assembler.yunnan;

import com.puree.hospital.business.api.RemoteEvaluationService;
import com.puree.hospital.business.api.model.BusEvaluationVO;
import com.puree.hospital.common.api.constant.SecurityConstants;
import com.puree.hospital.common.core.enums.EvaluationTypeEnum;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.monitor.api.event.constant.RegulatoryAreaConstant;
import com.puree.hospital.monitor.api.event.constant.RegulatoryEventConstant;
import com.puree.hospital.monitor.api.event.regulatory.report.yunnan.YnServiceEvaluation;
import com.puree.hospital.monitor.common.domain.BusConsultationOrder;
import com.puree.hospital.monitor.regulatory.event.assembler.AbstractEventDataAssembler;
import com.puree.hospital.monitor.regulatory.event.assembler.EventDataAssembler;
import com.puree.hospital.monitor.regulatory.event.model.RegulatoryHospitalConfig;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Objects;

/**
 * <p>
 * 云南监管-投诉建议数据组装
 * </p>
 *
 * <AUTHOR>
 * @date 2024/8/20 15:04
 */
@Component(RegulatoryAreaConstant.YUN_NAN + RegulatoryEventConstant.SERVICE_EVALUATION + EventDataAssembler.SUFFIX)
public class YnServiceEvaluationDataAssembler extends AbstractEventDataAssembler<YnServiceEvaluation> {

    @Resource
    private RemoteEvaluationService remoteEvaluationService;

    @Override
    protected YnServiceEvaluation newInstance() {
        return new YnServiceEvaluation();
    }

    @Override
    protected void assembleOtherInfo(YnServiceEvaluation regulatory, RegulatoryHospitalConfig config) {
        Long evaluationId = Long.valueOf(regulatory.getBusinessId());
        BusEvaluationVO evaluationVO = remoteEvaluationService.getFeignInfo(evaluationId, SecurityConstants.INNER).getData();
        if (Objects.isNull(evaluationVO)) {
            throw new IllegalStateException("查询服务评价信息为空,evaluationId：" + evaluationId);
        }
        if (!EvaluationTypeEnum.isConsultationOrder(evaluationVO.getEvaluationType())) {
            throw new IllegalArgumentException("当前的评价不是问诊评价，不做监管上报,evaluationId：" + evaluationId);
        }
        //互联网诊疗
        regulatory.setBusinessTypeCode("1");
        BusConsultationOrder order = super.getConsultationOrder(evaluationVO.getBusinessId());
        if (Objects.isNull(order)) {
            throw new IllegalStateException("查询服务评价关联的问诊订单为空,evaluationId：" + evaluationId);
        }
        regulatory.setBusinessCode(order.getOrderNo());
        regulatory.setEvaluationDatetime(evaluationVO.getEvaluationDatetime());
        BigDecimal score = Objects.nonNull(evaluationVO.getScore()) ? evaluationVO.getScore() : BigDecimal.ZERO;
        regulatory.setScoring(score.intValue() + "");
        String evaluations = StringUtils.isNotBlank(evaluationVO.getEvaluations()) ? evaluationVO.getEvaluations() : evaluationVO.getLabelNames();
        regulatory.setEvaluations(evaluations);
    }

    @Override
    protected String getEventType() {
        return RegulatoryEventConstant.SERVICE_EVALUATION;
    }
}
