package com.puree.hospital.monitor.regulatory.event.assembler.yunnan;

import com.puree.hospital.monitor.api.event.regulatory.report.yunnan.YnDepartment;
import com.puree.hospital.monitor.common.domain.BusAssociateDepartment;
import com.puree.hospital.monitor.common.domain.BusBizDepartment;
import com.puree.hospital.monitor.common.domain.BusDepartment;
import com.puree.hospital.monitor.common.repository.BusAssociateDepartmentRepository;
import com.puree.hospital.monitor.common.repository.BusBizDepartmentRepository;
import com.puree.hospital.monitor.common.repository.BusDepartmentRepository;
import com.puree.hospital.monitor.regulatory.event.assembler.AbstractEventDataAssembler;
import com.puree.hospital.monitor.regulatory.event.model.RegulatoryHospitalConfig;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <p>
 * 科室/科目备案信息组装抽象
 * </p>
 *
 * <AUTHOR>
 * @date 2024/6/25 17:00
 */
public abstract class YnBaseDepartmentEventDataAssembler<T extends YnDepartment> extends AbstractEventDataAssembler<T> {

    @Resource
    private BusBizDepartmentRepository busBizDepartmentRepository;

    @Resource
    private BusAssociateDepartmentRepository busAssociateDepartmentRepository;

    @Resource
    private BusDepartmentRepository busDepartmentRepository;

    @Override
    protected void assembleOtherInfo(T regulatory, RegulatoryHospitalConfig config) {
        BusBizDepartment busBizDepartment = busBizDepartmentRepository.getById(regulatory.getBusinessId());
        if (Objects.isNull(busBizDepartment)) {
            throw new IllegalStateException(String.format("未找到%s关联的业务科室信息", regulatory.getBusinessId()));
        }
        regulatory.setDepartmentName(busBizDepartment.getDepartmentName());
        regulatory.setDepartmentNumber(busBizDepartment.getDepartmentNumber());
        BusAssociateDepartment associateDepartment = busAssociateDepartmentRepository.getByBizDepartmentIdAndHospitalId(busBizDepartment.getId(), regulatory.getHospitalId());
        if (Objects.isNull(associateDepartment)) {
            throw new IllegalArgumentException(String.format("业务科室：%s未找到关联的标准科室信息", busBizDepartment.getDepartmentName()));
        }
        BusDepartment department = busDepartmentRepository.getById(associateDepartment.getDepartmentId());
        if (Objects.isNull(department)) {
            throw new IllegalArgumentException(String.format("业务科室：%s未找到关联的标准科室信息", busBizDepartment.getDepartmentName()));
        }
        regulatory.setDeptClassCode(department.getDepartmentNumber());
        regulatory.setIfAccepted(getIfAccepted(busBizDepartment.getStatus()));
        assembleExtra(regulatory, config);
    }

    protected void assembleExtra(T regulatory, RegulatoryHospitalConfig departmentConfig) {

    }

    /**
     * 可能会出现禁用操作
     *
     * @param status 医院状态信息
     * @return 0-禁用 1-启用
     */
    private boolean getIfAccepted(Integer status) {
        return Objects.equals(1, status);
    }

}
