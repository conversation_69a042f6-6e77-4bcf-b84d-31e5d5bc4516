package com.puree.hospital.monitor.guangdong.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("gd_t_ins_staff")
public class Staff {
    /**
     * 机构标识
     */
    private String jgdm;
    /**
     * 医护人员工号/登陆账号
     */
    private String yhrygh;
    /**
     * 医护人员姓名
     */
    private String yhryxm;
    /**
     * 性别
     */
    private String xb;
    /**
     * 出生日期
     */
    private Date csrq;
    /**
     * 身份证号
     */
    private String sfzh;
    /**
     * 身份证件类别代码
     */
    private String zjlbdm;
    /**
     * 所属科室代码
     */
    private String ksdm;
    /**
     * 专业技术职务代码
     */
    private String zyjszwdm;
    /**
     * 专业技术职务类别代码
     */
    private String zyjszwlbdm;
    /**
     * 资质类别名称（人员拥有的资质证书类别名称）
     */
    private String zzlbmc;
    /**
     * 资格证书编号
     */
    private String zgzsbh;
    /**
     * 资格获得时间
     */
    private Date zghdsj;
    /**
     * 执业证书编码
     */
    private String zyzsbm;
    /**
     * 发证日期
     */
    private Date fzrq;
    /**
     * 主要执业医院地点（使用医院名称）
     */
    private String zydd;
    /**
     * 执业范围
     */
    private String zyfw;
    /**
     * 主要执业医疗机构代码
     */
    private String zyzyyljgdm;
    /**
     * 主要执业医院名称
     */
    private String zyzyyymc;
    /**
     * 全科医生标志
     */
    private String qkysbz;
    /**
     * 手机号码
     */
    private String sjhm;
    /**
     * 参加工作日期
     */
    private Date cjgzrq;
    /**
     * 注册日期时间
     */
    private Date zcsj;
    /**
     * 个人照片存放地址
     */
    private String grzpcfdz;
    /**
     * 资格证存放地址
     */
    private String zgzcfdz;
    /**
     * 执业证存放地址
     */
    private String zyzcfdz;
    /**
     * 数据生成日期时间
     */
    private Date sjscsj;
    /**
     * 撤销标志
     */
    private String cxbz;
    /**
     * 创建时间（辅助作用）
     */
    private Date createTime;
    /**
     * 医院ID
     **/
    private Long hospitalId;
    /**
     * 职称
     */
    @TableField(exist = false)
    private Long title;
}
