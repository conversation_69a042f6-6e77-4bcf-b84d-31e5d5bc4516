package com.puree.hospital.monitor.common.repository;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.puree.hospital.monitor.guangdong.domain.MedicalRecord;
import com.puree.hospital.monitor.guangdong.mapper.MedicalRecordMapper;

import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class HospitalMedicalRecordRepository extends ServiceImpl<MedicalRecordMapper, MedicalRecord> {

    /**
     * 通过门诊号查找到对应的病历信息
     * @param recordNo -- 门诊号
     * @return -- 病历信息
     */
    public MedicalRecord findByRecordNo(String recordNo) {
        //门诊号
        QueryWrapper<MedicalRecord> mzh = new QueryWrapper<MedicalRecord>().eq("MZH", recordNo);
        mzh.last("LIMIT 1");
        return baseMapper.selectOne(mzh);
    }
}
