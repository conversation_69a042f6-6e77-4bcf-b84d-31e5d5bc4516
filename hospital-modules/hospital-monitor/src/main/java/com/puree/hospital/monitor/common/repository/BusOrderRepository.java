package com.puree.hospital.monitor.common.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.puree.hospital.monitor.common.domain.BusOrder;
import com.puree.hospital.monitor.common.mapper.BusOrderMapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>
 * 总订单信息 Repository
 * </p>
 *
 * <AUTHOR>
 * @date 2024/7/3 17:48
 */
@Repository
public class BusOrderRepository extends ServiceImpl<BusOrderMapper, BusOrder> {

    /**
     * 根据订单号查询订单信息
     *
     * @param orderNo 订单号
     * @return 订单信息
     */
    public List<BusOrder> getByOrderNo(String orderNo) {
        LambdaQueryWrapper<BusOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BusOrder::getOrderNo, orderNo);
        return list(queryWrapper);
    }
}
