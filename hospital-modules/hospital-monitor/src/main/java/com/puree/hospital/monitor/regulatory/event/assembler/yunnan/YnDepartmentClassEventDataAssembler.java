package com.puree.hospital.monitor.regulatory.event.assembler.yunnan;

import cn.hutool.core.date.DateUtil;
import com.puree.hospital.monitor.api.event.constant.RegulatoryAreaConstant;
import com.puree.hospital.monitor.api.event.constant.RegulatoryEventConstant;
import com.puree.hospital.monitor.api.event.regulatory.report.yunnan.YnDepartmentClass;
import com.puree.hospital.monitor.regulatory.event.assembler.EventDataAssembler;
import com.puree.hospital.monitor.regulatory.event.model.RegulatoryHospitalConfig;
import org.springframework.stereotype.Component;

/**
 * <p>
 * 云南科目备案
 * </p>
 *
 * <AUTHOR>
 * @date 2024/6/25 14:25
 */
@Component(RegulatoryAreaConstant.YUN_NAN + RegulatoryEventConstant.DEPARTMENT_CLASS_FILING + EventDataAssembler.SUFFIX)
public class YnDepartmentClassEventDataAssembler extends YnBaseDepartmentEventDataAssembler<YnDepartmentClass> {
    @Override
    protected YnDepartmentClass newInstance() {
        return new YnDepartmentClass();
    }

    @Override
    protected String getEventType() {
        return RegulatoryEventConstant.DEPARTMENT_CLASS_FILING;
    }

    @Override
    protected void assembleExtra(YnDepartmentClass regulatory, RegulatoryHospitalConfig config) {
        regulatory.setAuthorizeDate(DateUtil.format(config.getAuthorizeDate(), "yyyyMMdd"));
        regulatory.setEffectiveDate(DateUtil.format(config.getEffectiveDate(), "yyyyMMdd"));
        regulatory.setExpireDate(DateUtil.format(config.getExpireDate(), "yyyyMMdd"));
    }
}
