package com.puree.hospital.monitor.common.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.puree.hospital.monitor.common.domain.BusPrescription;
import com.puree.hospital.monitor.common.domain.BusPrescriptionQuery;
import com.puree.hospital.monitor.common.mapper.BusPrescriptionMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>
 * 处方信息 Repository
 * </p>
 *
 * <AUTHOR>
 * @date 2024/6/26 10:55
 */
@Repository
public class BusPrescriptionRepository extends ServiceImpl<BusPrescriptionMapper, BusPrescription> {

    @Autowired
    private BusPrescriptionMapper busPrescriptionMapper;
    /**
     * 根据咨询订单id查询处方信息
     *
     * @param consultationOrderId 咨询订单id
     * @return BusPrescription
     */
    public List<BusPrescription> getByConsultationOrderId(Long consultationOrderId) {
        LambdaQueryWrapper<BusPrescription> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BusPrescription::getConsultationOrderId, consultationOrderId)
                .orderByDesc(BusPrescription::getId);
        return list(wrapper);
    }

    /**
     * 根据咨询订单id查询处方信息
     * @param hospitalId 医院id
     * @param recordNo 病历号
     * @param familyName 患者姓名
     * @param doctorJobNumber 医生工号
     * @return
     */
    public List<BusPrescription> getByRecord(Long hospitalId, String recordNo,String familyName,String doctorJobNumber) {
        BusPrescriptionQuery busPrescriptionQuery = new BusPrescriptionQuery();
        busPrescriptionQuery.setHospitalId(hospitalId);
        busPrescriptionQuery.setRecordNo(recordNo);
        busPrescriptionQuery.setFamilyName(familyName);
        busPrescriptionQuery.setDoctorJobNumber(doctorJobNumber);
        return busPrescriptionMapper.getPrescriptionByRecordNo(busPrescriptionQuery);
    }

    /**
     * 根据处方编号查询处方信息
     * @param prescriptionNo - 处方编号
     * @return - 处方信息
     */
    public List<BusPrescription> getByRecord(List<String> prescriptionNo) {
        LambdaQueryWrapper<BusPrescription> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(BusPrescription::getPrescriptionNumber, prescriptionNo)
                .orderByDesc(BusPrescription::getId);
        return list(wrapper);
    }
}
