package com.puree.hospital.monitor.datashare.domain.vo;

import com.puree.hospital.monitor.common.domain.BusHospital;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/1/16 17:27
 */
@Data
public class BusHospitalVO extends BusHospital {

    private static final long serialVersionUID = 3847794158289361903L;
    /**
     * 医院地址
     */
    private String hosAddress;
    /**
     * 医院级别
     */
    private String hosLevel;
    /**
     * 微信小程序首页路径
     */
    private String wxMpPath;
    /**
     * 医院编码
     */
    private String hosId;
    /**
     * 医院网址
     */
    private String hosUrl;
    /**
     * 医院经纬度
     */
    private String location;
    /**
     * 微信小程序appId
     */
    private String wxMpAppId;
    /**
     * 医院名称
     */
    private String hosName;
    /**
     * 是否支持线上医保
     */
    private Boolean ybSupported;
    /**
     * 医院Logo
     */
    private String hosLogo;

}
