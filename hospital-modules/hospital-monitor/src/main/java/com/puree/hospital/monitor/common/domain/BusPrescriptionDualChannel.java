package com.puree.hospital.monitor.common.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 处方-双通道电子处方
 * </p>
 *
 * <AUTHOR>
 * @date 2024/12/08 16:00
 */
@Data
@TableName("bus_prescription_dual_channel")
public class BusPrescriptionDualChannel implements Serializable {

    private static final long serialVersionUID = 5556065043078080503L;

    /**
     * 处方id
     */
    @TableId
    private Long prescriptionId;

    /**
     * 电子处方追溯码
     */
    private String rxTraceCode;

    /**
     * 医保就诊id
     */
    private String mdtrtId;

    /**
     * 医保平台处方号
     */
    private String hiRxno;

    /**
     * 电子处方文件数组签名
     */
    private String signDigest;

    /**
     * 签名机构证书 SN
     */
    private String signCertSn;

    /**
     * 签名机构证书 DN
     */
    private String signCertDn;

    /**
     * 处方状态：医保状态 1-生效中，2-已失效，3-已撤销
     */
    private String rxStatus;

    /**
     * 处方状态名称
     */
    private String rxStatusName;

    /**
     * 处方审核状态：医保审核状态 0-未审核，1-审核通过，2-审核不通过
     */
    private String rxCheckStatus;

    /**
     * 处方审核状态名称
     */
    private String rxCheckStatusName;

    /**
     * 审核时间
     */
    private String checkTime;

    /**
     * 医药机构医保药师姓名
     * 是否非空：Y
     */
    private String pharName;

    /**
     * 医药医保药师代码
     * 是否非空：Y
     */
    private String pharCode;

    /**
     * 处方审核意见
     */
    private String rxCheckOpinion;

    /**
     * 结算时间
     */
    private String settleTime;

    /**
     * 处方使用状态：医保使用状态 1-未使用，1-已使用
     */
    private String rxUsedStatus;

    /**
     * 处方使用状态名称
     */
    private String rxUsedStatusName;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private Date updateTime;
}
