package com.puree.hospital.monitor.datashare.util;

import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.monitor.datashare.enums.HospitalUrlEnum;

/**
 * <AUTHOR>
 * @date 2023/1/17 18:39
 */
public class UrlCheck {
    public static String urlCheck(String hospitalCode) {
        String hospitalUrl;
        if (HospitalUrlEnum.ZZYY.getCode().equals(hospitalCode)) {
            hospitalUrl = HospitalUrlEnum.ZZYY.getInfo();
        } else {
            throw new ServiceException("医院不存在");
        }
        return hospitalUrl;
    }
}
