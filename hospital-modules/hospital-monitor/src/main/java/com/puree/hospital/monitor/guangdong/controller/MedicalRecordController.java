package com.puree.hospital.monitor.guangdong.controller;

import com.puree.hospital.common.core.web.controller.BaseController;
import com.puree.hospital.common.api.domain.AjaxResult;
import com.puree.hospital.monitor.guangdong.domain.MedicalRecord;
import com.puree.hospital.monitor.guangdong.service.IMedicalRecordService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("guangdong/medicalrecord")
public class MedicalRecordController extends BaseController {
    @Resource
    private IMedicalRecordService medicalRecordService;

    /**
     * 诊疗病历
     */
    @GetMapping("list")
    public List<MedicalRecord> list(@RequestParam("hospitalId") Long hospitalId, @RequestParam(defaultValue = "0") String isNewHos) {
        logger.info("诊疗病历：入参{}", hospitalId);
        //先同步
        medicalRecordService.syncData(hospitalId);
        //再拉取
        List<MedicalRecord> list = medicalRecordService.selectList(hospitalId, isNewHos);
        logger.info("诊疗病历：出参{}", list);
        return list;
    }

    /**
     * 同步数据
     */
    @PostMapping("/sync/{id}")
    public AjaxResult sync(@PathVariable Long id) {
        return toAjax(medicalRecordService.syncData(id));
    }
}
