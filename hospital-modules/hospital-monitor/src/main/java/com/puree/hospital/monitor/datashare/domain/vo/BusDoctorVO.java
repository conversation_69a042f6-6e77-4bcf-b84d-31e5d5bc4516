package com.puree.hospital.monitor.datashare.domain.vo;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/1/17 9:59
 */
@Data
public class BusDoctorVO {
    /**
     * 医院编码
     */
    private String hosId;
    /**
     * 科室ID
     */
    private String deptId;
    /**
     * 医生ID
     */
    private String doctorId;
    /**
     * 医生状态
     */
    private String doctorStatus;
    /**
     * 医生姓名
     */
    private String doctorName;
    /**
     * 医生性别（1男 2女）
     */
    private Integer doctorGender;
    /**
     * 医生职称（code）
     */
    private Long title;
    /**
     * 医生职称
     */
    private String doctorTitle;
    /**
     * 医生头像
     */
    private String doctorAvatar;
    /**
     * 医生擅长
     */
    private String doctorGoodAt;
    /**
     * 医生介绍
     */
    private String doctorIntro;
    /**
     * 医生链接地址
     */
    private String doctorUrl;
    /**
     * 医生小程序跳转路径
     */
    private String doctorWxMpPath;
}



