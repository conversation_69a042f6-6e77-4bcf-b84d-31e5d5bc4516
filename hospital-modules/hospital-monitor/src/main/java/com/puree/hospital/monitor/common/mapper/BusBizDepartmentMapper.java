package com.puree.hospital.monitor.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.puree.hospital.monitor.common.domain.BusBizDepartment;
import com.puree.hospital.monitor.datashare.domain.vo.BusBizDepartmentVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 业务科室信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @date 2024/6/25 16:20
 */
@Mapper
public interface BusBizDepartmentMapper extends BaseMapper<BusBizDepartment> {

    /**
     * 查询科室信息
     *
     * @param hospitalCode 医院标识
     * @return 科室列表信息
     */
    List<BusBizDepartmentVO> queryDepartment(String hospitalCode);

    /**
     * 查询科室ID
     *
     * @param doctorId 医生id
     * @return 科室id
     */
    String queryDrDeptId(@Param("doctorId") String doctorId, @Param("hospitalCode") String hospitalCode);

}
