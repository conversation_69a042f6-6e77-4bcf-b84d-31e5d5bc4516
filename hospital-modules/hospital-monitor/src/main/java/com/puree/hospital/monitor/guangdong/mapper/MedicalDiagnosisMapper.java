package com.puree.hospital.monitor.guangdong.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.puree.hospital.monitor.guangdong.domain.MedicalDiagnosis;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface MedicalDiagnosisMapper extends BaseMapper<MedicalDiagnosis> {
    List<MedicalDiagnosis> selectSyncDataList(@Param("hospitalId") Long hospitalId);
}
