package com.puree.hospital.monitor.datashare.controller;

import com.alibaba.fastjson.JSONObject;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.common.core.utils.bean.OrikaUtils;
import com.puree.hospital.common.core.web.controller.BaseController;
import com.puree.hospital.common.api.domain.AjaxResult;
import com.puree.hospital.common.core.web.domain.MapResult;
import com.puree.hospital.monitor.datashare.constant.HospitalSignConstant;
import com.puree.hospital.monitor.datashare.constant.UrlConstant;
import com.puree.hospital.monitor.datashare.domain.vo.BizDepartmentResult;
import com.puree.hospital.monitor.datashare.domain.vo.BusBizDepartmentVO;
import com.puree.hospital.monitor.datashare.service.IBusBizDepartmentService;
import com.puree.hospital.monitor.datashare.util.DesUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Iterator;
import java.util.List;

import static com.puree.hospital.monitor.datashare.util.SignCheck.signCheck;
import static com.puree.hospital.monitor.datashare.util.UrlCheck.urlCheck;

/**
 * <AUTHOR>
 * @date 2023/1/16 15:49
 */
@RestController
@RequestMapping("/biz/dept")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BusBizDepartmentController extends BaseController {
    private final IBusBizDepartmentService busBizDepartmentService;

    /**
     * 查询科室信息
     * @param key 时间戳
     * @param token key（导引平台提供）+“_”+salt（医院端定义服务器端密码）值
     * @return
     */
    @GetMapping("/acquireDepartment")
    public MapResult queryDepartment(String key, String token, String hospitalCode) {
        logger.info("导引平台查询科室入参1={}，入参2={}", key, token);
        MapResult ajaxResult = new MapResult();
        try {
            signCheck(key, token);
            List<BusBizDepartmentVO> bizDepartmentVos = busBizDepartmentService.queryDepartment(hospitalCode);
            List<BizDepartmentResult> resultList = OrikaUtils.converts(bizDepartmentVos, BizDepartmentResult.class);
            Iterator<BizDepartmentResult> iterator = resultList.iterator();
            while (iterator.hasNext()) {
                BizDepartmentResult bz = iterator.next();
                if (StringUtils.isNotEmpty(bz.getParentDeptId()) && StringUtils.isEmpty(bz.getStdDeptId())) {
                    iterator.remove();
                    continue;
                }
                bz.setDeptIntro("");
                bz.setDeptWxMpPath("");
                String deptUrl = urlCheck(hospitalCode);
                bz.setDeptUrl(deptUrl + UrlConstant.DEPARTMENT_URL + "?id=" + bz.getDeptId() + "&label=" + bz.getDeptName());
            }
            String jsonString = JSONObject.toJSONString(resultList);
            logger.info("科室信息={}", jsonString);
            // 使用DES加密
            String data = DesUtils.Encrypt(jsonString, HospitalSignConstant.SECRET_KEY);
            ajaxResult.put("status", 1);
            ajaxResult.put("message", "科室信息查询成功");
            ajaxResult.put("data", data);
        } catch (Exception e) {
            ajaxResult.put("status", 0);
            ajaxResult.put("message", "科室信息查询失败");
            ajaxResult.put("data", e.getMessage());
        }
        return ajaxResult;
    }

}
