package com.puree.hospital.monitor.guangdong.service.impl;

import cn.hutool.core.util.IdcardUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.puree.hospital.common.core.utils.DateUtils;
import com.puree.hospital.common.core.utils.des.DESUtil;
import com.puree.hospital.monitor.config.HospitalJgIdConfig;
import com.puree.hospital.monitor.datashare.service.SysDictDataService;
import com.puree.hospital.monitor.guangdong.domain.Staff;
import com.puree.hospital.monitor.guangdong.infrastructure.Zyjszwdm;
import com.puree.hospital.monitor.guangdong.infrastructure.Zyjszwlbdm;
import com.puree.hospital.monitor.guangdong.mapper.StaffMapper;
import com.puree.hospital.monitor.guangdong.service.IStaffService;
import com.puree.hospital.system.api.model.SysDictData;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class StaffServiceImpl implements IStaffService {
    private final StaffMapper staffMapper;
    private final HospitalJgIdConfig hospitalJgIdConfig;
    private final SysDictDataService sysDictDataService;

    @Override
    public List<Staff> selectList(Long hospitalId,String isNewHos) {
        LambdaQueryWrapper<Staff> queryWrapper;
        if ("0".equals(isNewHos)) {
            queryWrapper = new LambdaQueryWrapper<Staff>()
                    .eq(Staff::getHospitalId, hospitalId)
                    .ge(Staff::getCreateTime, LocalDate.now());
        } else {
            queryWrapper = new LambdaQueryWrapper<Staff>()
                    .eq(Staff::getHospitalId, hospitalId);
        }
        return staffMapper.selectList(queryWrapper);
    }

    /**
     * 传入以逗号分隔的字符串，获取第一个字符串
     * @param url 字符串
     * @return 逗号分隔后的第一个字符串
     */
    private String getFirstImageUrl(String url){
        if (url == null || url.isEmpty()) {
            return url;
        }
        return url.split(",")[0];
    }

    @Override
    public int syncData(Long hospitalId) {
        List<Staff> syncDataList = staffMapper.selectSyncDataList(hospitalId);
        // 查询字典表，结果更新到syncDataList中
        List<String> dictTypes=new ArrayList<>();
        dictTypes.add("bus_doctor_title");
        dictTypes.add("bus_pharmacist_title");
        dictTypes.add("bus_nurse_tile");
        //获取所有的医生职称code->职称名字的map
        Map<Long, String> dictMap = sysDictDataService.getDictMapByTypes(dictTypes, SysDictData::getDictCode, SysDictData::getDictLabel);
        syncDataList.forEach(i -> {
            //将职称code转为对应名字
            i.setZyjszwdm(dictMap.getOrDefault(i.getTitle(),null));
            i.setJgdm(hospitalJgIdConfig.getJgId(hospitalId));
            i.setZyzyyljgdm(hospitalJgIdConfig.getJgId(hospitalId));
            i.setHospitalId(hospitalId);
            i.setCxbz("1");
            // 解密身份证号
            String sfzh = DESUtil.decrypt(i.getSfzh());
            QueryWrapper<Staff> queryWrapper = new QueryWrapper<>();
            queryWrapper.like("sfzh", sfzh);
            queryWrapper.like("hospital_id",hospitalId);
            List<Staff> staffList = staffMapper.selectList(queryWrapper);
            if(staffList.isEmpty()) {
                i.setSfzh(sfzh);
                // 出生日期
                if (sfzh != null) {
                    i.setCsrq(IdcardUtil.getBirthDate(sfzh));
                }
                // 手机号
                i.setSjhm(DESUtil.decrypt(i.getSjhm()));
                // 性别
                i.setXb("0".equals(i.getXb()) ? "2" : "1");
                // 专业技术职务代码
                String zyjszwbm = "";
                // 专业技术职务类别代码
                String zyjszwlbbm = "";
                if (Zyjszwdm.ZY231.getInfo().equals(i.getZyjszwdm())) {
                    zyjszwbm = Zyjszwdm.ZY231.getCode();
                    zyjszwlbbm = Zyjszwlbdm.ZG.getCode();
                } else if (Zyjszwdm.ZY232.getInfo().equals(i.getZyjszwdm())) {
                    zyjszwbm = Zyjszwdm.ZY232.getCode();
                    zyjszwlbbm = Zyjszwlbdm.FG.getCode();
                } else if (Zyjszwdm.ZY233.getInfo().equals(i.getZyjszwdm())) {
                    zyjszwbm = Zyjszwdm.ZY233.getCode();
                    zyjszwlbbm = Zyjszwlbdm.ZJ.getCode();
                } else if (Zyjszwdm.ZY234.getInfo().equals(i.getZyjszwdm())) {
                    zyjszwbm = Zyjszwdm.ZY234.getCode();
                    zyjszwlbbm = Zyjszwlbdm.SJB.getCode();
                }
                i.setZyjszwdm(zyjszwbm);
                i.setZyjszwlbdm(zyjszwlbbm);
                i.setCreateTime(DateUtils.getNowDate());
                i.setZjlbdm("01");
                i.setZgzcfdz(getFirstImageUrl(i.getZgzcfdz()));
                i.setZyzcfdz(getFirstImageUrl(i.getZyzcfdz()));
                staffMapper.insert(i);
            }
        });
        return syncDataList.size();
    }
}
