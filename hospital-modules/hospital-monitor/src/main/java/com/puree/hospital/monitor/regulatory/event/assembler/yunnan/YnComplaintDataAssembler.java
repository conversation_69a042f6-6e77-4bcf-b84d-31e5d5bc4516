package com.puree.hospital.monitor.regulatory.event.assembler.yunnan;

import com.puree.hospital.business.api.RemoteComplaintService;
import com.puree.hospital.business.api.model.BusComplaintVO;
import com.puree.hospital.common.api.constant.SecurityConstants;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.common.core.enums.ComplaintTypeEnum;
import com.puree.hospital.monitor.api.event.constant.RegulatoryAreaConstant;
import com.puree.hospital.monitor.api.event.constant.RegulatoryEventConstant;
import com.puree.hospital.monitor.api.event.regulatory.report.yunnan.YnComplaint;
import com.puree.hospital.monitor.regulatory.event.assembler.AbstractEventDataAssembler;
import com.puree.hospital.monitor.regulatory.event.assembler.EventDataAssembler;
import com.puree.hospital.monitor.regulatory.event.model.RegulatoryHospitalConfig;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <p>
 * 云南监管-投诉建议数据组装
 * </p>
 *
 * <AUTHOR>
 * @date 2024/8/20 15:04
 */
@Component(RegulatoryAreaConstant.YUN_NAN + RegulatoryEventConstant.COMPLAINT + EventDataAssembler.SUFFIX)
public class YnComplaintDataAssembler extends AbstractEventDataAssembler<YnComplaint> {

    @Resource
    private RemoteComplaintService remoteComplaintService;

    @Override
    protected YnComplaint newInstance() {
        return new YnComplaint();
    }

    @Override
    protected void assembleOtherInfo(YnComplaint regulatory, RegulatoryHospitalConfig config) {
        Long complaintId = Long.valueOf(regulatory.getBusinessId());
        R<BusComplaintVO> result = remoteComplaintService.getComplaintInfo(complaintId, SecurityConstants.INNER);
        if (!result.isSuccess()) {
            throw new IllegalStateException(String.format("查询投诉举报信息失败,complaintId：%s", complaintId));
        }
        BusComplaintVO complaintVO = result.getData();
        if (Objects.isNull(complaintVO)) {
            throw new IllegalStateException("查询投诉举报信息为空,complaintId：" + complaintId);
        }
        ComplaintTypeEnum complaintTypeEnum = ComplaintTypeEnum.getByType(complaintVO.getComplaintType());
        if (Objects.isNull(complaintTypeEnum)) {
            throw new IllegalArgumentException("投诉举报类型错误,complaintId：" + complaintId);
        }
        regulatory.setComplaintNo(complaintVO.getComplaintNo());
        if (Objects.equals(complaintTypeEnum, ComplaintTypeEnum.COMPLAINT_HOSPITAL)) {
            regulatory.setComplaintedUnifiedOrgCode(config.getUnifiedCreditCode());
            regulatory.setComplaintedOrgName(config.getHospitalName());
        } else {
            regulatory.setComplaintedPsnName(complaintVO.getComplaintedPsnName());
            regulatory.setComplaintedUnifiedOrgCode(config.getUnifiedCreditCode());
        }
        regulatory.setComplaintDatetime(complaintVO.getComplaintDatetime());
        regulatory.setDemandTypeCode(complaintVO.getDemandTypeCode());
        regulatory.setComplaintTypeCode(complaintVO.getComplaintTypeCode());
        regulatory.setComplaintTitle(complaintVO.getComplaintTitle());
        regulatory.setComplaintText(complaintVO.getComplaintText());
        regulatory.setTelNum(complaintVO.getTelNum());
    }

    @Override
    protected String getEventType() {
        return RegulatoryEventConstant.COMPLAINT;
    }
}
