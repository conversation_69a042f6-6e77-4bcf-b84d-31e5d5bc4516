package com.puree.hospital.monitor.regulatory.event.assembler.yunnan;

import com.puree.hospital.monitor.api.event.constant.RegulatoryAreaConstant;
import com.puree.hospital.monitor.api.event.constant.RegulatoryEventConstant;
import com.puree.hospital.monitor.api.event.regulatory.report.yunnan.YnDepartment;
import com.puree.hospital.monitor.regulatory.event.assembler.EventDataAssembler;
import org.springframework.stereotype.Component;

/**
 * <p>
 * 云南科室信息组装
 * </p>
 *
 * <AUTHOR>
 * @date 2024/6/25 14:24
 */
@Component(RegulatoryAreaConstant.YUN_NAN + RegulatoryEventConstant.DEPARTMENT_FILING + EventDataAssembler.SUFFIX)
public class YnDepartmentEventDataAssembler extends YnBaseDepartmentEventDataAssembler<YnDepartment> {

    @Override
    protected YnDepartment newInstance() {
        return new YnDepartment();
    }

    @Override
    protected String getEventType() {
        return RegulatoryEventConstant.DEPARTMENT_FILING;
    }

}
