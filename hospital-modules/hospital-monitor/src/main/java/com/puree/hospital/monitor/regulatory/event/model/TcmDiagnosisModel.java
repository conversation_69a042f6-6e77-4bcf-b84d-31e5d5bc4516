package com.puree.hospital.monitor.regulatory.event.model;

import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 诊断信息
 * </p>
 *
 * <AUTHOR>
 * @date 2024/7/5 19:39
 */
@Data
public class TcmDiagnosisModel implements Serializable {

    private static final long serialVersionUID = 1881248732016734154L;

    /**
     * id
     */
    private Long id;

    /**
     * 是否确认
     */
    private boolean check;

    /**
     * 拼音编码
     */
    private String pinYinCode;

    /**
     * 中医症状
     */
    private TcmSyndrome tcmSyndrome;

    /**
     * 中医诊断
     */
    private String tcmDiagnosis;

    /**
     * 中医诊断编码
     */
    private String diagnosisCode;


    @Data
    public static class TcmSyndrome implements Serializable {

        private static final long serialVersionUID = 6408468672916478166L;

        /**
         * id
         */
        private Long id;

        /**
         * 是否确认
         */
        private boolean check;

        /**
         * 中医症状
         */
        private String tcmSyndrome;

        /**
         * 中医症状编码
         */
        private String diagnosisCode;

    }
}
