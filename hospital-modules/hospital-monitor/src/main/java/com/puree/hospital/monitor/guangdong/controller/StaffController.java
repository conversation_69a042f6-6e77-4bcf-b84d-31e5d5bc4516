package com.puree.hospital.monitor.guangdong.controller;

import com.puree.hospital.common.core.web.controller.BaseController;
import com.puree.hospital.monitor.guangdong.domain.Staff;
import com.puree.hospital.monitor.guangdong.service.IStaffService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("guangdong/staff")
public class StaffController extends BaseController {

    @Resource
    private IStaffService staffService;

    /**
     * 员工记录表
     */
    @GetMapping("list")
    public List<Staff> list(@RequestParam("hospitalId") Long hospitalId, @RequestParam(defaultValue = "0") String isNewHos) {
        logger.info("员工记录表：入参{}", hospitalId);
        staffService.syncData(hospitalId);
        List<Staff> list = staffService.selectList(hospitalId, isNewHos);
        logger.info("员工记录表：出参{}", list);
        return list;
    }
}
