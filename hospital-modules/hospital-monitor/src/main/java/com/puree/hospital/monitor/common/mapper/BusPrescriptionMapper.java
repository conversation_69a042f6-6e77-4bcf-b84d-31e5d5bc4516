package com.puree.hospital.monitor.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.puree.hospital.monitor.common.domain.BusPrescription;
import com.puree.hospital.monitor.common.domain.BusPrescriptionQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 处方表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @date 2024/6/26 10:54
 */
@Mapper
public interface BusPrescriptionMapper extends BaseMapper<BusPrescription> {
    /**
     * 根据咨询订单id查询处方信息
     * @param busPrescriptionQuery -- 包含hospitalId, recordNo, familyId, doctorId
     * @return - 处方信息列表
     */
     List<BusPrescription> getPrescriptionByRecordNo(@Param("prescription") BusPrescriptionQuery busPrescriptionQuery);
}
