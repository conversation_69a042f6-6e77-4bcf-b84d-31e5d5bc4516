package com.puree.hospital.monitor.datashare.util;

import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.sign.MD5Util;
import com.puree.hospital.monitor.datashare.constant.HospitalSignConstant;

/**
 * <AUTHOR>
 * @date 2023/1/16 19:46
 */
public class SignCheck {
    /**
     * 校验签名
     * @param key
     * @param token
     */
    public static void signCheck(String key, String token) {
        String verificationToken = key + "_" + HospitalSignConstant.SALT;
        String s = MD5Util.md5Encrypt32Lower(verificationToken);
        // 校验token
        if (!token.equals(s)) {
            throw new ServiceException("token校验失败");
        }
        // 前端的时间戳与服务器当前时间戳相差如果大于180，判定当前请求的timestamp无效
        if (Math.abs((Long.parseLong(key) - System.currentTimeMillis()) / 1000) > 180) {
            throw new ServiceException("timestamp超时");
        }
    }
}
