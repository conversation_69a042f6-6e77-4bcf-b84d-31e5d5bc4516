package com.puree.hospital.monitor.guangdong.controller;

import com.puree.hospital.common.core.web.controller.BaseController;
import com.puree.hospital.monitor.guangdong.domain.Institution;
import com.puree.hospital.monitor.guangdong.service.IInstitutionService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("guangdong/institution")
public class InstitutionController extends BaseController {
    @Resource
    private IInstitutionService institutionService;

    /**
     * 机构信息表
     */
    @GetMapping("list")
    public List<Institution> list(@RequestParam("hospitalId") Long hospitalId, @RequestParam(defaultValue = "0") String isNewHos) {
        logger.info("机构信息表：入参{}", hospitalId);
        List<Institution> list = institutionService.selectList(hospitalId, isNewHos);
        logger.info("机构信息表：出参{}", list);
        return list;
    }
}
