package com.puree.hospital.monitor.regulatory.event.assembler.yunnan;

import com.puree.hospital.business.api.RemoteAdverseEventService;
import com.puree.hospital.business.api.model.BusAdverseEventVO;
import com.puree.hospital.common.api.constant.SecurityConstants;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.monitor.api.event.constant.RegulatoryAreaConstant;
import com.puree.hospital.monitor.api.event.constant.RegulatoryEventConstant;
import com.puree.hospital.monitor.api.event.regulatory.report.yunnan.YnAdverseEvent;
import com.puree.hospital.monitor.regulatory.event.assembler.AbstractEventDataAssembler;
import com.puree.hospital.monitor.regulatory.event.assembler.EventDataAssembler;
import com.puree.hospital.monitor.regulatory.event.model.RegulatoryHospitalConfig;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <p>
 * 云南省监管-不良事件
 * </p>
 *
 * <AUTHOR>
 * @date 2024/8/20 15:11
 */
@Component(RegulatoryAreaConstant.YUN_NAN + RegulatoryEventConstant.ADVERSE_EVENT + EventDataAssembler.SUFFIX)
public class YnAdverseEventDataAssembler extends AbstractEventDataAssembler<YnAdverseEvent> {

    @Resource
    private RemoteAdverseEventService remoteAdverseEventService;

    @Override
    protected YnAdverseEvent newInstance() {
        return new YnAdverseEvent();
    }

    @Override
    protected void assembleOtherInfo(YnAdverseEvent regulatory, RegulatoryHospitalConfig config) {
        Long eventId = Long.valueOf(regulatory.getBusinessId());
        R<BusAdverseEventVO> result = remoteAdverseEventService.getAdverseEventInfo(eventId, SecurityConstants.INNER);
        if (!result.isSuccess()) {
            throw new IllegalStateException(String.format("查询不良事件信息失败,eventId：%s", eventId));
        }
        BusAdverseEventVO adverseEventVO = result.getData();
        if (Objects.isNull(adverseEventVO)) {
            throw new IllegalStateException("查询不良事件信息为空,eventId：" + eventId);
        }
        regulatory.setEventCode(adverseEventVO.getEventCode());
        regulatory.setOccurDatetime(adverseEventVO.getOccurDatetime());
        regulatory.setTitle(adverseEventVO.getTitle());
        regulatory.setDescription(adverseEventVO.getDescription());
    }

    @Override
    protected String getEventType() {
        return RegulatoryEventConstant.ADVERSE_EVENT;
    }
}
