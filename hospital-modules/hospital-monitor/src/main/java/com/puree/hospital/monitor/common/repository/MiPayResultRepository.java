package com.puree.hospital.monitor.common.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.puree.hospital.monitor.common.domain.MiPayResult;
import com.puree.hospital.monitor.common.mapper.MiPayResultMapper;
import org.springframework.stereotype.Repository;

/**
 * <p>
 * 医保支付结果 Repository
 * </p>
 *
 * <AUTHOR>
 * @date 2024/7/3 17:46
 */
@Repository
public class MiPayResultRepository extends ServiceImpl<MiPayResultMapper, MiPayResult> {

    /**
     * 根据订单号查询 医保支付结果
     *
     * @param orderNo 订单号
     * @return 医保支付结果
     */
    public MiPayResult getByOrderNo(String orderNo) {
        LambdaQueryWrapper<MiPayResult> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MiPayResult::getOrderNo, orderNo);
        return this.getOne(queryWrapper, false);
    }
}
