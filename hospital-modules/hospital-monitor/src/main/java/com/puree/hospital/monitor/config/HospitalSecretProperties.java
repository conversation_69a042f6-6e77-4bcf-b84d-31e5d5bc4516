package com.puree.hospital.monitor.config;


import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Data
@RefreshScope
@Component
@ConfigurationProperties(prefix = "secret")
public class HospitalSecretProperties {

    /**
     * 医院密钥配置
     */
    public List<Map<String, String>> hospital;

    /**
     * 限制匹配访问的时间--默认12小时
     */
    @Value("${secret.limitTime:43200000}")
    public Long limitTime = 43200000L;


    @Value("${aliyun.oss.prodFileAddressPrefix:https://aidmed.oss-cn-shenzhen.aliyuncs.com/}")
    public String ossUrl;

    @Value("${aliyun.oss.testFileAddressPrefix:https://puree-test.oss-cn-shenzhen.aliyuncs.com/}")
    public String ossUrlTest;

    @Data
    public static class Hospital{
        @Setter
        private String hospitalName;
        @Setter
        private String secret;
        @Setter
        private String id;

        public Hospital(String hospitalName, String secret, String id) {
            this.hospitalName = hospitalName;
            this.secret = secret;
            this.id = id;
        }
    }
}

