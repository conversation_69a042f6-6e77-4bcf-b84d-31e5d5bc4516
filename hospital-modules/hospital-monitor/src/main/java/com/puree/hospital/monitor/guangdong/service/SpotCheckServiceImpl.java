package com.puree.hospital.monitor.guangdong.service;

import com.alibaba.fastjson.JSONObject;
import com.puree.hospital.common.core.utils.SHA256Util;
import com.puree.hospital.monitor.common.domain.BusCommunicationMessage;
import com.puree.hospital.monitor.common.domain.BusPrescription;
import com.puree.hospital.monitor.common.repository.BusCommunicationMessageRepository;
import com.puree.hospital.monitor.common.repository.BusDoctorPatientGroupRepository;
import com.puree.hospital.monitor.common.repository.BusPrescriptionRepository;
import com.puree.hospital.monitor.common.repository.MedicalPrescriptionRepository;
import com.puree.hospital.monitor.config.HospitalJgIdConfig;
import com.puree.hospital.monitor.config.HospitalSecretProperties;
import com.puree.hospital.monitor.guangdong.domain.MedicalPrescription;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 广东监管正式接入抽查接口

 * <AUTHOR>
 */
@Service
public class SpotCheckServiceImpl implements SpotCheckService {
    private static final Logger log = LoggerFactory.getLogger(SpotCheckServiceImpl.class);
    /**
     * 视频类型
     */
    public static final String VIDEOCHAT_FILES = "VIDEOCHAT_FILES";
    /**
     * 密钥
     */
    @Autowired
    private HospitalJgIdConfig hospitalSecretProperties;
    @Autowired
    private BusPrescriptionRepository busPrescriptionRepository;
    @Autowired
    private BusDoctorPatientGroupRepository busDoctorPatientGroupRepository;
    @Autowired
    private BusCommunicationMessageRepository busCommunicationMessageRepository;
    @Autowired
    private MedicalPrescriptionRepository medicalPrescriptionRepository;

    /**
     * 检查token是否合法
     * @param token - 校验参数
     * @param time - 时间戳
     * @return - 是否合法
     */
    @Override
    public boolean checkToken(String token, String time) {
        if (System.currentTimeMillis() - Long.parseLong(time) * 1000L > hospitalSecretProperties.getLimitTime()){
            // 超时-限制访问
            return false;
        }
        // 拿密钥-重组-比对token

        List<HospitalSecretProperties.Hospital> hospitalSecret = hospitalSecretProperties.getSecret();
        if (hospitalSecret == null){
            return false;
        }
        for (HospitalSecretProperties.Hospital secret : hospitalSecret) {
            String secretHospital = secret.getSecret();
            // 没有64位的字符串直接下一个
            if (secretHospital.length() != 64){
                continue;
            }
            //重组token---算法生成（三次SHA256）
            String tokenNew = getTokenNew(time, secretHospital);
            //比对token
            if (token.equals(tokenNew)){
                return true;
            }
        }
        return false;
    }

    /**
     * 将传入的secret进行处理，生成新的token
     * @param time - 时间戳
     * secretHospital - 医院密钥
     * @return - 生成的新的token
     */
    private String getTokenNew(String time, String secretHospital) {

        //将长度为 64 的私钥分为两个字符串，第一个字符串为 1-31 位，第二个字符
        //串为 32-64 位。与 time 如图例拼接得到新的字符串
        String secretFirst = secretHospital.substring(0, 31);
        String secretLast = secretHospital.substring(31);
        String newSecret = secretFirst + time + secretLast;
        log.debug("重组前的secret为：{}", newSecret);
        //将字符串的奇数位和偶数位分为两组，奇数位在前，偶数位在后，组成新的字符串
        StringBuilder odd = new StringBuilder();
        StringBuilder even = new StringBuilder();
        for (int i = 0; i < newSecret.length(); i++) {
            if (i % 2 == 0){
                odd.append(newSecret.charAt(i));
            }else {
                even.append(newSecret.charAt(i));
            }
        }
        newSecret = odd.append(even).toString();
        log.debug("重组的token为：{}", newSecret);
        // 进行3次SHA256哈希运算，得到token
        String tokenNew = "";
        try {
            // 进行3次SHA256哈希运算
            tokenNew = getaNew(newSecret,3);
            log.debug("生成新的token为：{}", tokenNew);
        } catch (Exception e) {
            log.warn("生成token失败！,生成前的字符串为：{}", newSecret);
        }
        return tokenNew;
    }

    /**
     * 进行i次SHA256哈希运算--递归
     * @param newSecret - 待哈希的字符串
     * @param i - 递归次数
     * @return - 哈希后的字符串
     */
    private static String getaNew(String newSecret,int i) {
        if (i == 0){
            return newSecret;
        }
        String tokenNew;
        tokenNew = SHA256Util.toHexString(SHA256Util.getSHA(newSecret));
        return getaNew(tokenNew,i-1);
    }

    /**
     * 获取处方pdf地址
     * @param recordNo - 病历号
     * @return - 处方pdf地址
     */
    @Override
    public List<String> getPrescriptionPdfUrl(String recordNo,String environment) {
        String url = getUrlHead(environment);
        if (url == null) {
            return null;
        }
        // 从数据库中查询处方pdf地址
        List<BusPrescription> prescriptions = getBusPrescriptions(recordNo);
        if (prescriptions == null) {
            return null;
        }
        // 设置处方pdf地址
        return prescriptions.stream()
                .filter(i -> i.getPath() != null)
                .map(i -> (url + i.getPath()))
                .collect(Collectors.toList());
    }

    /**
     * 根据病历号查询处方信息
     * @param recordNo - 病历号
     * @return - 处方信息
     */
    private List<BusPrescription> getBusPrescriptions(String recordNo) {
        // 根据病历号查询处方信息
        List<MedicalPrescription> prescription = medicalPrescriptionRepository.findByRecordNo(recordNo);
        if (prescription == null){
            return null;
        }
        List<String> prescriptionNumberList = prescription.stream().map(MedicalPrescription::getCfbh).collect(Collectors.toList());
        // 根据处方查询对应的视频地址
        List<BusPrescription> prescriptions = busPrescriptionRepository.getByRecord(prescriptionNumberList);
        if (prescriptions == null || prescriptions.isEmpty()) {
            return null;
        }
        return prescriptions;
    }

    /**
     * 获取url地址的前缀
     * @param environment - 环境
     * @return -    url地址的前缀
     */
    private String getUrlHead(String environment) {
        if ("prod".equals(environment)){
            // 生产环境-配置
            return hospitalSecretProperties.getOssUrl();
        }
        if ("test".equals(environment)){
            // 测试环境-配置
            return hospitalSecretProperties.getOssUrlTest();
        }
        log.error("处方pdf地址对应OSS url未配置！");
        return null;
    }

    /**
     * 获取视频地址
     * @param recordNo - 门诊号
     * @return - 视频地址
     */
    @Override
    public String getVideoUrl(String recordNo,String environment) {
        String url = getUrlHead(environment);
        if (url == null) {
            return null;
        }
        List<BusPrescription> prescriptions = getBusPrescriptions(recordNo);
        // 从数据库中查询处方
        if (prescriptions == null || prescriptions.isEmpty()){
            return null;
        }
        Set<Long> groupIds = new HashSet<>();
        // 设置视频地址
        for (BusPrescription prescription : prescriptions) {
            if (prescription.getDoctorId() == null || prescription.getPatientId() == null
            || prescription.getFamilyId() == null || prescription.getDepartmentId() == null
                    || prescription.getHospitalId() == null){
                continue;
            }
            // 查询对应的groupId
            Set<Long> groupIdList = busDoctorPatientGroupRepository.getGroupIds(
                    prescription.getDoctorId(), prescription.getPatientId(),
                    prescription.getFamilyId(), prescription.getDepartmentId(),
                    prescription.getHospitalId());
            if (groupIdList != null && !groupIdList.isEmpty()){
                groupIds.addAll(groupIdList);
            }
        }
        // 根据groupId查询视频地址-
        // 1.先获取所有的对应的聊天记录
        List<BusCommunicationMessage> payloadByGroupIds = busCommunicationMessageRepository.getPayloadByGroupIds(groupIds);
        if (payloadByGroupIds == null || payloadByGroupIds.isEmpty()){
            return null;
        }
        // 2.根据聊天记录获取视频地址---解析聊天记录
        //示例 -{"data":{"duration":43,"type":"VIDEOCHAT_FILES","url":"im/d8bca576b7514881b15bbfcb213f27f4.mp4"}}
        for (BusCommunicationMessage message : payloadByGroupIds) {
            // 判断是聊天记录是否包含视频地址
            if (message.getPayload() == null ){
                continue;
            }
            String payload = message.getPayload();
            // String转json
            JSONObject jsonObject = JSONObject.parseObject(payload);
            String data = jsonObject.getString("data");
            if (data == null || data.isEmpty()){
                continue;
            }
            JSONObject dataJson = JSONObject.parseObject(data);
            // 类型为视频类型
            String type = dataJson.getString("type");
            if (type == null || type.isEmpty()){
                continue;
            }
            String videoUrl = null;
            if (VIDEOCHAT_FILES.equals(type)){
                videoUrl = dataJson.getString("url");
            }
            // 获取对应的视频地址
            if (videoUrl != null && !videoUrl.isEmpty()){
                return  url + videoUrl;
            }
        }
        // 3.返回视频地址
        return null;
    }
}
