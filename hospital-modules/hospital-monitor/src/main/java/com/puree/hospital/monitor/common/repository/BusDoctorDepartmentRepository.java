package com.puree.hospital.monitor.common.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.puree.hospital.monitor.common.domain.BusDoctorDepartment;
import com.puree.hospital.monitor.common.mapper.BusDoctorDepartmentMapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>
 * 医生科室关联信息 Repository
 * </p>
 *
 * <AUTHOR>
 * @date 2024/6/26 08:40
 */
@Repository
public class BusDoctorDepartmentRepository extends ServiceImpl<BusDoctorDepartmentMapper, BusDoctorDepartment> {

    /**
     * 查询医生科室关联信息
     *
     * @param busDoctorDepartment 查询参数
     * @return 医生科室关联信息集合
     */
    public List<BusDoctorDepartment> selectList(BusDoctorDepartment busDoctorDepartment) {
        return baseMapper.selectList(busDoctorDepartment);
    }
}
