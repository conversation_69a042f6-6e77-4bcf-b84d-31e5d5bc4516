package com.puree.hospital.monitor.guangdong.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName("gd_t_ins_institution")
public class Institution {
    /** 部署平台 */
    private String bspt;
    /** 撤销标志 */
    private String cxbz;
    /** 单位隶属关系代码 */
    private String dwlsgxdm;
    /** 地址 */
    private String dz;
    /** 法人代表/负责人 */
    private String frdb;
    /** 互联网医院网址 */
    private String hlwyywz;
    /** 机构成立日期 */
    private Date jgclrq;
    /** 机构标识 */
    private String jgdm;
    /** 机构分类代码 */
    private String jgfldm;
    /** 机构分类管理类别代码 */
    private String jgflgllbdm;
    /** 机构类型 */
    private String jglx;
    /** 机构名称 */
    private String jgmc;
    /** 机构所在地民族自治地方标志 */
    private String jgszdmzzzdfbz;
    /** 经济类型代码 */
    private String jjlxdm;
    /** 开办资金金额数 */
    private BigDecimal kbzjjes;
    /** 是否分支机构 */
    private String sffzjg;
    /** 是否具备双路供电或紧急发电设施 */
    private String sfslgd;
    /** 数据生成日期时 */
    private Date sjscsj;
    /** 实体医院机构等级 */
    private String styljgdj;
    /** 实体医疗机构级别 */
    private String styljgjb;
    /** 实体医院名称 */
    private String styymc;
    /** 实体医院医疗组织机构代码 */
    private String styyzzjgdm;
    /** 许可项目名称 */
    private String xkxmmc;
    /** 许可证号码 */
    private String xkzhm;
    /** 许可证有效期 */
    private Date xkzyxq;
    /** 信息安全等级保护 */
    private String xxaqdjbh;
    /** 行政区划代码 */
    private String xzqhdm;
    /** 统一社会信用代码 */
    private String zzjgdm;
    /** 创建时间（辅助作用） */
    private Date createTime;
    private Long hospitalId;
}
