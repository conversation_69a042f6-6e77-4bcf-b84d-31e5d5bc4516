package com.puree.hospital.monitor.regulatory.event.assembler.yunnan;

import cn.hutool.core.date.DateUtil;
import com.puree.hospital.common.core.utils.des.DESUtil;
import com.puree.hospital.monitor.api.event.constant.RegulatoryAreaConstant;
import com.puree.hospital.monitor.api.event.constant.RegulatoryEventConstant;
import com.puree.hospital.monitor.api.event.regulatory.report.yunnan.YnHospital;
import com.puree.hospital.monitor.common.domain.BusHospital;
import com.puree.hospital.monitor.common.repository.BusHospitalRepository;
import com.puree.hospital.monitor.regulatory.event.assembler.AbstractEventDataAssembler;
import com.puree.hospital.monitor.regulatory.event.assembler.EventDataAssembler;
import com.puree.hospital.monitor.regulatory.event.model.RegulatoryHospitalConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <p>
 * 云南医院数据组装
 * </p>
 *
 * <AUTHOR>
 * @date 2024/6/25 10:16
 */
@Slf4j
@Component(RegulatoryAreaConstant.YUN_NAN + RegulatoryEventConstant.HOSPITAL_FILING + EventDataAssembler.SUFFIX)
public class YnHospitalEventDataAssembler extends AbstractEventDataAssembler<YnHospital> {

    @Resource
    private BusHospitalRepository hospitalRepository;

    @Override
    protected YnHospital newInstance() {
        return new YnHospital();
    }

    @Override
    protected void assembleOtherInfo(YnHospital regulatory, RegulatoryHospitalConfig config) {
        regulatory.setHosTypeCode(config.getHosTypeCode());
        regulatory.setHosClassCode(config.getHosClassCode());
        regulatory.setHosDegreeCode(config.getHosDegreeCode());
        regulatory.setOpenDatetime(DateUtil.format(config.getOpenDatetime(), "yyyyMMdd"));
        regulatory.setWebsiteUrl(config.getWebsiteUrl());
        regulatory.setLegalRepresentative(config.getLegalRepresentative());
        regulatory.setPrincipal(config.getPrincipal());
        //目前数据库没有冗余存储省 + 市 + 区/县数据， 暂时先走配置数据
        regulatory.setAddress(config.getAddress());
        BusHospital hospital = hospitalRepository.getById(regulatory.getHospitalId());
        if (Objects.isNull(hospital)) {
            throw new IllegalStateException(String.format("医院信息不存在,医院id：%s", regulatory.getHospitalId()));
        }
        //组装DB对象数据医院数据， 重构需要走api接口
        regulatory.setHospitalName(hospital.getHospitalName());
        regulatory.setHospitalAbbreviation(hospital.getHospitalAbbreviation());
        regulatory.setHospitalPhone(DESUtil.decrypt(hospital.getHospitalPhone()));
        regulatory.setProvinceCode(hospital.getProvinceCode());
        regulatory.setCityCode(hospital.getCityCode());
        regulatory.setAreaCode(hospital.getAreaCode());
        regulatory.setDetailAddress(hospital.getDetailAddress());
        regulatory.setAddressCoordinate(hospital.getAddressCoordinate());
        regulatory.setHospitalDescription(hospital.getHospitalDescription());
        regulatory.setIfAccepted(getIfAccepted(hospital.getStatus()));
    }

    @Override
    protected String getEventType() {
        return RegulatoryEventConstant.HOSPITAL_FILING;
    }

    /**
     * 可能会出现禁用操作
     *
     * @param status 医院状态信息
     * @return 0-禁用 1-启用
     */
    private boolean getIfAccepted(Integer status) {
        return Objects.equals(1, status);
    }

}
