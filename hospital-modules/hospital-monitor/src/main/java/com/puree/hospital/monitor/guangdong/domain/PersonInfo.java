package com.puree.hospital.monitor.guangdong.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("gd_t_pi_person")
public class PersonInfo {
    /** 机构标识 */
    private String jgdm;
    /** 卡号 */
    private String kh;
    /** 卡类型 */
    private String klx;
    /** 省份证件号码 */
    private String zjhm;
    /** 省份证件类别代码 */
    private String zjlbdm;
    /** 姓名 */
    private String xm;
    /** 性别代码 */
    private String xbdm;
    /** 性别名称 */
    private String xbmc;
    /** 出生日期 */
    private String csrq;
    /** 手机号码 */
    private String sjhm;
    /** 数据生成时间 */
    private Date sjscsj;
    /** 撤销标志 */
    private String cxbz;
    private Long hospitalId;
    @TableField(exist = false)
    private Long familyId;
    private Date createTime;
}
