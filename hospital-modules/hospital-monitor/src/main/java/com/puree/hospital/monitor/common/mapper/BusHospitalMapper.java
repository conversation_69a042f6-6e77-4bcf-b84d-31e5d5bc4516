package com.puree.hospital.monitor.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.puree.hospital.monitor.common.domain.BusHospital;
import com.puree.hospital.monitor.datashare.domain.vo.BusHospitalVO;
import org.apache.ibatis.annotations.Mapper;

/**
 * <p>
 * 医院 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @date 2024/6/25 15:30
 */
@Mapper
public interface BusHospitalMapper extends BaseMapper<BusHospital> {

    /**
     * 查询医院信息
     * @param hospitalCode 医院标识
     * @return
     */
    BusHospitalVO queryHospital(String hospitalCode);

}
