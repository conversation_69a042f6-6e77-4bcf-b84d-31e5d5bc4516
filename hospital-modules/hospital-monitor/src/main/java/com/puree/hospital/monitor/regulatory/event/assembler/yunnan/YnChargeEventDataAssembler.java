package com.puree.hospital.monitor.regulatory.event.assembler.yunnan;

import com.puree.hospital.monitor.api.event.constant.RegulatoryAreaConstant;
import com.puree.hospital.monitor.api.event.constant.RegulatoryEventConstant;
import com.puree.hospital.monitor.api.event.regulatory.report.yunnan.YnChargeOrRefund;
import com.puree.hospital.monitor.common.domain.BusDrugsOrder;
import com.puree.hospital.monitor.regulatory.event.assembler.EventDataAssembler;
import com.puree.hospital.pay.api.model.BusPayOrder;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <p>
 * 云南收费数据上报组装
 * </p>
 *
 * <AUTHOR>
 * @date 2024/6/25 14:51
 */
@Component(RegulatoryAreaConstant.YUN_NAN + RegulatoryEventConstant.CHARGE + EventDataAssembler.SUFFIX)
public class YnChargeEventDataAssembler extends YnBaseChargeOrRefundEventDataAssembler {

    @Override
    protected String getEventType() {
        return RegulatoryEventConstant.CHARGE;
    }

    @Override
    protected String getChargeRefundCode() {
        return "1";
    }

    @Override
    protected String getAccountNo(BusPayOrder busPayOrder, YnChargeOrRefund regulatory) {
        return busPayOrder.getOutTradeNo();
    }

    @Override
    protected String getOutTradeNo(BusPayOrder busPayOrder, YnChargeOrRefund regulatory) {
        return busPayOrder.getTransactionId();
    }

    @Override
    protected String getOriginalAccountNo(BusPayOrder busPayOrder) {
        return null;
    }

    @Override
    protected List<BusDrugsOrder> getDrugsOrderList(List<Long> subOrderIds) {
        return busDrugsOrderRepository.listByIds(subOrderIds);
    }
}
