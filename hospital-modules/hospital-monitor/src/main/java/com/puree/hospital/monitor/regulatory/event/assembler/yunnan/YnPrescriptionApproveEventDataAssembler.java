package com.puree.hospital.monitor.regulatory.event.assembler.yunnan;

import com.puree.hospital.common.core.utils.des.DESUtil;
import com.puree.hospital.monitor.api.event.constant.RegulatoryAreaConstant;
import com.puree.hospital.monitor.api.event.constant.RegulatoryEventConstant;
import com.puree.hospital.monitor.api.event.regulatory.report.yunnan.YnPrescriptionApprove;
import com.puree.hospital.monitor.common.domain.BusConsultationOrder;
import com.puree.hospital.monitor.common.domain.BusDoctor;
import com.puree.hospital.monitor.common.domain.BusPrescription;
import com.puree.hospital.monitor.common.domain.BusSignature;
import com.puree.hospital.monitor.common.repository.BusDoctorRepository;
import com.puree.hospital.monitor.common.repository.BusPrescriptionRepository;
import com.puree.hospital.monitor.common.repository.BusSignatureRepository;
import com.puree.hospital.monitor.regulatory.event.assembler.AbstractEventDataAssembler;
import com.puree.hospital.monitor.regulatory.event.assembler.EventDataAssembler;
import com.puree.hospital.monitor.regulatory.event.model.RegulatoryHospitalConfig;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <p>
 * 云南处方审核事件数据组装
 * </p>
 *
 * <AUTHOR>
 * @date 2024/6/25 14:50
 */
@Component(RegulatoryAreaConstant.YUN_NAN + RegulatoryEventConstant.PRESCRIPTION_APPROVE + EventDataAssembler.SUFFIX)
public class YnPrescriptionApproveEventDataAssembler extends AbstractEventDataAssembler<YnPrescriptionApprove> {

    @Resource
    private BusPrescriptionRepository busPrescriptionRepository;

    @Resource
    private BusDoctorRepository busDoctorRepository;

    @Resource
    private BusSignatureRepository busSignatureRepository;

    @Override
    protected YnPrescriptionApprove newInstance() {
        return new YnPrescriptionApprove();
    }

    @Override
    protected void assembleOtherInfo(YnPrescriptionApprove regulatory, RegulatoryHospitalConfig config) {
        BusPrescription busPrescription = busPrescriptionRepository.getById(regulatory.getBusinessId());
        if (Objects.isNull(busPrescription)) {
            throw new IllegalStateException(String.format("处方信息不存在,处方id：%s", regulatory.getBusinessId()));
        }
        BusConsultationOrder consultationRecord = super.getConsultationRecord(busPrescription);
        if (Objects.isNull(consultationRecord)) {
            throw new IllegalArgumentException(String.format("处方关联的问诊记录信息不存在,处方id：%s", regulatory.getBusinessId()));
        }
        regulatory.setPrescriptionNumber(busPrescription.getPrescriptionNumber());
        regulatory.setReviewPharmacistName(busPrescription.getReviewPharmacistName());
        BusDoctor pharmacist = busDoctorRepository.getById(busPrescription.getReviewPharmacistId());
        if (Objects.isNull(pharmacist)) {
            throw new IllegalStateException(String.format("处方审核药师信息不存在,处方id：%s", regulatory.getBusinessId()));
        }
        //从bus_doctor 中获取
        regulatory.setReviewPharmacistIdCardNo(DESUtil.decrypt(pharmacist.getIdCardNo()));
        //获取审方药师签名
        BusSignature signature = busSignatureRepository.getByObjectIdAndObjectType(busPrescription.getReviewPharmacistId(), "1");
        if (Objects.nonNull(signature)) {
            String reviewPharmacistSignUrl = signature.getCertSignature();
            //从 bus_signature 中获取 还需要拼接 前半段域名
            regulatory.setReviewPharmacistSignUrl(getFileAddressPrefix() + reviewPharmacistSignUrl);
        }
        regulatory.setReviewPharmacistCaSign(getDoctorCaSign());
        regulatory.setReviewTime(Objects.nonNull(busPrescription.getReviewTime()) ? busPrescription.getReviewTime() : regulatory.getEventTime());
        regulatory.setStatus(busPrescription.getStatus());
        regulatory.setNotApprovedReason(busPrescription.getNotApprovedReason());
    }

    @Override
    protected String getEventType() {
        return RegulatoryEventConstant.PRESCRIPTION_APPROVE;
    }
}
