package com.puree.hospital.monitor.regulatory.event.model;

import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 西药处方临床诊断
 * </p>
 *
 * <AUTHOR>
 * @date 2024/7/8 16:08
 */
@Data
public class WmDiagnosisModel implements Serializable {

    private static final long serialVersionUID = -2636708270043715099L;
    /**
     * id
     */
    private Integer id;

    /**
     * common
     */
    private String common;

    /**
     * status
     */
    private String status;

    /**
     * 诊断结果编码
     */
    private String icdCode;

    /**
     * 拼音编码
     */
    private String pinYinCode;

    /**
     * 疾病名称
     */
    private String diseaseName;

    /**
     * 疾病编号
     */
    private String diseaseNumber;

}
