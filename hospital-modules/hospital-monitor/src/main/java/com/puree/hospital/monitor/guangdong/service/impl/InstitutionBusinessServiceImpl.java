package com.puree.hospital.monitor.guangdong.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.puree.hospital.monitor.guangdong.domain.Contacts;
import com.puree.hospital.monitor.guangdong.domain.InstitutionBusiness;
import com.puree.hospital.monitor.guangdong.mapper.InstitutionBusinessMapper;
import com.puree.hospital.monitor.guangdong.service.IInstitutionBusinessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;

@Service
public class InstitutionBusinessServiceImpl implements IInstitutionBusinessService {
    private final InstitutionBusinessMapper institutionBusinessMapper;

    @Autowired
    public InstitutionBusinessServiceImpl(InstitutionBusinessMapper institutionBusinessMapper) {
        this.institutionBusinessMapper = institutionBusinessMapper;
    }

    @Override
    public List<InstitutionBusiness> selectList(Long hospitalId,String isNewHos) {
        LambdaQueryWrapper<InstitutionBusiness> queryWrapper;
        if("0".equals(isNewHos)){
            queryWrapper = new LambdaQueryWrapper<InstitutionBusiness>()
                    .eq(InstitutionBusiness::getHospitalId, hospitalId)
                    .ge(InstitutionBusiness::getCreateTime, LocalDate.now());
        }else{
            queryWrapper = new LambdaQueryWrapper<InstitutionBusiness>()
                    .eq(InstitutionBusiness::getHospitalId, hospitalId);
        }
        return institutionBusinessMapper.selectList(queryWrapper);

    }
}
