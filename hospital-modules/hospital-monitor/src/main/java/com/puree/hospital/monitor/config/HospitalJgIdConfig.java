package com.puree.hospital.monitor.config;

import com.alibaba.fastjson.JSON;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.monitor.regulatory.constants.HospitalConfigConstants;
import com.puree.hospital.monitor.regulatory.event.model.RegulatoryHospitalConfig;
import com.puree.hospital.setting.api.RemoteHospitalSettingApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 医院与监管机构标识对应类
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class HospitalJgIdConfig {

    @Resource
    private HospitalSecretProperties hospitalSecretProperties;

    @Resource
    private RemoteHospitalSettingApi remoteHospitalSettingApi;

    /**
     * 医院获取对应的机构id
     *
     * @return 机构编码
     */
    public String getJgId(Long hospitalId){
        RegulatoryHospitalConfig hospitalConfig = getHospitalConfig(hospitalId);
        return Objects.nonNull(hospitalConfig) ? hospitalConfig.getInstitutionCode() : null;
    }

    /**
     * 获取医院对应的机构信息
     *
     * @return 机构信息
     */
    public RegulatoryHospitalConfig getHospitalConfig(Long hospitalId){
        R<String> settingValueResult = remoteHospitalSettingApi.getSettingValue(HospitalConfigConstants.GLOBAL_MONITOR_CONFIG, hospitalId);
        if(Objects.isNull(settingValueResult) || !settingValueResult.isSuccess()){
            log.error("获取监管医院机构配置信息失败 key = " + HospitalConfigConstants.GLOBAL_MONITOR_CONFIG + " hospitalId = {}", hospitalId);
            return null;
        }
        return JSON.parseObject(settingValueResult.getData(), RegulatoryHospitalConfig.class);
    }

    /**
     * 获取医院对应的密钥
     */
    public List<HospitalSecretProperties.Hospital> getSecret(){
        return hospitalSecretProperties.getHospital().stream()
                .filter(hospital -> hospital.get("hospitalName") != null && hospital.get("secret") != null && hospital.get("id") != null)
                .map(hospital -> new HospitalSecretProperties.Hospital(hospital.get("hospitalName"),
                        hospital.get("secret"), hospital.get("id")))
                .collect(Collectors.toList());
    }

    /**
     * 设置对应的访问时间限制
     */
    public long getLimitTime(){
        return hospitalSecretProperties.getLimitTime();
    }

    /**
     * 获取ossUrl--默认（生产）
     */
    public String getOssUrl() {
        return hospitalSecretProperties.getOssUrl();
    }

    /**
     * 获取ossUrl--测试
     */
    public String getOssUrlTest() {
        return hospitalSecretProperties.getOssUrlTest();
    }

}
