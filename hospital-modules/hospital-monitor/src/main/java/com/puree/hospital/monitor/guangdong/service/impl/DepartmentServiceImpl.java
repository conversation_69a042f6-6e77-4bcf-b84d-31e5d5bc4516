package com.puree.hospital.monitor.guangdong.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.puree.hospital.monitor.config.HospitalJgIdConfig;
import com.puree.hospital.monitor.guangdong.domain.Department;
import com.puree.hospital.monitor.guangdong.mapper.DepartmentMapper;
import com.puree.hospital.monitor.guangdong.service.IDepartmentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;

@Service
public class DepartmentServiceImpl implements IDepartmentService {
    private final DepartmentMapper departmentMapper;
    private final HospitalJgIdConfig hospitalJgIdConfig;

    @Autowired
    public DepartmentServiceImpl(DepartmentMapper departmentMapper,HospitalJgIdConfig hospitalJgIdConfig) {
        this.departmentMapper = departmentMapper;
        this.hospitalJgIdConfig = hospitalJgIdConfig;
    }

    @Override
    public List<Department> selectList(Long hospitalId,String isNewHos) {
        LambdaQueryWrapper<Department> queryWrapper;
        if ("0".equals(isNewHos)) {
            queryWrapper = new LambdaQueryWrapper<Department>()
                    .eq(Department::getHospitalId, hospitalId)
                    .ge(Department::getCreateTime, LocalDate.now());
        } else {
            queryWrapper = new LambdaQueryWrapper<Department>()
                    .eq(Department::getHospitalId, hospitalId);
        }
        return departmentMapper.selectList(queryWrapper);
    }

    @Override
    public int selectSyncDataList(Long hospitalId) {
        List<Department> syncDataList = departmentMapper.selectSyncDataList(hospitalId);
        syncDataList.forEach(i -> {
            i.setJgdm(hospitalJgIdConfig.getJgId(hospitalId));
            i.setHospitalId(hospitalId);
            i.setCxbz("1");
            QueryWrapper<Department> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("KSBM",i.getKsbm());
            queryWrapper.eq("hospital_id",hospitalId);
            List<Department> departmentList = departmentMapper.selectList(queryWrapper);
            if(departmentList.size()==0) {
                departmentMapper.insert(i);
            }
        });

        return syncDataList.size();
    }
}
