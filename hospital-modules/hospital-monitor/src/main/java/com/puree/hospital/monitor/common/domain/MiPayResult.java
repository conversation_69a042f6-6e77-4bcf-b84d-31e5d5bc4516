package com.puree.hospital.monitor.common.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.puree.hospital.common.api.domain.entity.Entity;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 医保支付结果
 * </p>
 *
 * <AUTHOR>
 * @date 2024/7/3 17:44
 */
@Data
@TableName("mi_pay_result")
public class MiPayResult extends Entity {

    private static final long serialVersionUID = -3384048120149681700L;

    /**
     * 订单编号
     */
    @TableField(value = "order_no")
    private String orderNo;

    /**
     * 处方号
     */
    @TableField(value = "rx_no")
    private String rxNo;

    /**
     * 是否使用个账
     */
    @TableField(value = "acct_used_flag")
    private String acctUsedFlag;

    /**
     * 个人账户共济支付金额
     */
    @TableField(value = "acct_mulaid_pay")
    private BigDecimal acctMulaidPay;

    /**
     * 个人账户支出
     */
    @TableField(value = "acct_pay")
    private BigDecimal acctPay;

    /**
     * 实际支付起付线
     */
    @TableField(value = "act_pay_dedc")
    private BigDecimal actPayDedc;

    /**
     * 年龄
     */
    @TableField(value = "age")
    private BigDecimal age;

    /**
     * 余额
     */
    @TableField(value = "balc")
    private BigDecimal balc;

    /**
     * 出生日期
     */
    @TableField(value = "brdy")
    private String brdy;

    /**
     * 证件号码
     */
    @TableField(value = "certno")
    private String certno;

    /**
     * 清算经办机构
     */
    @TableField(value = "clr_optins")
    private String clrOptins;

    /**
     * 清算类别
     */
    @TableField(value = "clr_type")
    private String clrType;

    /**
     * 清算方式
     */
    @TableField(value = "clr_way")
    private String clrWay;

    /**
     * 公务员标志
     */
    @TableField(value = "cvlserv_flag")
    private String cvlservFlag;

    /**
     * 公务员医疗补助资金支出
     */
    @TableField(value = "cvlserv_pay")
    private BigDecimal cvlservPay;

    /**
     * 全自费金额
     */
    @TableField(value = "fulamt_ownpay_amt")
    private BigDecimal fulamtOwnpayAmt;

    /**
     * 基金支付总额
     */
    @TableField(value = "fund_pay_sumamt")
    private BigDecimal fundPaySumamt;

    /**
     * 性别
     */
    @TableField(value = "gend")
    private String gend;

    /**
     * 伤残人员医疗保障基金支出
     */
    @TableField(value = "hifdm_pay")
    private BigDecimal hifdmPay;

    /**
     * 补充医疗保险基金支出
     */
    @TableField(value = "hifes_pay")
    private BigDecimal hifesPay;

    /**
     * 大病补充医疗保险基金支出
     */
    @TableField(value = "hifmi_pay")
    private BigDecimal hifmiPay;

    /**
     * 大额医疗补助基金支出
     */
    @TableField(value = "hifob_pay")
    private BigDecimal hifobPay;

    /**
     * 基本医疗保险统筹基金支出
     */
    @TableField(value = "hifp_pay")
    private BigDecimal hifpPay;

    /**
     * 医院负担金额
     */
    @TableField(value = "hosp_part_amt")
    private BigDecimal hospPartAmt;

    /**
     * 符合范围金额
     */
    @TableField(value = "inscp_scp_amt")
    private BigDecimal inscpScpAmt;

    /**
     * 险种类型
     */
    @TableField(value = "insutype")
    private String insutype;

    /**
     * 医疗救助基金支出
     */
    @TableField(value = "maf_pay")
    private BigDecimal mafPay;

    /**
     * 就诊凭证类型
     */
    @TableField(value = "mdtrt_cert_type")
    private String mdtrtCertType;

    /**
     * 就诊ID
     */
    @TableField(value = "mdtrt_id")
    private String mdtrtId;

    /**
     * 医疗类别
     */
    @TableField(value = "med_type")
    private String medType;

    /**
     * 医疗费总额
     */
    @TableField(value = "medfee_sumamt")
    private BigDecimal medfeeSumamt;

    /**
     * 其他基金支出
     */
    @TableField(value = "oth_pay")
    private BigDecimal othPay;

    /**
     * 超限价自费费用
     */
    @TableField(value = "overlmt_selfpay")
    private BigDecimal overlmtSelfpay;

    /**
     * 基本医疗统筹比例自付
     */
    @TableField(value = "pool_prop_selfpay")
    private BigDecimal poolPropSelfpay;

    /**
     * 先行自付金额
     */
    @TableField(value = "preselfpay_amt")
    private BigDecimal preselfpayAmt;

    /**
     * 个人现金支出
     */
    @TableField(value = "psn_cash_pay")
    private BigDecimal psnCashPay;

    /**
     * 人员证件类型
     */
    @TableField(value = "psn_cert_type")
    private String psnCertType;

    /**
     * 证件名字
     */
    @TableField(value = "psn_name")
    private String psnName;

    /**
     * 证件号码
     */
    @TableField(value = "psn_no")
    private String psnNo;

    /**
     * 人员类别
     */
    @TableField(value = "psn_type")
    private String psnType;

    /**
     * 结算时间
     */
    @TableField(value = "setl_time")
    private Date setlTime;

    /**
     * 住院押金
     */
    @TableField(value = "deposit")
    private BigDecimal deposit;

    /**
     * 费用总额
     */
    @TableField(value = "feeSumamt")
    private BigDecimal feeSumamt;

    /**
     * 医保基金支付
     */
    @TableField(value = "fundPay")
    private BigDecimal fundPay;

    /**
     * 订单状态
     */
    @TableField(value = "ordStas")
    private String ordStas;

    /**
     * 其他支付金额
     */
    @TableField(value = "othFeeAmt")
    private BigDecimal othFeeAmt;

    /**
     * 现金支付
     */
    @TableField(value = "ownPayAmt")
    private BigDecimal ownPayAmt;

    /**
     * 支付订单号
     */
    @TableField(value = "payOrdId")
    private String payOrdId;

    /**
     * 个人账户支出
     */
    @TableField(value = "psnAcctPay")
    private BigDecimal psnAcctPay;

    /**
     * 支付授权码
     */
    private String payAuthNo;

    /**
     * 微信医保支付返回单号
     */
    private String medTransId;

    /**
     * 微信支付url
     */
    private String payUrl;

    /**
     * 运费
     */
    private BigDecimal freight;

    /**
     * 挂号费
     */
    private BigDecimal examinationFee;

    /**
     * 其他现金支付金额
     */
    private BigDecimal otherCashAmount;
    /**
     * 中药加工费用
     */
    private BigDecimal processFee;
}
