package com.puree.hospital.monitor.guangdong.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.puree.hospital.common.core.utils.AgeCalculationUtil;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.monitor.config.HospitalJgIdConfig;
import com.puree.hospital.monitor.guangdong.domain.MedicalDiagnosis;
import com.puree.hospital.monitor.guangdong.domain.model.MMDiagnosis;
import com.puree.hospital.monitor.guangdong.domain.model.TCMDiagnosis;
import com.puree.hospital.monitor.guangdong.infrastructure.DiagnosisEnum;
import com.puree.hospital.monitor.guangdong.mapper.MedicalDiagnosisMapper;
import com.puree.hospital.monitor.guangdong.service.IMedicalDiagnosisService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;

@Service
public class MedicalDiagnosisServiceImpl implements IMedicalDiagnosisService {
    private final MedicalDiagnosisMapper medicalDiagnosisMapper;
    private final HospitalJgIdConfig hospitalJgIdConfig;

    @Autowired
    public MedicalDiagnosisServiceImpl(MedicalDiagnosisMapper medicalDiagnosisMapper, HospitalJgIdConfig hospitalJgIdConfig) {
        this.medicalDiagnosisMapper = medicalDiagnosisMapper;
        this.hospitalJgIdConfig = hospitalJgIdConfig;
    }

    @Override
    public List<MedicalDiagnosis> selectList(Long hospitalId, String isNewHos) {
        if ("0".equals(isNewHos)) {
            LambdaQueryWrapper<MedicalDiagnosis> queryWrapper = new LambdaQueryWrapper<MedicalDiagnosis>()
                    .eq(MedicalDiagnosis::getHospitalId, hospitalId)
                    .ge(MedicalDiagnosis::getCreateTime, LocalDate.now());
            return medicalDiagnosisMapper.selectList(queryWrapper);
        } else {
            LambdaQueryWrapper<MedicalDiagnosis> queryWrapper = new LambdaQueryWrapper<MedicalDiagnosis>()
                    .eq(MedicalDiagnosis::getHospitalId, hospitalId);
            return medicalDiagnosisMapper.selectList(queryWrapper);
        }
    }

    @Override
    public int syncData(Long hospitalId) {
        List<MedicalDiagnosis> syncDataList = medicalDiagnosisMapper.selectSyncDataList(hospitalId);
        syncDataList.forEach(i -> {
            i.setJgdm(hospitalJgIdConfig.getJgId(hospitalId));
            i.setFwwddm(hospitalJgIdConfig.getJgId(hospitalId));
            i.setKh(String.valueOf(i.getFamilyId()));
            i.setKlx("3");//系统内部号
            String xbdm = i.getXbdm().equals("0") ? "2" : "1";
            i.setXbdm(xbdm);
            int nls = AgeCalculationUtil.getAge(i.getCsrq());
            i.setNls(nls);
            if (nls == 0) {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                String csrq = sdf.format(i.getCsrq());
                String[] arrays = csrq.split("-");
                csrq = arrays[1] + " " + arrays[2] + "/30";
                i.setNly(csrq);
            }
            String zdlxbm = i.getPrescriptionType().equals("0") ? DiagnosisEnum.ZYZDZZ.getCode() : DiagnosisEnum.XYZDZY.getCode();
            i.setZdlxbm(zdlxbm);
            if (i.getZdlxbm().equals(DiagnosisEnum.ZYZDZZ.getCode())) {
                if (StringUtils.isNotEmpty(i.getClinicalDiagnosis())) {
                    List<TCMDiagnosis> list = JSONObject.parseArray(i.getClinicalDiagnosis(), TCMDiagnosis.class);
                    i.setZyzdbm(list.get(0).getDiagnosisCode());
                    i.setZyzdmc(list.get(0).getTcmDiagnosis());
                    i.setZyzdbmlx("03");//国标97
                }
            } else {
                if (StringUtils.isNotEmpty(i.getClinicalDiagnosis())) {
                    List<MMDiagnosis> list = JSONObject.parseArray(i.getClinicalDiagnosis(), MMDiagnosis.class);
                    i.setXyzdbm(list.get(0).getIcdCode());
                    i.setXyzdmc(list.get(0).getDiseaseName());
                    i.setXyzdbmlx("01");//ICD-10
                }
            }
            i.setZdbz("1");
            i.setCxbz("1");
            i.setSjscsj(new Date());
        });

        for (MedicalDiagnosis p : syncDataList) {
            p.setHospitalId(hospitalId);
            QueryWrapper<MedicalDiagnosis> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("ZDXXID", p.getZdxxid());
            queryWrapper.eq("hospital_id", hospitalId);
            List<MedicalDiagnosis> plist = medicalDiagnosisMapper.selectList(queryWrapper);
            if (CollectionUtils.isEmpty(plist)) {
                p.setHospitalId(hospitalId);
                medicalDiagnosisMapper.insert(p);
            }
        }
        return 1;
    }

    @Override
    public int insert(MedicalDiagnosis medicalDiagnosis) {
        return medicalDiagnosisMapper.insert(medicalDiagnosis);
    }
}
