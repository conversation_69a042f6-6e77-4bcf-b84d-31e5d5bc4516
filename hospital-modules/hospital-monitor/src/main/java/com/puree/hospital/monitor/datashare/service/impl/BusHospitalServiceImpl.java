package com.puree.hospital.monitor.datashare.service.impl;

import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.monitor.common.domain.BusHospital;
import com.puree.hospital.monitor.datashare.domain.vo.BusHospitalVO;
import com.puree.hospital.monitor.common.mapper.BusHospitalMapper;
import com.puree.hospital.monitor.datashare.service.IBusHospitalService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/1/16 15:45
 */
@Service
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BusHospitalServiceImpl implements IBusHospitalService {
    private final BusHospitalMapper busHospitalMapper;

    /**
     * 查询医院信息
     *
     * @param hospitalCode 医院标识
     * @return
     */
    @Override
    public BusHospitalVO queryHospital(String hospitalCode) {
        return busHospitalMapper.queryHospital(hospitalCode);
    }

    /**
     * 查询医院编码
     *
     * @param hospitalId
     * @return
     */
    @Override
    public String selectHospitalCode(Long hospitalId) {
        BusHospital busHospital = busHospitalMapper.selectById(hospitalId);
        if (Objects.isNull(busHospital)) {
            throw new ServiceException("医院不存在");
        }
        return busHospital.getHospitalCode();
    }
}
