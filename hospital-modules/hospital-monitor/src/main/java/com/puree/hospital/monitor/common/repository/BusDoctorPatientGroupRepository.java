package com.puree.hospital.monitor.common.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.puree.hospital.common.core.enums.ImGroupType;
import com.puree.hospital.monitor.common.domain.BusDoctorPatientGroup;
import com.puree.hospital.monitor.common.mapper.BusDoctorPatientGroupMapper;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Set;

/**
 * <p>
 * 诊疗群组 Repository
 * </p>
 *
 * <AUTHOR>
 * @date 2024/7/8 14:27
 */
@Repository
public class BusDoctorPatientGroupRepository extends ServiceImpl<BusDoctorPatientGroupMapper, BusDoctorPatientGroup> {

    /**
     * 获取诊疗群组
     *
     * @param doctorId     医生ID
     * @param patientId    患者ID
     * @param familyId     患者家庭ID
     * @param departmentId 医生所属科室ID
     * @param hospitalId   医生所属医院ID
     * @return 诊疗群组
     */
    public BusDoctorPatientGroup getGroup(Long doctorId,
                                          Long patientId,
                                          Long familyId,
                                          Long departmentId,
                                          Long hospitalId) {
        LambdaQueryWrapper<BusDoctorPatientGroup> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BusDoctorPatientGroup::getDoctorId, doctorId)
                .eq(BusDoctorPatientGroup::getPatientId, patientId)
                .eq(BusDoctorPatientGroup::getFamilyId, familyId)
                .eq(BusDoctorPatientGroup::getDepartmentId, departmentId)
                .eq(BusDoctorPatientGroup::getHospitalId, hospitalId)
                .eq(BusDoctorPatientGroup::getType, ImGroupType.INQUIRIES.getCode())
                .eq(BusDoctorPatientGroup::getMarkChanges, Boolean.FALSE)
                .orderByDesc(BusDoctorPatientGroup::getId);
        return this.getOne(queryWrapper, false);
    }

    public Set<Long> getGroupIds( Long doctorId,
                                  Long patientId,
                                  Long familyId,
                                  Long departmentId,
                                  Long hospitalId) {
        return baseMapper.getDistinctGroupIds(doctorId, patientId, familyId, departmentId, hospitalId);
    }
}
