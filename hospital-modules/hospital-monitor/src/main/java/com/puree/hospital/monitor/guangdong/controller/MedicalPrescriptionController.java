package com.puree.hospital.monitor.guangdong.controller;

import com.puree.hospital.common.core.web.controller.BaseController;
import com.puree.hospital.common.api.domain.AjaxResult;
import com.puree.hospital.monitor.guangdong.domain.MedicalPrescription;
import com.puree.hospital.monitor.guangdong.service.IMedicalPrescriptionService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("guangdong/medicalprescription")
public class MedicalPrescriptionController extends BaseController {
    @Resource
    private IMedicalPrescriptionService medicalPrescriptionService;

    /**
     * 诊疗处方
     */
    @GetMapping("list")
    public List<MedicalPrescription> list(@RequestParam("hospitalId") Long hospitalId, @RequestParam(defaultValue = "0") String isNewHos) {
        logger.info("诊疗处方：入参{}", hospitalId);
        //先同步
        medicalPrescriptionService.syncData(hospitalId);
        //再拉取
        List<MedicalPrescription> list = medicalPrescriptionService.selectList(hospitalId, isNewHos);
        logger.info("诊疗处方：出参{}", list);
        return list;
    }

    /**
     * 同步数据
     */
    @PostMapping("/sync/{id}")
    public AjaxResult sync(@PathVariable Long id) {
        return toAjax(medicalPrescriptionService.syncData(id));
    }

    /**
     * 插入数据
     */
    @PostMapping("/insert")
    public AjaxResult insert(@RequestBody MedicalPrescription medicalPrescription) {
        return toAjax(medicalPrescriptionService.insert(medicalPrescription));
    }
}
