package com.puree.hospital.monitor.common.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.puree.hospital.monitor.common.domain.MiRefundResult;
import com.puree.hospital.monitor.common.mapper.MiRefundResultMapper;
import org.springframework.stereotype.Repository;

/**
 * <p>
 * 医保退款结果 Repository
 * </p>
 *
 * <AUTHOR>
 * @date 2024/7/3 18:57
 */
@Repository
public class MiRefundResultRepository extends ServiceImpl<MiRefundResultMapper, MiRefundResult> {

    /**
     * 根据订单号查询医保退款结果
     *
     * @param orderNo 订单号
     * @return 医保退款结果
     */
    public MiRefundResult getByOrderNo(String orderNo) {
        LambdaQueryWrapper<MiRefundResult> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MiRefundResult::getOrderNo, orderNo);
        return this.getOne(wrapper, false);
    }
}
