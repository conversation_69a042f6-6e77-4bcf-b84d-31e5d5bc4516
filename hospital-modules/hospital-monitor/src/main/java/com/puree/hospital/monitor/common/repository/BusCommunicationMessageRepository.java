package com.puree.hospital.monitor.common.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.puree.hospital.monitor.common.domain.BusCommunicationMessage;
import com.puree.hospital.monitor.common.mapper.BusCommunicationMessageMapper;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * <p>
 * IM通讯消息对象 Repository
 * </p>
 *
 * <AUTHOR>
 * @date 2024/7/18 18:18
 */
@Repository
public class BusCommunicationMessageRepository extends ServiceImpl<BusCommunicationMessageMapper, BusCommunicationMessage> {

    private static final String TIM_CUSTOM_ELEMENT_TYPE = "TIMCustomElem";

    private static final String TIM_TEXT_ELEMENT_TYPE = "TIMTextElem";

    private static final String PATIENT_QUICK_CONSULTATION = "PATIENT_QUICKCONSULTATION";

    private static final String VIDEOCHAT_FILES = "VIDEOCHAT_FILES";

    /**
     * 获取最后一条自定义消息
     *
     * @param groupId 消息组id
     * @return BusCommunicationMessage
     */
    public BusCommunicationMessage getLastCustomElemMessage(Long groupId) {
        LambdaQueryWrapper<BusCommunicationMessage> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BusCommunicationMessage::getGroupId, groupId);
        wrapper.eq(BusCommunicationMessage::getType, TIM_CUSTOM_ELEMENT_TYPE);
        wrapper.like(BusCommunicationMessage::getPayload, PATIENT_QUICK_CONSULTATION);
        wrapper.orderByDesc(BusCommunicationMessage::getId);
        return this.getOne(wrapper, false);
    }

    /**
     * 获取医生最后回复消息列表
     *
     * @param groupId         消息组id
     * @param doctorId        医生id
     * @param lastUpdateTime  回合数
     * @return BusCommunicationMessage
     */
    public BusCommunicationMessage getLastDoctorMessage(Long groupId, Long doctorId, Date lastUpdateTime) {
        LambdaQueryWrapper<BusCommunicationMessage> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BusCommunicationMessage::getGroupId, groupId);
        wrapper.eq(BusCommunicationMessage::getType, TIM_TEXT_ELEMENT_TYPE);
        wrapper.eq(BusCommunicationMessage::getFrom, "DOCTOR_" + doctorId);
        wrapper.le(BusCommunicationMessage::getCreateTime, lastUpdateTime);
        wrapper.orderByDesc(BusCommunicationMessage::getId);
        wrapper.last("limit 1");
        return this.getOne(wrapper, false);
    }

    public List<BusCommunicationMessage> getPayloadByGroupIds(Set<Long> groupIds) {
        LambdaQueryWrapper<BusCommunicationMessage> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BusCommunicationMessage::getType, TIM_CUSTOM_ELEMENT_TYPE);
        wrapper.in(BusCommunicationMessage::getGroupId, groupIds);
        // 数据字段过长，使用like查询可能会慢，这里使用apply查询
        wrapper.apply("INSTR(payload, {0}) > 0", VIDEOCHAT_FILES);
        wrapper.orderByDesc(BusCommunicationMessage::getId);
        return this.list(wrapper);
    }
}
