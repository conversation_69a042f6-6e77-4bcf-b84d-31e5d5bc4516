package com.puree.hospital.monitor.guangdong.controller;

import com.puree.hospital.common.core.web.controller.BaseController;
import com.puree.hospital.monitor.guangdong.domain.ServicePoint;
import com.puree.hospital.monitor.guangdong.service.IServicePointService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("guangdong/servicepoint")
public class ServicePointController extends BaseController {
    @Resource
    private IServicePointService servicePointService;

    /**
     * 服务网点表
     */
    @GetMapping("list")
    public List<ServicePoint> list(@RequestParam("hospitalId") Long hospitalId,@RequestParam(defaultValue = "0") String isNewHos) {
        logger.info("服务网点表：入参{}", hospitalId);
        List<ServicePoint> list = servicePointService.selectList(hospitalId, isNewHos);
        logger.info("服务网点表：出参{}", list);
        return list;
    }
}
