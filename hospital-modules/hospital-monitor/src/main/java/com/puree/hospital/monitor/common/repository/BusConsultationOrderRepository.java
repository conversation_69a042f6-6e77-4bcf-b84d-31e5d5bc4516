package com.puree.hospital.monitor.common.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.puree.hospital.monitor.common.domain.BusConsultationOrder;
import com.puree.hospital.monitor.common.mapper.BusConsultationOrderMapper;
import org.springframework.stereotype.Repository;

/**
 * <p>
 * 问诊订单信息 Repository
 * </p>
 *
 * <AUTHOR>
 * @date 2024/6/26 15:40
 */
@Repository
public class BusConsultationOrderRepository extends ServiceImpl<BusConsultationOrderMapper, BusConsultationOrder> {

    /**
     * 根据订单号查询问诊订单信息
     *
     * @param orderNo 问诊订单号
     * @return 问诊订单信息
     */
    public BusConsultationOrder getByOrderNo(String orderNo) {
        LambdaQueryWrapper<BusConsultationOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BusConsultationOrder::getOrderNo, orderNo);
        return this.getOne(queryWrapper, false);
    }

}
