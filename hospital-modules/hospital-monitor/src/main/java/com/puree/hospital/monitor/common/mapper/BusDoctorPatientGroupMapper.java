package com.puree.hospital.monitor.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.puree.hospital.monitor.common.domain.BusDoctorPatientGroup;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Set;

/**
 * <p>
 * 诊疗群组 Mapper接口
 * </p>
 *
 * <AUTHOR>
 * @date 2024/7/8 14:26
 */
@Mapper
public interface BusDoctorPatientGroupMapper extends BaseMapper<BusDoctorPatientGroup> {

    /**
     * 获取诊疗群组
     *
     * @param doctorId     医生ID
     * @param patientId    患者ID
     * @param familyId     患者家庭ID
     * @param departmentId 医生所属科室ID
     * @param hospitalId   医生所属医院ID
     * @return 诊疗群组
     */
    Set<Long> getDistinctGroupIds(@Param("doctorId") Long doctorId, @Param("patientId") Long patientId,
                                  @Param("familyId") Long familyId, @Param("departmentId") Long departmentId,
                                  @Param("hospitalId") Long hospitalId);
}
