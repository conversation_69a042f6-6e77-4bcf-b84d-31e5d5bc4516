package com.puree.hospital.monitor.regulatory.event.config;

import com.puree.hospital.common.rabbitmq.config.RabbitEnableCondition;
import com.puree.hospital.monitor.api.event.constant.EventMessageQueueConstant;
import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.DirectExchange;
import org.springframework.amqp.core.Queue;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Conditional;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

/**
 * <p>
 * 事件消息綁定配置
 * </p>
 *
 * <AUTHOR>
 * @date 2024/6/25 08:53
 */
@Configuration
@Conditional(RabbitEnableCondition.class)
public class EventMessageBindConfig {

    /**
     * 默認的交換機
     */
    @Bean
    DirectExchange defaultExchange() {
        return new DirectExchange(EventMessageQueueConstant.REGULATORY_MONITOR_COLLECTOR_EXCHANGE, true, false);
    }

    @Bean
    public Queue regulatorQueue() {
        // durable: 是否持久化,默认是false,持久化队列：会被存储在磁盘上，当消息代理重启时仍然存在，暂存队列：当前连接有效
        // exclusive: 默认也是false，只能被当前创建的连接使用，而且当连接关闭后队列即被删除。
        // autoDelete: 是否自动删除，当没有生产者或者消费者使用此队列，该队列会自动删除。
        Map<String, Object> args = regulatorQueueArgs();
        return new Queue(EventMessageQueueConstant.REGULATORY_MONITOR_COLLECTOR_QUEUE, true, false, false, args);
    }

    @Bean
    Binding regulatorQueueBinding() {
        return BindingBuilder.bind(regulatorQueue())
                .to(defaultExchange())
                .with(EventMessageQueueConstant.REGULATORY_MONITOR_COLLECTOR_TOPIC);
    }

    /**
     * 死信队列
     */
    @Bean
    public Queue regulatorDeadQueue() {
        Map<String, Object> args = regulatorDeadQueueArgs();
        return new Queue(EventMessageQueueConstant.REGULATORY_MONITOR_COLLECTOR_QUEUE + "_dead", true, false, false, args);
    }

    /**
     * 私信队列绑定关系
     *
     * @return 绑定信息
     */
    @Bean
    Binding regulatorDeadQueueBinding() {
        return BindingBuilder.bind(regulatorDeadQueue())
                .to(defaultExchange())
                .with(EventMessageQueueConstant.REGULATORY_MONITOR_COLLECTOR_TOPIC + "_dead");
    }

    /**
     * 转发到 死信队列，配置参数
     */
    private Map<String, Object> regulatorQueueArgs() {
        Map<String, Object> map = new HashMap<>();
        // 绑定该队列到死信交换机
        map.put("x-dead-letter-exchange", EventMessageQueueConstant.REGULATORY_MONITOR_COLLECTOR_EXCHANGE);
        map.put("x-dead-letter-routing-key", EventMessageQueueConstant.REGULATORY_MONITOR_COLLECTOR_TOPIC + "_dead");
        return map;
    }

    /**
     * 转发到 死信队列，配置参数
     */
    private Map<String, Object> regulatorDeadQueueArgs() {
        Map<String, Object> map = new HashMap<>();
        // 绑定该队列到死信交换机
        map.put("x-dead-letter-exchange", EventMessageQueueConstant.REGULATORY_MONITOR_COLLECTOR_EXCHANGE);
        //默认超时时间120s
        map.put("x-message-ttl", 120000);
        map.put("x-dead-letter-routing-key", EventMessageQueueConstant.REGULATORY_MONITOR_COLLECTOR_TOPIC);
        return map;
    }
}
