package com.puree.hospital.monitor.regulatory.event.assembler.yunnan;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.google.common.collect.ImmutableMultimap;
import com.puree.hospital.common.core.utils.des.DESUtil;
import com.puree.hospital.monitor.api.event.constant.RegulatoryAreaConstant;
import com.puree.hospital.monitor.api.event.constant.RegulatoryEventConstant;
import com.puree.hospital.monitor.api.event.regulatory.report.yunnan.YnDoctor;
import com.puree.hospital.monitor.common.domain.BusDoctor;
import com.puree.hospital.monitor.common.domain.BusDoctorDepartment;
import com.puree.hospital.monitor.common.domain.BusDoctorHospital;
import com.puree.hospital.monitor.common.repository.BusDoctorDepartmentRepository;
import com.puree.hospital.monitor.common.repository.BusDoctorHospitalRepository;
import com.puree.hospital.monitor.common.repository.BusDoctorRepository;
import com.puree.hospital.monitor.datashare.service.SysDictDataService;
import com.puree.hospital.monitor.regulatory.event.assembler.AbstractEventDataAssembler;
import com.puree.hospital.monitor.regulatory.event.assembler.EventDataAssembler;
import com.puree.hospital.monitor.regulatory.event.model.RegulatoryHospitalConfig;
import com.puree.hospital.system.api.model.SysDictData;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <p>
 * 云南医生备案
 * </p>
 *
 * <AUTHOR>
 * @date 2024/6/25 14:28
 */
@Component(RegulatoryAreaConstant.YUN_NAN + RegulatoryEventConstant.DOCTOR_FILING + EventDataAssembler.SUFFIX)
public class YnDoctorEventDataAssembler extends AbstractEventDataAssembler<YnDoctor> {

    @Resource
    private BusDoctorRepository busDoctorRepository;

    @Resource
    private SysDictDataService sysDictDataService;

    @Resource
    private BusDoctorDepartmentRepository busDoctorDepartmentRepository;

    @Resource
    private BusDoctorHospitalRepository busDoctorHospitalRepository;

    @Override
    protected YnDoctor newInstance() {
        return new YnDoctor();
    }

    @Override
    protected void assembleOtherInfo(YnDoctor regulatory, RegulatoryHospitalConfig config) {
        BusDoctor busDoctor = busDoctorRepository.getById(regulatory.getBusinessId());
        if (Objects.isNull(busDoctor)) {
            throw new IllegalStateException(String.format("未找到医生信息, 医生id:%s", regulatory.getBusinessId()));
        }
        regulatory.setFullName(busDoctor.getFullName());
        String idCardNo = DESUtil.decrypt(busDoctor.getIdCardNo());
        regulatory.setIdCardNo(idCardNo);
        regulatory.setSex(busDoctor.getSex());
        regulatory.setBirthday(super.getBirthday(idCardNo));
        regulatory.setPracticeCertificateNumber(busDoctor.getPracticeCertificateNumber());
        regulatory.setQualificationCertificateNumber(busDoctor.getQualificationCertificateNumber());
        regulatory.setTitleCertificateNumber(busDoctor.getTitleCertificateNumber());
        regulatory.setObtainEmploymentTime(DateUtil.format(busDoctor.getWorkTime(), DatePattern.PURE_DATE_PATTERN));
        //String practisingScopeCode = getPractisingScopeCode(busDoctor, regulatory.getHospitalId());
        regulatory.setPractisingScopeCode(busDoctor.getPractisingScopeCode());
        regulatory.setPractisingTypeCode(busDoctor.getPractisingTypeCode());
        regulatory.setDoctTitleCode(getDoctorTitleCode(busDoctor.getTitle()));
        BusDoctorHospital busDoctorHospital = busDoctorHospitalRepository.getByDoctorIdAndHospitalId(busDoctor.getId(), regulatory.getHospitalId());
        if (Objects.isNull(busDoctorHospital)) {
            throw new IllegalArgumentException(String.format("未找到医生与医院关联信息, 医生id:%s, 医院id:%s", busDoctor.getId(), regulatory.getHospitalId()));
        }
        if (Objects.equals(1, busDoctorHospital.getIsThisCourt())) {
            regulatory.setFirstHospitalUnifiedCreditCode(regulatory.getUnifiedCreditCode());
        } else {
            regulatory.setFirstHospitalUnifiedCreditCode(busDoctor.getFirstHospitalUnifiedCreditCode());
        }
        regulatory.setIfAccepted(Objects.equals(1, busDoctorHospital.getStatus()));
    }

    @Override
    protected String getEventType() {
        return RegulatoryEventConstant.DOCTOR_FILING;
    }


    /**
     * 医生执业范围数据
     */
    private static final ImmutableMultimap<String, String> PRACTISING_SCOPE_CODE_MAP = ImmutableMultimap.<String, String>builder()
            .put("内科", "A11")
            .put("外科", "A12")
            .put("妇产科", "A13")
            .put("儿科", "A14")
            .put("眼耳鼻咽喉科", "A15")
            .put("皮肤病与性病", "A16")
            .put("精神卫生", "A17")
            .put("职业病", "A18")
            .put("医学影像和放射治疗", "A19")
            .put("医学检验、病理", "A20")
            .put("全科医学", "A21")
            .put("急救医学", "A22")
            .put("康复医学", "A23")
            .put("预防保健", "A24")
            .put("特种医学与军事医学", "A25")
            .put("计划生育技术服务", "A26")
            .put("口腔", "A31")
            .put("公共卫生类别", "A41")
            .put("中医", "A51")
            .put("中西医结合", "A52")
            .put("蒙医", "A53")
            .put("藏医", "A54")
            .put("维医", "A55")
            .put("傣医", "A56")
            .put("朝医", "A57")
            .put("壮医", "A58")
            .put("省级卫生行政部门规定的其他", "A59")
            .build();

    /**
     * 获取医生职称编码
     *
     * @param title 医生职称标识
     * @return 医生职称编码
     */
    private String getDoctorTitleCode(Long title) {
        //根据dict_code查询其对应的字典信息
        SysDictData sysDictData = sysDictDataService.getDictByCode(title);
        if (Objects.isNull(sysDictData)) {
            throw new IllegalArgumentException("未找到医生职称数据字典信息");
        }
        String doctorTitleCode = "234";
        for (Map.Entry<String, String> entry : DOCTOR_TITLE_CODE_MAP.entries()) {
            if (sysDictData.getDictLabel().contains(entry.getKey())) {
                doctorTitleCode = entry.getValue();
            }
        }
        return doctorTitleCode;
    }

    /**
     * 获取从业类别编码
     *
     * @param busDoctor           医生信息
     * @param practisingScopeCode 医师置业范围code
     * @return 从业类别编码
     */
    @SuppressWarnings("unused")
    private String getPractisingTypeCode(BusDoctor busDoctor, String practisingScopeCode) {
        // 口腔
        if ("A31".equals(practisingScopeCode)) {
            return "2";
        }
        // 公共卫生
        if ("A41".equals(practisingScopeCode)) {
            return "3";
        }
        // 中医
        if ("A51".equals(practisingScopeCode) || "A52".equals(practisingScopeCode)) {
            return "4";
        }
        return "1";
    }

    /**
     * 获取职业范围数据：
     * 实现步骤：1.查出医生所挂的科室，
     * 2.遍历科室名称，与监管的职业范围数据名称取相似度，取相似度最高的值作为医生职业范围编码
     *
     * @param busDoctor  医生
     * @param hospitalId 医院id
     * @return 职业范围编码
     */
    @SuppressWarnings("unused")
    private String getPractisingScopeCode(BusDoctor busDoctor, Long hospitalId) {
        String practisingScopeCode = "A59";
        BusDoctorDepartment doctorDepartment = new BusDoctorDepartment();
        doctorDepartment.setDoctorId(busDoctor.getId());
        doctorDepartment.setHospitalId(hospitalId);
        List<BusDoctorDepartment> departmentList = busDoctorDepartmentRepository.selectList(doctorDepartment);
        if (CollectionUtil.isNotEmpty(departmentList)) {
            double similarity = 0;
            for (BusDoctorDepartment department : departmentList) {
                for (Map.Entry<String, String> entry : PRACTISING_SCOPE_CODE_MAP.entries()) {
                    //计算医生挂属标准科室相似度
                    double currentSimilarity = getSimilarity(department.getStandardDepartmentName(), entry.getKey());
                    if (currentSimilarity > similarity) {
                        practisingScopeCode = entry.getValue();
                        similarity = currentSimilarity;
                    }
                }
            }
        }
        return practisingScopeCode;
    }
}
