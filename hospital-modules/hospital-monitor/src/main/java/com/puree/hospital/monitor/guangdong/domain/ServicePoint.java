package com.puree.hospital.monitor.guangdong.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("gd_t_ins_service_point")
public class ServicePoint {
    /** 撤销标志 */
    private String cxbz;
    /** 单位隶属关系代码 */
    private String dwlsgxdm;
    /** 地址 */
    private String dz;
    /** 服务点成立日期 */
    private Date fwdclrq;
    /** 服务点分类代码 */
    private String fwdfldm;
    /** 服务点分类管理类别代码 */
    private String fwdflgllbdm;
    /** 服务点类型 */
    private String fwdlx;
    /** 服务点名称 */
    private String fwdmc;
    /** 服务点所在地民族自治地方标志 */
    private String fwdszdmzzzdfbz;
    /** 服务点医院等级 */
    private String fwdyydj;
    /** 服务点医院级别 */
    private String fwdyyjb;
    /** 服务网点代码 */
    private String fwwddm;
    /** 机构标识 */
    private String jgdm;
    /** 经济类型代码 */
    private String jjlxdm;
    /** 是否分支机构 */
    private String sffzjg;
    /** 数据生成日期时间 */
    private Date sjscsj;
    /** 许可项目名称 */
    private String xkxmmc;
    /** 许可证号码 */
    private String xkzhm;
    /** 许可证有效期 */
    private Date xkzyxq;
    /** 统一社会信用代码 */
    private String zzjgdm;
    /** 创建时间（辅助作用） */
    private Date createTime;
    private Long hospitalId;
}
