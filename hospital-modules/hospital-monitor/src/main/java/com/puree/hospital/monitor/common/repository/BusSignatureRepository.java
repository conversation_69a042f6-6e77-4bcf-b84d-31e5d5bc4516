package com.puree.hospital.monitor.common.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.puree.hospital.monitor.common.domain.BusSignature;
import com.puree.hospital.monitor.common.mapper.BusSignatureMapper;
import org.springframework.stereotype.Repository;

/**
 * <p>
 * 签名信息 Repository
 * </p>
 *
 * <AUTHOR>
 * @date 2024/6/26 14:14
 */
@Repository
public class BusSignatureRepository extends ServiceImpl<BusSignatureMapper, BusSignature> {

    /**
     * 根据对象ID和类型查询
     *
     * @param objectId   对象ID
     * @param objectType 对象类型
     * @return 签名详细信息
     */
    public BusSignature getByObjectIdAndObjectType(Long objectId, String objectType) {
        LambdaQueryWrapper<BusSignature> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BusSignature::getObjectId, objectId)
                .eq(BusSignature::getObjectType, objectType);
        return this.getOne(queryWrapper, false);
    }

}
