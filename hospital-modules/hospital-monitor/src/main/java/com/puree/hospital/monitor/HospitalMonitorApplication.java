package com.puree.hospital.monitor;

import com.puree.hospital.common.security.annotation.EnableCustomConfig;
import com.puree.hospital.common.security.annotation.EnableHospitalFeignClients;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.amqp.RabbitAutoConfiguration;

/**
 * <AUTHOR>
 */
@EnableCustomConfig
@EnableHospitalFeignClients
@SpringBootApplication(exclude = {RabbitAutoConfiguration.class})
public class HospitalMonitorApplication {

    public static void main(String[] args) {
        System.setProperty("druid.mysql.usePingMethod","false");
        SpringApplication.run(HospitalMonitorApplication.class, args);
        System.out.println("省监管模块启动成功");
    }

}
