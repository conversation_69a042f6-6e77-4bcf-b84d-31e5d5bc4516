package com.puree.hospital.monitor.regulatory.event.assembler.yunnan;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.common.core.utils.des.DESUtil;
import com.puree.hospital.monitor.api.event.constant.RegulatoryAreaConstant;
import com.puree.hospital.monitor.api.event.constant.RegulatoryEventConstant;
import com.puree.hospital.monitor.api.event.regulatory.report.yunnan.YnPharmacist;
import com.puree.hospital.monitor.common.domain.BusDoctor;
import com.puree.hospital.monitor.common.domain.BusDoctorHospital;
import com.puree.hospital.monitor.common.repository.BusDoctorHospitalRepository;
import com.puree.hospital.monitor.common.repository.BusDoctorRepository;
import com.puree.hospital.monitor.datashare.service.SysDictDataService;
import com.puree.hospital.monitor.regulatory.event.assembler.AbstractEventDataAssembler;
import com.puree.hospital.monitor.regulatory.event.assembler.EventDataAssembler;
import com.puree.hospital.monitor.regulatory.event.model.RegulatoryHospitalConfig;
import com.puree.hospital.system.api.model.SysDictData;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <p>
 * 云南药师备案
 * </p>
 *
 * <AUTHOR>
 * @date 2024/6/25 14:30
 */
@Component(RegulatoryAreaConstant.YUN_NAN + RegulatoryEventConstant.PHARMACIST_FILING + EventDataAssembler.SUFFIX)
public class YnPharmacistEventDataAssembler extends AbstractEventDataAssembler<YnPharmacist> {

    @Resource
    private BusDoctorRepository busDoctorRepository;

    @Resource
    private SysDictDataService sysDictDataService;

    @Resource
    private BusDoctorHospitalRepository busDoctorHospitalRepository;

    @Override
    protected YnPharmacist newInstance() {
        return new YnPharmacist();
    }

    @Override
    protected void assembleOtherInfo(YnPharmacist regulatory, RegulatoryHospitalConfig config) {
        BusDoctor doctor = busDoctorRepository.getById(regulatory.getBusinessId());
        if (Objects.isNull(doctor)) {
            throw new IllegalStateException(String.format("未找到该药师信息, 药师id:%s", regulatory.getBusinessId()));
        }
        regulatory.setFullName(doctor.getFullName());
        String idCardNo = DESUtil.decrypt(doctor.getIdCardNo());
        regulatory.setIdCardNo(idCardNo);
        regulatory.setSex(doctor.getSex());
        regulatory.setBirthday(super.getBirthday(idCardNo));
        regulatory.setObtainEmploymentTime(DateUtil.format(doctor.getWorkTime(), DatePattern.PURE_DATE_PATTERN));
        regulatory.setQualificationCertificateNumber(doctor.getQualificationCertificateNumber());
        String pharTitleCode = "244";
        //根据dict_code查询其对应的字典信息
        SysDictData sysDictData = sysDictDataService.getDictByCode(doctor.getTitle());
        if (Objects.nonNull(sysDictData) && StringUtils.isNotBlank(sysDictData.getDictValue())) {
            pharTitleCode = sysDictData.getDictValue();
        }
        regulatory.setPharTitleCode(pharTitleCode);
        BusDoctorHospital busDoctorHospital = busDoctorHospitalRepository.getByDoctorIdAndHospitalId(doctor.getId(), regulatory.getHospitalId());
        if (Objects.isNull(busDoctorHospital)) {
            throw new IllegalArgumentException(String.format("未找到医生与医院关联信息, 医生id:%s, 医院id:%s", doctor.getId(), regulatory.getHospitalId()));
        }
        regulatory.setIfAccepted(Objects.equals(1, busDoctorHospital.getStatus()));
    }

    @Override
    protected String getEventType() {
        return RegulatoryEventConstant.PHARMACIST_FILING;
    }
}
