package com.puree.hospital.monitor.guangdong.controller;

import com.puree.hospital.common.core.web.controller.BaseController;
import com.puree.hospital.monitor.guangdong.domain.Device;
import com.puree.hospital.monitor.guangdong.service.IDeviceService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("guangdong/device")
public class DeviceController extends BaseController {
    @Resource
    private IDeviceService deviceService;

    /**
     * 设备信息表
     */
    @GetMapping("list")
    public List<Device> list(@RequestParam("hospitalId") Long hospitalId, @RequestParam(defaultValue = "0") String isNewHos) {
        logger.info("设备信息表：入参{}", hospitalId);
        List<Device> list = deviceService.selectList(hospitalId, isNewHos);
        logger.info("设备信息表：出参{}", list);
        return list;
    }
}
