package com.puree.hospital.monitor.datashare.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.puree.hospital.common.core.enums.CodeEnum;
import com.puree.hospital.common.api.enums.YesNoEnum;
import com.puree.hospital.common.core.utils.bean.OrikaUtils;
import com.puree.hospital.common.core.utils.http.HttpUtil;
import com.puree.hospital.common.core.utils.sign.MD5Util;
import com.puree.hospital.common.core.web.controller.BaseController;
import com.puree.hospital.common.api.domain.AjaxResult;
import com.puree.hospital.common.core.web.domain.MapResult;
import com.puree.hospital.monitor.datashare.constant.HospitalSignConstant;
import com.puree.hospital.monitor.datashare.constant.UrlConstant;
import com.puree.hospital.monitor.datashare.domain.vo.BusDoctorVO;
import com.puree.hospital.monitor.datashare.domain.vo.DoctorResult;
import com.puree.hospital.monitor.datashare.domain.vo.DoctorStatusResult;
import com.puree.hospital.monitor.datashare.service.IBusBizDepartmentService;
import com.puree.hospital.monitor.datashare.service.IBusDoctorService;
import com.puree.hospital.monitor.datashare.util.DesUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.puree.hospital.monitor.datashare.util.SignCheck.signCheck;
import static com.puree.hospital.monitor.datashare.util.UrlCheck.urlCheck;

/**
 * <AUTHOR>
 * @date 2023/1/16 15:50
 */
@RestController
@RequestMapping("/doctor")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BusDoctorController extends BaseController {
    private final IBusDoctorService busDoctorService;
    private final IBusBizDepartmentService busBizDepartmentService;
    @Value("${aliyun.oss.fileAddressPrefix}")
    private String fileAddressPrefix;

    /**
     * 查询医生信息
     * @param key 时间戳
     * @param token key（导引平台提供）+“_”+salt（医院端定义服务器端密码）值
     * @return
     */
    @GetMapping("/acquireDoctor")
    public MapResult queryDoctor(String key, String token, String hospitalCode) {
        logger.info("导引平台查询医生入参1={}，入参2={}", key, token);
        MapResult ajaxResult = new MapResult();
        try {
            signCheck(key, token);
            List<BusDoctorVO> doctorVos = busDoctorService.queryDoctor(hospitalCode);
            List<DoctorResult> resultList = OrikaUtils.converts(doctorVos, DoctorResult.class);
            resultList.forEach(r -> {
                r.setDoctorWxMpPath("");
                String doctorUrl = urlCheck(hospitalCode);
                r.setDoctorUrl(doctorUrl + UrlConstant.DOCTOR_URL + "?id=" + r.getDoctorId());
                logger.info("医生链接地址={}", doctorUrl + UrlConstant.DOCTOR_URL + "?id=" + r.getDoctorId());
                r.setDoctorAvatar(fileAddressPrefix + r.getDoctorAvatar());
                // 查询医生科室信息
                String deptId = busBizDepartmentService.queryDrDeptId(r.getDoctorId(), hospitalCode);
                r.setDeptId(deptId);
            });
            String jsonString = JSONObject.toJSONString(resultList);
            logger.info("医生信息={}", jsonString);
            // 使用DES加密
            String data = DesUtils.Encrypt(jsonString, HospitalSignConstant.SECRET_KEY);
            ajaxResult.put("status", 1);
            ajaxResult.put("message", "医生信息查询成功");
            ajaxResult.put("data", data);
        } catch (Exception e) {
            ajaxResult.put("status", 0);
            ajaxResult.put("message", "医生信息查询失败");
            ajaxResult.put("data", e.getMessage());
        }
        return ajaxResult;
    }

    /**
     * 查询医生状态信息
     * @param key 时间戳
     * @param token key（导引平台提供）+“_”+salt（医院端定义服务器端密码）值
     * @return
     */
    @GetMapping("/acquireDoctorState")
    public MapResult queryDoctorStatus(String key, String token, String hospitalCode) {
        logger.info("导引平台查询医生状态入参1={}，入参2={}", key, token);
        MapResult ajaxResult = new MapResult();
        try {
            signCheck(key, token);
            List<BusDoctorVO> doctorVos = busDoctorService.queryDoctor(hospitalCode);
            doctorVos.forEach(r -> {
                if (CodeEnum.NO.getCode().equals(r.getDoctorStatus())) {
                    r.setDoctorStatus("离线");
                } else {
                    r.setDoctorStatus("在线");
                }
            });
            List<DoctorStatusResult> resultList = OrikaUtils.converts(doctorVos, DoctorStatusResult.class);
            String jsonString = JSONObject.toJSONString(resultList);
            // 使用DES加密
            String data = DesUtils.Encrypt(jsonString, HospitalSignConstant.SECRET_KEY);
            ajaxResult.put("status", 1);
            ajaxResult.put("message", "医生状态信息查询成功");
            ajaxResult.put("data", data);
        } catch (Exception e) {
            ajaxResult.put("status", 0);
            ajaxResult.put("message", "医生状态信息查询失败");
            ajaxResult.put("data", e.getMessage());
        }
        return ajaxResult;
    }

    /**
     * 推送医生状态
     * @param hospitalId
     * @return
     */
    @GetMapping("/push/status")
    public AjaxResult pushDoctorStatus(Long hospitalId, Long doctorId, Integer onlineStatus) throws Exception {
        // 封装参数
        Map<String, Object> paramsMap = new HashMap<>();
        long timeMillis = System.currentTimeMillis();
        JSONArray jsonArray = new JSONArray();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("doctorId", doctorId + "");
        jsonObject.put("doctorStatus", YesNoEnum.NO.getCode().equals(onlineStatus) ? "离线" : "在线");
        jsonArray.add(jsonObject);
        String jsonString = jsonArray.toJSONString();
        // 使用DES加密
        String data = DesUtils.Encrypt(jsonString, HospitalSignConstant.SECRET_KEY);
        paramsMap.put("data", data);
        // 生成签名
        String signStr = "timestamp=" + timeMillis + "&token=" + HospitalSignConstant.SALT;
        String sign = MD5Util.md5Encrypt32Lower(signStr);
        paramsMap.put("sign", sign);
        paramsMap.put("timestamp", timeMillis);
        String params = JSONObject.toJSONString(paramsMap);
        JSONObject resultJson = HttpUtil.doPost("http://112.94.67.44:20954/hospitals/hospital/pushDoctorStatus/" + hospitalId, params);
        logger.info("推送医生状态结果={}", resultJson);
        return AjaxResult.success();
    }

}
