package com.puree.hospital.monitor.guangdong.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@TableName("gd_t_ms_treatment_order")
public class MedicalPrescription {
    /** 处方编号 */
    private String cfbh;
    /** 处方开立科室编 */
    private String cfklksbm;
    /** 处方开立科室名 */
    private String cfklksmc;
    /** 处方开立日期 */
    private Date cfklsj;
    /** 处方开立医师工 */
    private String cfklysgh;
    /** 处方开立医师签 */
    private String cfklysqm;
    /** 处方明细 ID */
    private String cfmxid;
    /** 处方明细名称 */
    private String cfmxmc;
    /** 处方有效天数(1天) */
    private Integer cfyxts;
    /** 出生日期 */
    private Date csrq;
    /** 撤销标志 */
    private String cxbz;
    /** 单价 */
    private BigDecimal dj;
    /** 服务网点代码 */
    private String fwwddm;
    /** 机构标识 */
    private String jgdm;
    /** 就诊日期时间 */
    private Date jzrqsj;
    /** 卡号 */
    private String kh;
    /** 卡类型 */
    private String klx;
    /** 门诊号 */
    private String mzh;
    /** 年龄（岁） */
    private Integer nls;
    /** 年龄月 */
    private String nly;
    /** 数据生成日期时 */
    private Date sjscsj;
    /** 性别代码 */
    private String xbdm;
    /** 姓名 */
    private String xm;
    /** 药品处方属性 */
    private String ypcfsx;
    /** 医嘱项目类型代 */
    private String yzxmlxdm;
    /** 总金额 */
    private BigDecimal zje;
    /** 药品ID */
    private String ypid;
    /** 药物名称 */
    private String ywmc;
    /** 药品规格 */
    private String ypgg;
    private String ywjxdm;
    private BigDecimal ywsycjl;
    private String ywsyjldw;
    private String ywsypcdm;
    private String ywsypcmc;
    private String yytjdm;
    private String yytjmc;
    private BigDecimal ywsyzjl;
    private Date createTime;
    private Long hospitalId;
    /**
     * 医嘱说明
     */
    @TableField("YZSM")
    private String yzsm;
    /**
     * 发药计量
     */
    @TableField("FYJL")
    private BigDecimal fyjl;
    /**
     * 发药计量单位-10位
     */
    @TableField("FYJLDW")
    private String fyjldw;

    @TableField(exist = false)
    private long drugsId;
    @TableField(exist = false)
    private Long familyId;
    @TableField(exist = false)
    private String prescriptionType;
    @TableField(exist = false)
    private List<MedicalPrescriptionDetail> list;
}
