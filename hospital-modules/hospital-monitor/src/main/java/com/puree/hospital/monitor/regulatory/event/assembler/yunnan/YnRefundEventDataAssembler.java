package com.puree.hospital.monitor.regulatory.event.assembler.yunnan;

import cn.hutool.core.lang.generator.SnowflakeGenerator;
import com.puree.hospital.monitor.api.event.constant.RegulatoryAreaConstant;
import com.puree.hospital.monitor.api.event.constant.RegulatoryEventConstant;
import com.puree.hospital.monitor.api.event.regulatory.report.yunnan.YnChargeOrRefund;
import com.puree.hospital.monitor.common.domain.BusDrugsOrder;
import com.puree.hospital.monitor.regulatory.event.assembler.EventDataAssembler;
import com.puree.hospital.pay.api.model.BusPayOrder;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <p>
 * 云南退费信息组装
 * </p>
 *
 * <AUTHOR>
 * @date 2024/6/25 14:51
 */
@Component(RegulatoryAreaConstant.YUN_NAN + RegulatoryEventConstant.REFUND + EventDataAssembler.SUFFIX)
public class YnRefundEventDataAssembler extends YnBaseChargeOrRefundEventDataAssembler {

    private static final SnowflakeGenerator GENERATOR = new SnowflakeGenerator(0, 0);

    @Override
    protected String getEventType() {
        return RegulatoryEventConstant.REFUND;
    }

    @Override
    protected String getChargeRefundCode() {
        return "2";
    }

    @Override
    protected String getAccountNo(BusPayOrder busPayOrder, YnChargeOrRefund regulatory) {
        //todo 暂时直接 用雪花算法生产，后面对接退款记录
        return "REF" + GENERATOR.next()  + regulatory.getHospitalId();
    }

    @Override
    protected String getOutTradeNo(BusPayOrder busPayOrder, YnChargeOrRefund regulatory) {
        //todo 暂时直接 用雪花算法生产，后面对接退款记录
        return "TR" + GENERATOR.next()  + regulatory.getHospitalId();
    }

    @Override
    protected String getOriginalAccountNo(BusPayOrder busPayOrder) {
        return busPayOrder.getOutTradeNo();
    }

    @Override
    protected List<BusDrugsOrder> getDrugsOrderList(List<Long> subOrderIds) {
        //todo 暂时用全部的子订单进行退款， 后续需要对接售后单和退款记录
        return busDrugsOrderRepository.listByIds(subOrderIds);
    }


}
