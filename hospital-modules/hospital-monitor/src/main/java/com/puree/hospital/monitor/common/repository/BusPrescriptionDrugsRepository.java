package com.puree.hospital.monitor.common.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.puree.hospital.monitor.common.domain.BusPrescriptionDrugs;
import com.puree.hospital.monitor.common.mapper.BusPrescriptionDrugsMapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>
 * 处方药品 Repository
 * </p>
 *
 * <AUTHOR>
 * @date 2024/6/26 15:30
 */
@Repository
public class BusPrescriptionDrugsRepository extends ServiceImpl<BusPrescriptionDrugsMapper, BusPrescriptionDrugs> {

    /**
     * 根据处方ID查询处方药品明细信息
     *
     * @param prescriptionId 处方ID
     * @return 处方药品明细信息
     */
    public List<BusPrescriptionDrugs> getByPrescriptionId(Long prescriptionId) {
        LambdaQueryWrapper<BusPrescriptionDrugs> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BusPrescriptionDrugs::getPrescriptionId, prescriptionId);
        return list(wrapper);
    }

}
