<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE  mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.puree.hospital.monitor.common.mapper.BusDoctorDepartmentMapper">

    <resultMap type="com.puree.hospital.monitor.common.domain.BusDoctorDepartment" id="busDoctorDepartmentMap">
        <result property="id" column="id"/>
        <result property="departmentId" column="department_id"/>
        <result property="hospitalId" column="hospital_id"/>
        <result property="doctorId" column="doctor_id"/>
        <result property="departmentName" column="department_name"/>
        <result property="standardDepartmentName" column="standard_department_name"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
    </resultMap>

    <sql id="Base_Column_List">
        bt.id,
        bt.department_id,
        bt.hospital_id,
        bt.doctor_id,
        bt.create_time,
        bt.update_time,
        bt.create_by,
        bt.update_by,
        bd.department_name,
        bd.department_name as standard_department_name
    </sql>

    <select id="selectList" parameterType="com.puree.hospital.monitor.common.domain.BusDoctorDepartment"
            resultMap="busDoctorDepartmentMap">
        select
        <include refid="Base_Column_List"/>
        from bus_doctor_department bt
        LEFT JOIN bus_biz_department bbd ON bbd.id = bt.department_id
        LEFT JOIN bus_associate_department bad ON bad.biz_department_id = bbd.id
        LEFT JOIN bus_department bd ON bad.department_id = bd.id
        <where>
            <trim prefixOverrides="and">
                <if test="hospitalId != null">
                    and bt.hospital_id = #{hospitalId}
                </if>
                <if test="doctorId != null">
                    and bt.doctor_id=#{doctorId}
                </if>
                <if test="departmentId != null">
                    and bbd.department_id=#{departmentId}
                </if>
                <if test="departmentName != null and '' != departmentName">
                    and bbd.department_name =#{departmentName}
                </if>
            </trim>
        </where>
    </select>

</mapper>