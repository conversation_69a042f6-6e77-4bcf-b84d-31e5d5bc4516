<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.puree.hospital.monitor.common.mapper.BusDoctorMapper">

    <select id="queryDoctor" parameterType="String" resultType="com.puree.hospital.monitor.datashare.domain.vo.BusDoctorVO">
        SELECT
            t3.hospital_code hos_id,
            t1.id doctor_id,
            t1.full_name doctor_name,
        IF
            ( t1.sex = 1, 1, 2 ) doctor_gender,
            t1.title,
            t1.photo doctor_avatar,
            t1.be_good_at doctor_good_at,
            t1.introduce doctor_intro,
            t2.online_status doctor_status
        FROM
            bus_doctor t1
            LEFT JOIN bus_doctor_hospital t2 ON t1.id = t2.doctor_id
            LEFT JOIN bus_hospital t3 ON t2.hospital_id = t3.id
        WHERE
            t3.hospital_code = #{hospitalCode}
            AND t2.is_this_court != 2
            AND t2.`status` = 1
            AND t2.`is_authentication` = 3
            AND t1.id NOT IN (23,42,67)
    </select>

</mapper>