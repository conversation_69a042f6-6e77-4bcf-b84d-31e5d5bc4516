<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.puree.hospital.monitor.common.mapper.BusHospitalMapper">

    <select id="queryHospital" resultType="com.puree.hospital.monitor.datashare.domain.vo.BusHospitalVO">
        SELECT
            t1.id,
            CONCAT( sp.`name`, sc.`name`, sa.`name`, t1.detail_address ) hos_address,
            t1.hospital_code hosId,
            t1.address_coordinate location,
            t1.hospital_name hos_name,
        IF
            ( t1.medical_insurance = 0, 'FALSE', 'TRUE' ) yb_supported,
            t1.hospital_photo
        FROM
            bus_hospital t1
            LEFT JOIN sys_province sp ON t1.province_code = sp.`code`
            LEFT JOIN sys_city sc ON t1.city_code = sc.`code`
            LEFT JOIN sys_area sa ON t1.area_code = sa.`code`
        WHERE
            t1.hospital_code = #{hospitalCode}
    </select>

</mapper>