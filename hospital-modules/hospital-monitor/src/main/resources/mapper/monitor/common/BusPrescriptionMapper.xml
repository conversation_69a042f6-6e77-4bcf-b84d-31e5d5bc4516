<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.puree.hospital.monitor.common.mapper.BusPrescriptionMapper">

    <select id="getPrescriptionByRecordNo"
            resultType="com.puree.hospital.monitor.common.domain.BusPrescription">
        SELECT p.*
        FROM bus_prescription p
                 INNER JOIN bus_patient_family f ON f.id = p.family_id
                 INNER JOIN bus_doctor_hospital h ON h.doctor_id = p.doctor_id AND h.hospital_id = p.hospital_id
        WHERE p.hospital_id = #{prescription.hospitalId}
            and f.outpatient_number = #{prescription.recordNo}
          <if test="prescription.familyName != null and prescription.familyName != ''">
            and p.family_name like CONCAT('%', #{prescription.familyName} , '%')
          </if>
          <if test="prescription.doctorJobNumber != null and prescription.doctorJobNumber != ''">
            and h.job_number = #{prescription.doctorJobNumber}
          </if>
            and f.id_number is not null
            and f.id_number != ''
        ORDER BY p.create_time DESC
    </select>

</mapper>