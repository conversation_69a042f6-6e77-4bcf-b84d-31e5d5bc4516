<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE  mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.puree.hospital.monitor.common.mapper.BusDoctorPatientGroupMapper">


    <select id="getDistinctGroupIds" resultType="java.lang.Long">
        SELECT DISTINCT id FROM bus_doctor_patient_group
        <where>
            <if test="doctorId != null">
                AND doctor_id = #{doctorId}
            </if>
            <if test="hospitalId != null">
                AND hospital_id = #{hospitalId}
            </if>
            <if test="patientId != null">
                AND patient_id = #{patientId}
            </if>
            <if test="familyId != null">
                AND family_id = #{familyId}
            </if>
            <if test="departmentId != null">
                AND department_id = #{departmentId}
            </if>
        </where>
    </select>
</mapper>