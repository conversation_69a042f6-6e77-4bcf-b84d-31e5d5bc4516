<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.puree.hospital.monitor.common.mapper.BusBizDepartmentMapper">

    <select id="queryDepartment" parameterType="String" resultType="com.puree.hospital.monitor.datashare.domain.vo.BusBizDepartmentVO">
        SELECT
            t4.hospital_code hos_id,
            t1.id dept_id,
            t1.department_name dept_name,
        IF
            ( t1.parent_id = 0, 1, 2 ) `level`,
        IF
            ( t1.parent_id = 0, '', t1.parent_id ) parent_dept_id,
            t3.department_number std_dept_id
        FROM
            bus_biz_department t1
            LEFT JOIN bus_associate_department t2 ON t1.id = t2.biz_department_id
            LEFT JOIN bus_department t3 ON t2.department_id = t3.id
            LEFT JOIN bus_hospital t4 ON t1.hospital_id = t4.id
        WHERE
            t4.hospital_code = #{hospitalCode}
    </select>

    <select id="queryDrDeptId" parameterType="java.lang.String" resultType="java.lang.String">
        SELECT
            t1.department_id
        FROM
            bus_doctor_department t1
            LEFT JOIN bus_hospital t2 ON t1.hospital_id = t2.id
        WHERE
            t2.hospital_code = #{hospitalCode}
            AND t1.doctor_id = #{doctorId}
            LIMIT 1
    </select>

</mapper>