<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.puree.hospital.monitor.guangdong.mapper.MedicalRecordMapper">
    <select id="selectSyncDataList" resultType="com.puree.hospital.monitor.guangdong.domain.MedicalRecord">
        SELECT p.department_id   AS KSBM,
               p.department_name AS KSMC,
               p.family_name     AS XM,
               p.family_sex      AS XBDM,
               f.date_of_birth   AS CSRQ,
               p.allergic_history,
               p.create_time     AS JZRQSJ,
               p.create_time     AS SJSCSJ,
               h.job_number      AS YZYSGH,
               f.outpatient_number as MZH,
               p.doctor_name     AS YZYSQM,
               f.id as familyId,
               p.clinical_diagnosis as diagnosis
        FROM bus_prescription p
                 INNER JOIN bus_patient_family f ON f.id = p.family_id
                 INNER JOIN bus_doctor_hospital h ON h.doctor_id = p.doctor_id AND h.hospital_id = p.hospital_id
        WHERE p.hospital_id = #{hospitalId} and f.id_number is not null and f.id_number != ''
         and p.create_time &gt;= DATE_SUB(CURDATE(),INTERVAL 5 DAY)
         and p.create_time &lt; date_add(curdate(),interval +1 day)
    </select>

</mapper> 