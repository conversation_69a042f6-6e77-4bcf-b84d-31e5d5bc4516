<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.puree.hospital.monitor.guangdong.mapper.StaffMapper">
    <select id="selectSyncDataList" resultType="com.puree.hospital.monitor.guangdong.domain.Staff">
        select
        -- 医护人员工号
        d.doctor_number as YHRYGH,
        -- 医护人员姓名
        d.full_name as YHRYXM,
        -- 性别
        d.sex as XB,
        -- 出生日期 YYYY-MM-DD (根据身份证算)
        -- d.id_card_no as CSRQ,
        -- 身份证号
        d.id_card_no as SFZH,
        -- 身份证件类别代码 写死
        '01' as ZJLBDM,
        -- 科室代码
        ad.biz_department_id as KSDM,
        -- 专业技术职务代码
--         sdd.dict_label as ZYJSZWDM,
        d.title,
        -- 专业技术职务类别代码
        999 as ZYJSZWLBDM,
        -- 资质类别名称
        d.qualifications_category as ZZLBMC,
        -- 资格证书编号
        d.qualification_certificate_number as ZGZSBH,
        -- 资格获得时间
        d.qualification_certificate_time as ZGHDSJ,
        -- 执业证书编码
        d.practice_certificate_number as ZYZSBM,
        -- 发证日期
        d.practice_certificate_time as FZRQ,
        -- 执业地点(使用医院名称)
        h.hospital_name as ZYDD,
        -- 执业范围（未知）
        999 as ZYFW,
        -- 主要执业医疗机构代码(取机构表)
        999 as ZYZYYLJGDM,
        -- 主要执业医院名称
        h.hospital_name as ZYZYYYMC,
        -- 全科医生标志
        'F' as QKYSBZ,
        -- 手机号码
        d.phone_number as SJHM,
        -- 参与工作时间
        d.work_time as CJGZRQ,
        -- 注册日期时间
        d.create_time as ZCSJ,
        -- 个人照片存放地址
        d.photo as GRZPCFDZ,
        -- 资格证存放地址
        d.qualification_certificate as ZGZCFDZ,
        -- 执业证存放地址
        d.practice_certificate as ZYZCFDZ,
        -- 数据生成日期时间
        d.create_time as SJSCSJ
         from bus_doctor_hospital dh
         INNER JOIN bus_hospital h on h.id = dh.hospital_id
        inner join bus_doctor d on d.id = dh.doctor_id
        inner join bus_doctor_department dd on dd.doctor_id = d.id
        inner join bus_associate_department ad on ad.biz_department_id = dd.department_id
        inner join bus_department bd on ad.department_id = bd.id
--         inner join sys_dict_data sdd on sdd.dict_code = d.title
        where
        dh.hospital_id = #{hospitalId}
        and d.practice_certificate_number is not null
        and d.create_time &gt;= DATE_SUB(CURDATE(),INTERVAL 5 DAY)
        and d.create_time &lt;= date_add(curdate(),interval +1 day)
    </select>

</mapper> 