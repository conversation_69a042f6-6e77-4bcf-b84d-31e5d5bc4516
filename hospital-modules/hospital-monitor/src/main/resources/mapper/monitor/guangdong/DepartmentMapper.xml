<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.puree.hospital.monitor.guangdong.mapper.DepartmentMapper">
    <select id="selectSyncDataList" resultType="com.puree.hospital.monitor.guangdong.domain.Department">
        select
        bd.id as ksbm,
        bd.department_name as ksmc,
        dep.department_number as bzksdm,
        bd.create_time as sjscsj,
        bd.create_time as createTime
        from `bus_biz_department` bd
        INNER JOIN `bus_department` dep on bd.`department_name`  = dep.`department_name`
        where bd.hospital_id = #{hospitalId} and bd.`parent_id` != 0
          and bd.create_time &gt;= DATE_SUB(CURDATE(),INTERVAL 5 DAY)
          and bd.create_time &lt;= date_add(curdate(),interval +1 day)
    </select>

</mapper> 