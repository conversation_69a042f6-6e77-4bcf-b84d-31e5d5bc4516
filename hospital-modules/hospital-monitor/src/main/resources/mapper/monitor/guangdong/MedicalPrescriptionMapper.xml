<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.puree.hospital.monitor.guangdong.mapper.MedicalPrescriptionMapper">
    <!-- 中医诊断结果map -->
    <resultMap id="MedicalPrescriptionResultMap" type="com.puree.hospital.monitor.guangdong.domain.MedicalPrescription">
        <result property="cfbh" column="CFBH"/>
        <result property="cfklsj" column="CFKLSJ"/>
        <result property="cfklksbm" column="CFKLKSBM"/>
        <result property="cfklksmc" column="CFKLKSMC"/>
        <result property="mzh" column="MZH"/>
        <result property="xm" column="XM"/>
        <result property="xbdm" column="XBDM"/>
        <result property="csrq" column="CSRQ"/>
        <result property="nls" column="NLS"/>
        <result property="jzrqsj" column="JZRQSJ"/>
        <result property="sjscsj" column="SJSCSJ"/>
        <result property="cfklysgh" column="CFKLYSGH"/>
        <result property="cfklysqm" column="CFKLYSQM"/>
        <result property="familyId" column="familyId"/>
        <result property="yzsm" column="YZSM"/>
        <result property="prescriptionType" column="prescription_type"/>
        <collection property="list" ofType="com.puree.hospital.monitor.guangdong.domain.MedicalPrescriptionDetail">
            <id property="detailId" column="detailId"/>
            <result property="yid" column="yid"/>
            <result property="drugsName" column="drugs_name"/>
            <result property="sellingPrice" column="selling_price"/>
            <result property="quantity" column="quantity"/>
            <result property="standardCommonName" column="standard_common_name"/>
            <result property="drugsSpecification" column="drugs_specification"/>
            <result property="medicationFrequency" column="medication_frequency"/>
            <result property="singleDose" column="single_dose"/>
            <result property="medicationDays" column="medication_days"/>
            <result property="unit" column="unit"/>
            <result property="drugsUsageValue" column="drugs_usage_value"/>
            <result property="usageCode" column="usageCode"/>
            <result property="usageName" column="usageName"/>
            <result property="weight" column="weight"/>
        </collection>
    </resultMap>
    <select id="selectSyncDataList" resultMap="MedicalPrescriptionResultMap">
        SELECT p.prescription_number AS CFBH,
               pd.id                 AS detailId,
               pd.standard_common_name,
               pd.drugs_name,
               pd.selling_price,
               pd.quantity,
               pd.drugs_specification,
               pd.medication_frequency,
               pd.single_dose,
               pd.medication_days,
               pd.unit,
               pd.drugs_usage_value,
               pd.weight,
               ddu.code              as usageCode,
               ddu.name              as usageName,
               d.ypid               as yid,
               p.create_time         AS CFKLSJ,
               p.department_id       AS CFKLKSBM,
               p.department_name     AS CFKLKSMC,
               f.outpatient_number   AS MZH,
               p.family_name         AS XM,
               p.family_sex          AS XBDM,
               f.date_of_birth       AS CSRQ,
               p.create_time         AS JZRQSJ,
               p.create_time         AS SJSCSJ,
               h.job_number          AS CFKLYSGH,
               p.doctor_name         AS CFKLYSQM,
               f.id as familyId,
               p.prescription_type,
               p.remark as YZSM
        FROM bus_prescription p
                 inner JOIN bus_prescription_drugs pd ON pd.prescription_id = p.id
            inner JOIN bus_patient_family f ON f.id = p.family_id
            inner JOIN bus_doctor_hospital h ON h.doctor_id = p.doctor_id AND h.hospital_id = p.hospital_id
            inner join bus_drugs d on d.id = pd.drugs_id
            inner join bus_dict_drugs_usage ddu on ddu.name = pd.drugs_usage_value
        WHERE p.hospital_id = #{hospitalId} and f.id_number is not null and f.id_number != ''
          and p.create_time &gt;= DATE_SUB(CURDATE(),INTERVAL 5 DAY)
          and p.create_time &lt;= date_add(curdate(),interval +1 day)
    </select>

</mapper> 